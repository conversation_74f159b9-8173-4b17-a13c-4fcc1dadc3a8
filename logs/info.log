2025-08-21 22:10:48.987|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStarting:55|Starting GRfpApiApplication on LAPTOP-BVT1ALEM with PID 21348 (D:\RFP\grfp-project\grfp-api\target\classes started by <PERSON>ham<PERSON>od in D:\RFP\grfp-project)  [] ------
2025-08-21 22:10:48.992|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStartupProfileInfo:652|The following profiles are active: dev  [] ------
2025-08-21 22:10:51.019|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:249|Multiple Spring Data modules found, entering strict repository configuration mode!  [] ------
2025-08-21 22:10:51.024|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:127|Bootstrapping Spring Data Redis repositories in DEFAULT mode.  [] ------
2025-08-21 22:10:51.094|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:187|Finished Spring Data repository scanning in 43ms. Found 0 Redis repository interfaces.  [] ------
2025-08-21 22:10:52.518|main| INFO|o.s.boot.web.embedded.tomcat.TomcatWebServer.initialize:108|Tomcat initialized with port(s): 6888 (http)  [] ------
2025-08-21 22:10:52.535|main| INFO|org.apache.catalina.core.StandardService.log:173|Starting service [Tomcat]  [] ------
2025-08-21 22:10:52.535|main| INFO|org.apache.catalina.core.StandardEngine.log:173|Starting Servlet engine: [Apache Tomcat/9.0.45]  [] ------
2025-08-21 22:10:52.709|main| INFO|o.a.c.c.C.[Tomcat].[localhost].[/grfp-api].log:173|Initializing Spring embedded WebApplicationContext  [] ------
2025-08-21 22:10:52.709|main| INFO|o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:285|Root WebApplicationContext: initialization completed in 3626 ms  [] ------
2025-08-21 22:10:53.040|main| INFO|org.redisson.Version.logVersion:41|Redisson 3.16.0  [] ------
2025-08-21 22:10:55.144|redisson-netty-2-27| INFO|o.r.connection.pool.MasterPubSubConnectionPool.lambda$run$0:166|1 connections initialized for *************/*************:6379  [] ------
2025-08-21 22:10:57.321|redisson-netty-2-19| INFO|org.redisson.connection.pool.MasterConnectionPool.lambda$run$0:166|24 connections initialized for *************/*************:6379  [] ------
2025-08-21 22:11:00.993|main| INFO|com.fangcang.grfp.api.config.OssAutoConfiguration.ossClient:34|---------------Init OssClient ----------------  [] ------
2025-08-21 22:11:02.103|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'queryBidHotelInfoExecutor'  [] ------
2025-08-21 22:11:02.563|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'hotelHexagonGenerateExecutor'  [] ------
2025-08-21 22:11:03.670|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'auditLogExecutor'  [] ------
2025-08-21 22:11:04.169|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'generalExecutor'  [] ------
2025-08-21 22:11:04.170|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'syncGlinkExecutor'  [] ------
2025-08-21 22:11:04.255|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'rfpCommonExecutor'  [] ------
2025-08-21 22:11:05.413|main| INFO|o.s.boot.web.embedded.tomcat.TomcatWebServer.start:220|Tomcat started on port(s): 6888 (http) with context path '/grfp-api'  [] ------
2025-08-21 22:11:09.641|main| INFO|o.s.s.a.ScheduledAnnotationBeanPostProcessor.finishRegistration:309|No TaskScheduler/ScheduledExecutorService bean found for scheduled processing  [] ------
2025-08-21 22:11:10.425|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStarted:61|Started GRfpApiApplication in 22.096 seconds (JVM running for 24.22)  [] ------
2025-08-21 22:11:10.436|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.afterStarted:59|==============================GrfpApi 系统启动完成v1.0.0-20250808===========================================  [] ------
2025-08-21 22:11:10.699|main| INFO|com.zaxxer.hikari.HikariDataSource.getConnection:110|HikariPool-1 - Starting...  [] ------
2025-08-21 22:11:13.248|main| INFO|com.zaxxer.hikari.HikariDataSource.getConnection:123|HikariPool-1 - Start completed.  [] ------
2025-08-21 22:11:15.082|main| INFO|c.f.g.core.cached.impl.CachedHotelBrandServiceImpl.getNameMap:49|HotelBrandNameMap size en-US, 9926  [] ------
2025-08-21 22:11:15.848|main| INFO|c.f.g.core.cached.impl.CachedHotelBrandServiceImpl.getNameMap:49|HotelBrandNameMap size zh-CN, 9926  [] ------
2025-08-21 22:11:16.296|main| INFO|c.f.g.core.cached.impl.CachedHotelGroupServiceImpl.getNameMap:60|HotelGroupNameMap size en-US, 8123  [] ------
2025-08-21 22:11:16.801|main| INFO|c.f.g.core.cached.impl.CachedHotelGroupServiceImpl.getNameMap:60|HotelGroupNameMap size zh-CN, 8123  [] ------
2025-08-21 22:11:31.829|main| INFO|c.f.grfp.core.cached.impl.CachedCityServiceImpl.getNameMap:55|City Map size en-US, 309420  [] ------
2025-08-21 22:11:46.443|main| INFO|c.f.grfp.core.cached.impl.CachedCityServiceImpl.getNameMap:55|City Map size zh-CN, 309420  [] ------
2025-08-21 22:32:36.746|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStarting:55|Starting GRfpApiApplication on LAPTOP-BVT1ALEM with PID 11564 (D:\RFP\grfp-project\grfp-api\target\classes started by ShamGod in D:\RFP\grfp-project)  [] ------
2025-08-21 22:32:36.747|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStartupProfileInfo:652|The following profiles are active: dev  [] ------
2025-08-21 22:32:38.233|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:249|Multiple Spring Data modules found, entering strict repository configuration mode!  [] ------
2025-08-21 22:32:38.236|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:127|Bootstrapping Spring Data Redis repositories in DEFAULT mode.  [] ------
2025-08-21 22:32:38.287|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:187|Finished Spring Data repository scanning in 34ms. Found 0 Redis repository interfaces.  [] ------
2025-08-21 22:32:39.528|main| INFO|o.s.boot.web.embedded.tomcat.TomcatWebServer.initialize:108|Tomcat initialized with port(s): 6888 (http)  [] ------
2025-08-21 22:32:39.544|main| INFO|org.apache.catalina.core.StandardService.log:173|Starting service [Tomcat]  [] ------
2025-08-21 22:32:39.544|main| INFO|org.apache.catalina.core.StandardEngine.log:173|Starting Servlet engine: [Apache Tomcat/9.0.45]  [] ------
2025-08-21 22:32:39.711|main| INFO|o.a.c.c.C.[Tomcat].[localhost].[/grfp-api].log:173|Initializing Spring embedded WebApplicationContext  [] ------
2025-08-21 22:32:39.712|main| INFO|o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:285|Root WebApplicationContext: initialization completed in 2898 ms  [] ------
2025-08-21 22:32:40.045|main| INFO|org.redisson.Version.logVersion:41|Redisson 3.16.0  [] ------
2025-08-21 22:32:42.032|redisson-netty-2-21| INFO|o.r.connection.pool.MasterPubSubConnectionPool.lambda$run$0:166|1 connections initialized for *************/*************:6379  [] ------
2025-08-21 22:32:43.315|redisson-netty-2-19| INFO|org.redisson.connection.pool.MasterConnectionPool.lambda$run$0:166|24 connections initialized for *************/*************:6379  [] ------
2025-08-21 22:32:46.831|main| INFO|com.fangcang.grfp.api.config.OssAutoConfiguration.ossClient:34|---------------Init OssClient ----------------  [] ------
2025-08-21 22:32:47.856|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'queryBidHotelInfoExecutor'  [] ------
2025-08-21 22:32:48.275|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'hotelHexagonGenerateExecutor'  [] ------
2025-08-21 22:32:49.384|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'auditLogExecutor'  [] ------
2025-08-21 22:32:49.866|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'generalExecutor'  [] ------
2025-08-21 22:32:49.867|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'syncGlinkExecutor'  [] ------
2025-08-21 22:32:49.947|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'rfpCommonExecutor'  [] ------
2025-08-21 22:32:51.052|main| INFO|o.s.boot.web.embedded.tomcat.TomcatWebServer.start:220|Tomcat started on port(s): 6888 (http) with context path '/grfp-api'  [] ------
2025-08-21 22:42:31.028|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStarting:55|Starting GRfpApiApplication on LAPTOP-BVT1ALEM with PID 9912 (D:\RFP\grfp-project\grfp-api\target\classes started by ShamGod in D:\RFP\grfp-project)  [] ------
2025-08-21 22:42:31.030|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStartupProfileInfo:652|The following profiles are active: dev  [] ------
2025-08-21 22:42:32.493|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:249|Multiple Spring Data modules found, entering strict repository configuration mode!  [] ------
2025-08-21 22:42:32.496|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:127|Bootstrapping Spring Data Redis repositories in DEFAULT mode.  [] ------
2025-08-21 22:42:32.544|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:187|Finished Spring Data repository scanning in 33ms. Found 0 Redis repository interfaces.  [] ------
2025-08-21 22:42:33.707|main| INFO|o.s.boot.web.embedded.tomcat.TomcatWebServer.initialize:108|Tomcat initialized with port(s): 6888 (http)  [] ------
2025-08-21 22:42:33.722|main| INFO|org.apache.catalina.core.StandardService.log:173|Starting service [Tomcat]  [] ------
2025-08-21 22:42:33.723|main| INFO|org.apache.catalina.core.StandardEngine.log:173|Starting Servlet engine: [Apache Tomcat/9.0.45]  [] ------
2025-08-21 22:42:33.885|main| INFO|o.a.c.c.C.[Tomcat].[localhost].[/grfp-api].log:173|Initializing Spring embedded WebApplicationContext  [] ------
2025-08-21 22:42:33.885|main| INFO|o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:285|Root WebApplicationContext: initialization completed in 2788 ms  [] ------
2025-08-21 22:42:34.202|main| INFO|org.redisson.Version.logVersion:41|Redisson 3.16.0  [] ------
2025-08-21 22:42:36.249|redisson-netty-2-26| INFO|o.r.connection.pool.MasterPubSubConnectionPool.lambda$run$0:166|1 connections initialized for *************/*************:6379  [] ------
2025-08-21 22:42:37.424|redisson-netty-2-18| INFO|org.redisson.connection.pool.MasterConnectionPool.lambda$run$0:166|24 connections initialized for *************/*************:6379  [] ------
2025-08-21 22:42:40.915|main| INFO|com.fangcang.grfp.api.config.OssAutoConfiguration.ossClient:34|---------------Init OssClient ----------------  [] ------
2025-08-21 22:42:41.867|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'queryBidHotelInfoExecutor'  [] ------
2025-08-21 22:42:42.293|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'hotelHexagonGenerateExecutor'  [] ------
2025-08-21 22:42:43.941|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'auditLogExecutor'  [] ------
2025-08-21 22:42:44.825|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'generalExecutor'  [] ------
2025-08-21 22:42:44.826|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'syncGlinkExecutor'  [] ------
2025-08-21 22:42:45.002|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'rfpCommonExecutor'  [] ------
2025-08-21 22:42:47.475|main| INFO|o.s.boot.web.embedded.tomcat.TomcatWebServer.start:220|Tomcat started on port(s): 6888 (http) with context path '/grfp-api'  [] ------
2025-08-21 22:42:51.590|main| INFO|o.s.s.a.ScheduledAnnotationBeanPostProcessor.finishRegistration:309|No TaskScheduler/ScheduledExecutorService bean found for scheduled processing  [] ------
2025-08-21 22:42:52.502|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStarted:61|Started GRfpApiApplication in 22.091 seconds (JVM running for 23.618)  [] ------
2025-08-21 22:42:52.561|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.afterStarted:59|==============================GrfpApi 系统启动完成v1.0.0-20250808===========================================  [] ------
2025-08-21 22:42:53.419|main| INFO|com.zaxxer.hikari.HikariDataSource.getConnection:110|HikariPool-1 - Starting...  [] ------
2025-08-21 22:42:55.518|main| INFO|com.zaxxer.hikari.HikariDataSource.getConnection:123|HikariPool-1 - Start completed.  [] ------
2025-08-21 22:42:56.693|main| INFO|c.f.g.core.cached.impl.CachedHotelBrandServiceImpl.getNameMap:49|HotelBrandNameMap size en-US, 9926  [] ------
2025-08-21 22:42:57.402|main| INFO|c.f.g.core.cached.impl.CachedHotelBrandServiceImpl.getNameMap:49|HotelBrandNameMap size zh-CN, 9926  [] ------
2025-08-21 22:42:58.055|main| INFO|c.f.g.core.cached.impl.CachedHotelGroupServiceImpl.getNameMap:60|HotelGroupNameMap size en-US, 8123  [] ------
2025-08-21 22:42:58.612|main| INFO|c.f.g.core.cached.impl.CachedHotelGroupServiceImpl.getNameMap:60|HotelGroupNameMap size zh-CN, 8123  [] ------
2025-08-21 22:43:10.949|main| INFO|c.f.grfp.core.cached.impl.CachedCityServiceImpl.getNameMap:55|City Map size en-US, 309420  [] ------
2025-08-21 22:43:25.260|main| INFO|c.f.grfp.core.cached.impl.CachedCityServiceImpl.getNameMap:55|City Map size zh-CN, 309420  [] ------
2025-08-21 22:45:10.729|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStarting:55|Starting GRfpApiApplication on LAPTOP-BVT1ALEM with PID 14648 (D:\RFP\grfp-project\grfp-api\target\classes started by ShamGod in D:\RFP\grfp-project)  [] ------
2025-08-21 22:45:10.731|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStartupProfileInfo:652|The following profiles are active: dev  [] ------
2025-08-21 22:45:12.217|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:249|Multiple Spring Data modules found, entering strict repository configuration mode!  [] ------
2025-08-21 22:45:12.220|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:127|Bootstrapping Spring Data Redis repositories in DEFAULT mode.  [] ------
2025-08-21 22:45:12.271|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:187|Finished Spring Data repository scanning in 34ms. Found 0 Redis repository interfaces.  [] ------
2025-08-21 22:45:13.493|main| INFO|o.s.boot.web.embedded.tomcat.TomcatWebServer.initialize:108|Tomcat initialized with port(s): 6888 (http)  [] ------
2025-08-21 22:45:13.509|main| INFO|org.apache.catalina.core.StandardService.log:173|Starting service [Tomcat]  [] ------
2025-08-21 22:45:13.509|main| INFO|org.apache.catalina.core.StandardEngine.log:173|Starting Servlet engine: [Apache Tomcat/9.0.45]  [] ------
2025-08-21 22:45:13.676|main| INFO|o.a.c.c.C.[Tomcat].[localhost].[/grfp-api].log:173|Initializing Spring embedded WebApplicationContext  [] ------
2025-08-21 22:45:13.677|main| INFO|o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:285|Root WebApplicationContext: initialization completed in 2882 ms  [] ------
2025-08-21 22:45:13.988|main| INFO|org.redisson.Version.logVersion:41|Redisson 3.16.0  [] ------
2025-08-21 22:45:15.861|redisson-netty-2-13| INFO|o.r.connection.pool.MasterPubSubConnectionPool.lambda$run$0:166|1 connections initialized for *************/*************:6379  [] ------
2025-08-21 22:45:17.147|redisson-netty-2-19| INFO|org.redisson.connection.pool.MasterConnectionPool.lambda$run$0:166|24 connections initialized for *************/*************:6379  [] ------
2025-08-21 22:45:20.719|main| INFO|com.fangcang.grfp.api.config.OssAutoConfiguration.ossClient:34|---------------Init OssClient ----------------  [] ------
2025-08-21 22:45:21.765|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'queryBidHotelInfoExecutor'  [] ------
2025-08-21 22:45:22.212|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'hotelHexagonGenerateExecutor'  [] ------
2025-08-21 22:45:23.342|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'auditLogExecutor'  [] ------
2025-08-21 22:45:23.826|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'generalExecutor'  [] ------
2025-08-21 22:45:23.827|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'syncGlinkExecutor'  [] ------
2025-08-21 22:45:23.912|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'rfpCommonExecutor'  [] ------
2025-08-21 22:45:25.197|main| INFO|o.s.boot.web.embedded.tomcat.TomcatWebServer.start:220|Tomcat started on port(s): 6888 (http) with context path '/grfp-api'  [] ------
2025-08-21 22:45:28.441|main| INFO|o.s.s.a.ScheduledAnnotationBeanPostProcessor.finishRegistration:309|No TaskScheduler/ScheduledExecutorService bean found for scheduled processing  [] ------
2025-08-21 22:45:29.141|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStarted:61|Started GRfpApiApplication in 18.996 seconds (JVM running for 20.373)  [] ------
2025-08-21 22:45:29.153|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.afterStarted:59|==============================GrfpApi 系统启动完成v1.0.0-20250808===========================================  [] ------
2025-08-21 22:45:29.388|main| INFO|com.zaxxer.hikari.HikariDataSource.getConnection:110|HikariPool-1 - Starting...  [] ------
2025-08-21 22:45:31.010|main| INFO|com.zaxxer.hikari.HikariDataSource.getConnection:123|HikariPool-1 - Start completed.  [] ------
2025-08-21 22:45:33.178|main| INFO|c.f.g.core.cached.impl.CachedHotelBrandServiceImpl.getNameMap:49|HotelBrandNameMap size en-US, 9926  [] ------
2025-08-21 22:45:34.497|main| INFO|c.f.g.core.cached.impl.CachedHotelBrandServiceImpl.getNameMap:49|HotelBrandNameMap size zh-CN, 9926  [] ------
2025-08-21 22:45:35.189|main| INFO|c.f.g.core.cached.impl.CachedHotelGroupServiceImpl.getNameMap:60|HotelGroupNameMap size en-US, 8123  [] ------
2025-08-21 22:45:35.745|main| INFO|c.f.g.core.cached.impl.CachedHotelGroupServiceImpl.getNameMap:60|HotelGroupNameMap size zh-CN, 8123  [] ------
2025-08-21 22:45:52.804|main| INFO|c.f.grfp.core.cached.impl.CachedCityServiceImpl.getNameMap:55|City Map size en-US, 309420  [] ------
2025-08-21 22:46:10.146|main| INFO|c.f.grfp.core.cached.impl.CachedCityServiceImpl.getNameMap:55|City Map size zh-CN, 309420  [] ------
2025-08-21 22:48:09.924|http-nio-6888-exec-1| INFO|o.a.c.c.C.[Tomcat].[localhost].[/grfp-api].log:173|Initializing Spring DispatcherServlet 'dispatcherServlet'  [] ------
2025-08-21 22:48:09.924|http-nio-6888-exec-1| INFO|org.springframework.web.servlet.DispatcherServlet.initServletBean:525|Initializing Servlet 'dispatcherServlet'  [] ------
2025-08-21 22:48:09.941|http-nio-6888-exec-1| INFO|org.springframework.web.servlet.DispatcherServlet.initServletBean:547|Completed initialization in 17 ms  [] ------
2025-08-21 22:48:10.319|http-nio-6888-exec-1| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.beforeRequest:23|Before request [client=127.0.0.1, POST /grfp-api/Api/ProjectContract/QueryBidHotelPriceLevelList]  [68a731aa0e00651900f9fc4e] ------
2025-08-21 22:48:10.945|http-nio-6888-exec-1| INFO|c.fangcang.grfp.core.util.ControllerLoggingUtility.logFinish:101|POST /Api/ProjectContract/QueryBidHotelPriceLevelList FINISH in -1ms returning {"code":"401","data":null,"message":"Session expired, please login again","successful":false}  [68a731aa0e00651900f9fc4e] ------
2025-08-21 22:48:11.036|http-nio-6888-exec-1| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.beforeBodyWrite:54|response body: {"code":"401","data":null,"message":"Session expired, please login again","successful":false}  [] ------
2025-08-21 22:48:11.115|http-nio-6888-exec-1| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.afterRequest:28|After request [client=127.0.0.1, POST /grfp-api/Api/ProjectContract/QueryBidHotelPriceLevelList]  [] ------
2025-08-21 22:48:30.874|http-nio-6888-exec-5| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.beforeRequest:23|Before request [client=127.0.0.1, POST /grfp-api/Api/ProjectContract/QueryBidHotelPriceLevelList]  [68a731be0e00651900f9fc4f] ------
2025-08-21 22:48:31.180|http-nio-6888-exec-5| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.afterBodyRead:37|request body: {"hotelId":134789,"projectIntentHotelId":236}  [68a731be0e00651900f9fc4f] ------
2025-08-21 22:48:33.619|http-nio-6888-exec-5|ERROR|c.f.grfp.api.exception.CustomRestExceptionHandler.handleException:256|controller error java.lang.IllegalArgumentException: Null key returned for cache operation (maybe you are using named params on classes without debug info?) Builder[public com.fangcang.grfp.core.entity.CurrencyExchangeRateEntity com.fangcang.grfp.core.cached.impl.CachedCurrencyServiceImpl.getCurrencyRateInfo(java.lang.String)] caches=[cachedCurrencyService.getCurrencyRateInfo] | key='#currencyCode' | keyGenerator='' | cacheManager='ehCacheCacheManager' | cacheResolver='' | condition='' | unless='#result == null' | sync='false'
	at org.springframework.cache.interceptor.CacheAspectSupport.generateKey(CacheAspectSupport.java:595)
	at org.springframework.cache.interceptor.CacheAspectSupport.findCachedItem(CacheAspectSupport.java:535)
	at org.springframework.cache.interceptor.CacheAspectSupport.execute(CacheAspectSupport.java:402)
	at org.springframework.cache.interceptor.CacheAspectSupport.execute(CacheAspectSupport.java:346)
	at org.springframework.cache.interceptor.CacheInterceptor.invoke(CacheInterceptor.java:61)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:691)
	at com.fangcang.grfp.core.cached.impl.CachedCurrencyServiceImpl$$EnhancerBySpringCGLIB$$ce3f979f.getCurrencyRateInfo(<generated>)
	at com.fangcang.grfp.core.manager.BidMapManager.querBidHotelPriceLevelInfoList(BidMapManager.java:1062)
	at com.fangcang.grfp.core.manager.BidMapManager$$FastClassBySpringCGLIB$$2543fca1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:687)
	at com.fangcang.grfp.core.manager.BidMapManager$$EnhancerBySpringCGLIB$$be546af0.querBidHotelPriceLevelInfoList(<generated>)
	at com.fangcang.grfp.api.controller.project.service.ProjectContractService.querBidHotelPriceLevelInfoList(ProjectContractService.java:1285)
	at com.fangcang.grfp.api.controller.project.service.ProjectContractService$$FastClassBySpringCGLIB$$ebb8acf3.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:687)
	at com.fangcang.grfp.api.controller.project.service.ProjectContractService$$EnhancerBySpringCGLIB$$eff23f26.querBidHotelPriceLevelInfoList(<generated>)
	at com.fangcang.grfp.api.controller.project.ProjectContractController.queryHotelPriceLevelList(ProjectContractController.java:147)
	at com.fangcang.grfp.api.controller.project.ProjectContractController$$FastClassBySpringCGLIB$$83764e97.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:771)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:95)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:691)
	at com.fangcang.grfp.api.controller.project.ProjectContractController$$EnhancerBySpringCGLIB$$8e80f0e3.queryHotelPriceLevelList(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:652)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.session.web.http.SessionRepositoryFilter.doFilterInternal(SessionRepositoryFilter.java:141)
	at org.springframework.session.web.http.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:82)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.AbstractRequestLoggingFilter.doFilterInternal(AbstractRequestLoggingFilter.java:289)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.fangcang.grfp.core.filter.TraceIdFilter.doFilter(TraceIdFilter.java:21)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1707)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
  [68a731be0e00651900f9fc4f] ------
2025-08-21 22:48:33.622|http-nio-6888-exec-5| INFO|c.fangcang.grfp.core.util.ControllerLoggingUtility.logFinish:101|POST /Api/ProjectContract/QueryBidHotelPriceLevelList FINISH in -1ms returning {"code":"500","data":null,"message":"Null key returned for cache operation (maybe you are using named params on classes without debug info?) Builder[public com.fangcang.grfp.core.entity.CurrencyExchangeRateEntity com.fangcang.grfp.core.cached.impl.CachedCurrencyServiceImpl.getCurrencyRateInfo(java.lang.String)] caches=[cachedCurrencyService.getCurrencyRateInfo] | key='#currencyCode' | keyGenerator='' | cacheManager='ehCacheCacheManager' | cacheResolver='' | condition='' | unless='#result == null' | sync='false'","successful":false}  [68a731be0e00651900f9fc4f] ------
2025-08-21 22:48:33.623|http-nio-6888-exec-5| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.beforeBodyWrite:54|response body: {"code":"500","data":null,"message":"Null key returned for cache operation (maybe you are using named params on classes without debug info?) Builder[public com.fangcang.grfp.core.entity.CurrencyExchangeRateEntity com.fangcang.grfp.core.cached.impl.CachedCurrencyServiceImpl.getCurrencyRateInfo(java.lang.String)] caches=[cachedCurrencyService.getCurrencyRateInfo] | key='#currencyCode' | keyGenerator='' | cacheManager='ehCacheCacheManager' | cacheResolver='' | condition='' | unless='#result == null' | sync='false'","successful":false}  [] ------
2025-08-21 22:48:34.611|http-nio-6888-exec-5| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.afterRequest:28|After request [client=127.0.0.1, POST /grfp-api/Api/ProjectContract/QueryBidHotelPriceLevelList]  [] ------
2025-08-21 22:50:54.633|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStarting:55|Starting GRfpApiApplication on LAPTOP-BVT1ALEM with PID 3220 (D:\RFP\grfp-project\grfp-api\target\classes started by ShamGod in D:\RFP\grfp-project)  [] ------
2025-08-21 22:50:54.635|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStartupProfileInfo:652|The following profiles are active: dev  [] ------
2025-08-21 22:50:56.085|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:249|Multiple Spring Data modules found, entering strict repository configuration mode!  [] ------
2025-08-21 22:50:56.088|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:127|Bootstrapping Spring Data Redis repositories in DEFAULT mode.  [] ------
2025-08-21 22:50:56.137|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:187|Finished Spring Data repository scanning in 33ms. Found 0 Redis repository interfaces.  [] ------
2025-08-21 22:50:57.315|main| INFO|o.s.boot.web.embedded.tomcat.TomcatWebServer.initialize:108|Tomcat initialized with port(s): 6888 (http)  [] ------
2025-08-21 22:50:57.329|main| INFO|org.apache.catalina.core.StandardService.log:173|Starting service [Tomcat]  [] ------
2025-08-21 22:50:57.330|main| INFO|org.apache.catalina.core.StandardEngine.log:173|Starting Servlet engine: [Apache Tomcat/9.0.45]  [] ------
2025-08-21 22:50:57.496|main| INFO|o.a.c.c.C.[Tomcat].[localhost].[/grfp-api].log:173|Initializing Spring embedded WebApplicationContext  [] ------
2025-08-21 22:50:57.496|main| INFO|o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:285|Root WebApplicationContext: initialization completed in 2802 ms  [] ------
2025-08-21 22:50:57.796|main| INFO|org.redisson.Version.logVersion:41|Redisson 3.16.0  [] ------
2025-08-21 22:50:59.813|redisson-netty-2-24| INFO|o.r.connection.pool.MasterPubSubConnectionPool.lambda$run$0:166|1 connections initialized for *************/*************:6379  [] ------
2025-08-21 22:51:00.966|redisson-netty-2-16| INFO|org.redisson.connection.pool.MasterConnectionPool.lambda$run$0:166|24 connections initialized for *************/*************:6379  [] ------
2025-08-21 22:51:04.671|main| INFO|com.fangcang.grfp.api.config.OssAutoConfiguration.ossClient:34|---------------Init OssClient ----------------  [] ------
2025-08-21 22:51:05.595|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'queryBidHotelInfoExecutor'  [] ------
2025-08-21 22:51:06.024|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'hotelHexagonGenerateExecutor'  [] ------
2025-08-21 22:51:07.075|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'auditLogExecutor'  [] ------
2025-08-21 22:51:07.521|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'generalExecutor'  [] ------
2025-08-21 22:51:07.522|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'syncGlinkExecutor'  [] ------
2025-08-21 22:51:07.599|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'rfpCommonExecutor'  [] ------
2025-08-21 22:51:08.790|main| INFO|o.s.boot.web.embedded.tomcat.TomcatWebServer.start:220|Tomcat started on port(s): 6888 (http) with context path '/grfp-api'  [] ------
2025-08-21 22:51:11.892|main| INFO|o.s.s.a.ScheduledAnnotationBeanPostProcessor.finishRegistration:309|No TaskScheduler/ScheduledExecutorService bean found for scheduled processing  [] ------
2025-08-21 22:51:12.735|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStarted:61|Started GRfpApiApplication in 18.693 seconds (JVM running for 20.138)  [] ------
2025-08-21 22:51:12.745|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.afterStarted:59|==============================GrfpApi 系统启动完成v1.0.0-20250808===========================================  [] ------
2025-08-21 22:51:12.966|main| INFO|com.zaxxer.hikari.HikariDataSource.getConnection:110|HikariPool-1 - Starting...  [] ------
2025-08-21 22:51:14.307|main| INFO|com.zaxxer.hikari.HikariDataSource.getConnection:123|HikariPool-1 - Start completed.  [] ------
2025-08-21 22:51:15.533|main| INFO|c.f.g.core.cached.impl.CachedHotelBrandServiceImpl.getNameMap:49|HotelBrandNameMap size en-US, 9926  [] ------
2025-08-21 22:51:16.143|main| INFO|c.f.g.core.cached.impl.CachedHotelBrandServiceImpl.getNameMap:49|HotelBrandNameMap size zh-CN, 9926  [] ------
2025-08-21 22:51:16.639|main| INFO|c.f.g.core.cached.impl.CachedHotelGroupServiceImpl.getNameMap:60|HotelGroupNameMap size en-US, 8123  [] ------
2025-08-21 22:51:17.085|main| INFO|c.f.g.core.cached.impl.CachedHotelGroupServiceImpl.getNameMap:60|HotelGroupNameMap size zh-CN, 8123  [] ------
2025-08-21 22:51:26.030|http-nio-6888-exec-1| INFO|o.a.c.c.C.[Tomcat].[localhost].[/grfp-api].log:173|Initializing Spring DispatcherServlet 'dispatcherServlet'  [] ------
2025-08-21 22:51:26.031|http-nio-6888-exec-1| INFO|org.springframework.web.servlet.DispatcherServlet.initServletBean:525|Initializing Servlet 'dispatcherServlet'  [] ------
2025-08-21 22:51:26.045|http-nio-6888-exec-1| INFO|org.springframework.web.servlet.DispatcherServlet.initServletBean:547|Completed initialization in 14 ms  [] ------
2025-08-21 22:51:26.405|http-nio-6888-exec-1| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.beforeRequest:23|Before request [client=127.0.0.1, POST /grfp-api/Api/ProjectContract/QueryBidHotelPriceLevelList]  [68a7326e0e000efac09e48e0] ------
2025-08-21 22:51:26.822|http-nio-6888-exec-1| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.afterBodyRead:37|request body: {"hotelId":134789,"projectIntentHotelId":236}  [68a7326e0e000efac09e48e0] ------
2025-08-21 22:51:28.925|main| INFO|c.f.grfp.core.cached.impl.CachedCityServiceImpl.getNameMap:55|City Map size en-US, 309420  [] ------
2025-08-21 22:51:29.761|http-nio-6888-exec-1| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.beforeBodyWrite:54|response body: {"code":"200","data":[{"bidHotelPriceGroupList":[{"applicableWeeks":"2,3,4,5,6,7,1","bidHotelPriceList":[{"hotelPriceId":164,"lastOnePersonIncludeTaxPrice":385.93,"lastOnePersonPrice":385.93,"lastOnePersonPriceTaxInfo":null,"lastTwoPersonIncludeTaxPrice":0.00,"lastTwoPersonPrice":0.00,"lastTwoPersonPriceTaxInfo":null,"onePersonIncludeTaxPrice":449.99,"onePersonPrice":385.93,"onePersonPriceTaxInfo":{"basePrice":385.93,"bidPrice":385.93,"taxInfo":{"citytxFeeAmount":0,"citytxFeeIsInclude":0,"citytxFeeType":null,"citytxFeeValue":0.00000,"earlyckFeeIsInclude":0,"earlyckFeeType":null,"earlyckFeeValue":0.00000,"lodgtxFeeAmount":0,"lodgtxFeeIsInclude":0,"lodgtxFeeType":null,"lodgtxFeeValue":0.00000,"occFeeAmount":0,"occFeeIsInclude":0,"occFeeType":null,"occFeeValue":0.00000,"othertx1FeeAmount":0,"othertx1FeeDesc":"NA","othertx1FeeIsInclude":0,"othertx1FeeType":null,"othertx1FeeValue":0.00000,"othertx2FeeAmount":0,"othertx2FeeDesc":null,"othertx2FeeIsInclude":null,"othertx2FeeType":null,"othertx2FeeValue":null,"othertx3FeeAmount":0,"othertx3FeeDesc":null,"othertx3FeeIsInclude":null,"othertx3FeeType":null,"othertx3FeeValue":null,"serviceFeeAmount":38.59,"serviceFeeIsInclude":0,"serviceFeeType":1,"serviceFeeValue":10.00000,"statetxFeeAmount":0,"statetxFeeIsInclude":0,"statetxFeeType":null,"statetxFeeValue":0.00000,"vatgstfbFeeAmount":0,"vatgstfbFeeIsInclude":0,"vatgstfbFeeType":null,"vatgstfbFeeValue":0.00000,"vatgstrmFeeAmount":25.47,"vatgstrmFeeIsInclude":0,"vatgstrmFeeType":1,"vatgstrmFeeValue":6.60000},"totalCostAmount":449.99,"totalIncludeTaxAmount":0,"totalUnIncludeTaxAmount":64.06},"priceType":1,"twoPersonIncludeTaxPrice":0.00,"twoPersonPrice":0.00,"twoPersonPriceTaxInfo":null}],"hotelPriceGroupId":104,"hotelPriceLevelId":126,"isIncludeBreakfast":0,"isLocked":null,"lra":0,"remark":""}],"bigBedRoomCount":null,"currencyCode":"CNY","doubleBedRoomCount":null,"exchangeRate":0.**********,"hotelPriceLevelId":126,"roomLevelNo":1,"roomList":[{"customRoomTypeName":null,"displayOrder":null,"hotelPriceLevelId":null,"priceApplicableRoomId":null,"roomName":"Standard","roomTypeId":null}],"roomNameDesc":"Standard","totalRoomCount":133,"viewCurrencyCode":null,"viewCurrencyExchangeRate":null}, {"bidHotelPriceGroupList":null,"bigBedRoomCount":null,"currencyCode":"CNY","doubleBedRoomCount":null,"exchangeRate":0.**********,"hotelPriceLevelId":127,"roomLevelNo":2,"roomList":[{"customRoomTypeName":null,"displayOrder":null,"hotelPriceLevelId":null,"priceApplicableRoomId":null,"roomName":"Deluxe","roomTypeId":null}],"roomNameDesc":"Deluxe","totalRoomCount":83,"viewCurrencyCode":null,"viewCurrencyExchangeRate":null}],"message":"Succeeded","successful":true}  [68a7326e0e000efac09e48e0] ------
2025-08-21 22:51:31.208|http-nio-6888-exec-1| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.afterRequest:28|After request [client=127.0.0.1, POST /grfp-api/Api/ProjectContract/QueryBidHotelPriceLevelList]  [68a7326e0e000efac09e48e0] ------
2025-08-21 22:51:43.188|main| INFO|c.f.grfp.core.cached.impl.CachedCityServiceImpl.getNameMap:55|City Map size zh-CN, 309420  [] ------
2025-08-21 22:52:00.883|http-nio-6888-exec-3| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.beforeRequest:23|Before request [client=127.0.0.1, POST /grfp-api/Api/ProjectContract/QueryBidHotelPriceLevelList]  [68a732900e000efac09e48e1] ------
2025-08-21 22:52:00.967|http-nio-6888-exec-3| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.afterBodyRead:37|request body: {"hotelId":134789,"projectIntentHotelId":236}  [68a732900e000efac09e48e1] ------
2025-08-21 22:52:02.752|http-nio-6888-exec-3| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.beforeBodyWrite:54|response body: {"code":"200","data":[{"bidHotelPriceGroupList":[{"applicableWeeks":"2,3,4,5,6,7,1","bidHotelPriceList":[{"hotelPriceId":164,"lastOnePersonIncludeTaxPrice":385.93,"lastOnePersonPrice":385.93,"lastOnePersonPriceTaxInfo":null,"lastTwoPersonIncludeTaxPrice":0.00,"lastTwoPersonPrice":0.00,"lastTwoPersonPriceTaxInfo":null,"onePersonIncludeTaxPrice":449.99,"onePersonPrice":385.93,"onePersonPriceTaxInfo":{"basePrice":385.93,"bidPrice":385.93,"taxInfo":{"citytxFeeAmount":0,"citytxFeeIsInclude":0,"citytxFeeType":null,"citytxFeeValue":0.00000,"earlyckFeeIsInclude":0,"earlyckFeeType":null,"earlyckFeeValue":0.00000,"lodgtxFeeAmount":0,"lodgtxFeeIsInclude":0,"lodgtxFeeType":null,"lodgtxFeeValue":0.00000,"occFeeAmount":0,"occFeeIsInclude":0,"occFeeType":null,"occFeeValue":0.00000,"othertx1FeeAmount":0,"othertx1FeeDesc":"NA","othertx1FeeIsInclude":0,"othertx1FeeType":null,"othertx1FeeValue":0.00000,"othertx2FeeAmount":0,"othertx2FeeDesc":null,"othertx2FeeIsInclude":null,"othertx2FeeType":null,"othertx2FeeValue":null,"othertx3FeeAmount":0,"othertx3FeeDesc":null,"othertx3FeeIsInclude":null,"othertx3FeeType":null,"othertx3FeeValue":null,"serviceFeeAmount":38.59,"serviceFeeIsInclude":0,"serviceFeeType":1,"serviceFeeValue":10.00000,"statetxFeeAmount":0,"statetxFeeIsInclude":0,"statetxFeeType":null,"statetxFeeValue":0.00000,"vatgstfbFeeAmount":0,"vatgstfbFeeIsInclude":0,"vatgstfbFeeType":null,"vatgstfbFeeValue":0.00000,"vatgstrmFeeAmount":25.47,"vatgstrmFeeIsInclude":0,"vatgstrmFeeType":1,"vatgstrmFeeValue":6.60000},"totalCostAmount":449.99,"totalIncludeTaxAmount":0,"totalUnIncludeTaxAmount":64.06},"priceType":1,"twoPersonIncludeTaxPrice":0.00,"twoPersonPrice":0.00,"twoPersonPriceTaxInfo":null}],"hotelPriceGroupId":104,"hotelPriceLevelId":126,"isIncludeBreakfast":0,"isLocked":null,"lra":0,"remark":""}],"bigBedRoomCount":null,"currencyCode":"CNY","doubleBedRoomCount":null,"exchangeRate":0.**********,"hotelPriceLevelId":126,"roomLevelNo":1,"roomList":[{"customRoomTypeName":null,"displayOrder":null,"hotelPriceLevelId":null,"priceApplicableRoomId":null,"roomName":"Standard","roomTypeId":null}],"roomNameDesc":"Standard","totalRoomCount":133,"viewCurrencyCode":null,"viewCurrencyExchangeRate":null}, {"bidHotelPriceGroupList":null,"bigBedRoomCount":null,"currencyCode":"CNY","doubleBedRoomCount":null,"exchangeRate":0.**********,"hotelPriceLevelId":127,"roomLevelNo":2,"roomList":[{"customRoomTypeName":null,"displayOrder":null,"hotelPriceLevelId":null,"priceApplicableRoomId":null,"roomName":"Deluxe","roomTypeId":null}],"roomNameDesc":"Deluxe","totalRoomCount":83,"viewCurrencyCode":null,"viewCurrencyExchangeRate":null}],"message":"Succeeded","successful":true}  [68a732900e000efac09e48e1] ------
2025-08-21 22:52:03.670|http-nio-6888-exec-3| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.afterRequest:28|After request [client=127.0.0.1, POST /grfp-api/Api/ProjectContract/QueryBidHotelPriceLevelList]  [68a732900e000efac09e48e1] ------
2025-08-21 22:52:25.088|http-nio-6888-exec-2| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.beforeRequest:23|Before request [client=127.0.0.1, POST /grfp-api/Api/ProjectContract/QueryBidHotelPriceLevelList]  [68a732a90e000efac09e48e2] ------
2025-08-21 22:52:25.145|http-nio-6888-exec-2| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.afterBodyRead:37|request body: {"hotelId":134789,"projectIntentHotelId":236}  [68a732a90e000efac09e48e2] ------
2025-08-21 22:52:26.970|http-nio-6888-exec-2| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.beforeBodyWrite:54|response body: {"code":"200","data":[{"bidHotelPriceGroupList":[{"applicableWeeks":"2,3,4,5,6,7,1","bidHotelPriceList":[{"hotelPriceId":164,"lastOnePersonIncludeTaxPrice":385.93,"lastOnePersonPrice":385.93,"lastOnePersonPriceTaxInfo":null,"lastTwoPersonIncludeTaxPrice":0.00,"lastTwoPersonPrice":0.00,"lastTwoPersonPriceTaxInfo":null,"onePersonIncludeTaxPrice":449.99,"onePersonPrice":385.93,"onePersonPriceTaxInfo":{"basePrice":385.93,"bidPrice":385.93,"taxInfo":{"citytxFeeAmount":0,"citytxFeeIsInclude":0,"citytxFeeType":null,"citytxFeeValue":0.00000,"earlyckFeeIsInclude":0,"earlyckFeeType":null,"earlyckFeeValue":0.00000,"lodgtxFeeAmount":0,"lodgtxFeeIsInclude":0,"lodgtxFeeType":null,"lodgtxFeeValue":0.00000,"occFeeAmount":0,"occFeeIsInclude":0,"occFeeType":null,"occFeeValue":0.00000,"othertx1FeeAmount":0,"othertx1FeeDesc":"NA","othertx1FeeIsInclude":0,"othertx1FeeType":null,"othertx1FeeValue":0.00000,"othertx2FeeAmount":0,"othertx2FeeDesc":null,"othertx2FeeIsInclude":null,"othertx2FeeType":null,"othertx2FeeValue":null,"othertx3FeeAmount":0,"othertx3FeeDesc":null,"othertx3FeeIsInclude":null,"othertx3FeeType":null,"othertx3FeeValue":null,"serviceFeeAmount":38.59,"serviceFeeIsInclude":0,"serviceFeeType":1,"serviceFeeValue":10.00000,"statetxFeeAmount":0,"statetxFeeIsInclude":0,"statetxFeeType":null,"statetxFeeValue":0.00000,"vatgstfbFeeAmount":0,"vatgstfbFeeIsInclude":0,"vatgstfbFeeType":null,"vatgstfbFeeValue":0.00000,"vatgstrmFeeAmount":25.47,"vatgstrmFeeIsInclude":0,"vatgstrmFeeType":1,"vatgstrmFeeValue":6.60000},"totalCostAmount":449.99,"totalIncludeTaxAmount":0,"totalUnIncludeTaxAmount":64.06},"priceType":1,"twoPersonIncludeTaxPrice":0.00,"twoPersonPrice":0.00,"twoPersonPriceTaxInfo":null}],"hotelPriceGroupId":104,"hotelPriceLevelId":126,"isIncludeBreakfast":0,"isLocked":null,"lra":0,"remark":""}],"bigBedRoomCount":null,"currencyCode":"CNY","doubleBedRoomCount":null,"exchangeRate":0.**********,"hotelPriceLevelId":126,"roomLevelNo":1,"roomList":[{"customRoomTypeName":null,"displayOrder":null,"hotelPriceLevelId":null,"priceApplicableRoomId":null,"roomName":"Standard","roomTypeId":null}],"roomNameDesc":"Standard","totalRoomCount":133,"viewCurrencyCode":"USD","viewCurrencyExchangeRate":0.14}, {"bidHotelPriceGroupList":null,"bigBedRoomCount":null,"currencyCode":"CNY","doubleBedRoomCount":null,"exchangeRate":0.**********,"hotelPriceLevelId":127,"roomLevelNo":2,"roomList":[{"customRoomTypeName":null,"displayOrder":null,"hotelPriceLevelId":null,"priceApplicableRoomId":null,"roomName":"Deluxe","roomTypeId":null}],"roomNameDesc":"Deluxe","totalRoomCount":83,"viewCurrencyCode":"USD","viewCurrencyExchangeRate":0.14}],"message":"Succeeded","successful":true}  [68a732a90e000efac09e48e2] ------
2025-08-21 22:52:27.814|http-nio-6888-exec-2| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.afterRequest:28|After request [client=127.0.0.1, POST /grfp-api/Api/ProjectContract/QueryBidHotelPriceLevelList]  [68a732a90e000efac09e48e2] ------
2025-08-21 22:55:48.124|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStarting:55|Starting GRfpApiApplication on LAPTOP-BVT1ALEM with PID 20664 (D:\RFP\grfp-project\grfp-api\target\classes started by ShamGod in D:\RFP\grfp-project)  [] ------
2025-08-21 22:55:48.126|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStartupProfileInfo:652|The following profiles are active: dev  [] ------
2025-08-21 22:55:49.999|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:249|Multiple Spring Data modules found, entering strict repository configuration mode!  [] ------
2025-08-21 22:55:50.002|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:127|Bootstrapping Spring Data Redis repositories in DEFAULT mode.  [] ------
2025-08-21 22:55:50.054|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:187|Finished Spring Data repository scanning in 34ms. Found 0 Redis repository interfaces.  [] ------
2025-08-21 22:55:51.402|main| INFO|o.s.boot.web.embedded.tomcat.TomcatWebServer.initialize:108|Tomcat initialized with port(s): 6888 (http)  [] ------
2025-08-21 22:55:51.418|main| INFO|org.apache.catalina.core.StandardService.log:173|Starting service [Tomcat]  [] ------
2025-08-21 22:55:51.419|main| INFO|org.apache.catalina.core.StandardEngine.log:173|Starting Servlet engine: [Apache Tomcat/9.0.45]  [] ------
2025-08-21 22:55:51.587|main| INFO|o.a.c.c.C.[Tomcat].[localhost].[/grfp-api].log:173|Initializing Spring embedded WebApplicationContext  [] ------
2025-08-21 22:55:51.587|main| INFO|o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:285|Root WebApplicationContext: initialization completed in 3385 ms  [] ------
2025-08-21 22:55:51.922|main| INFO|org.redisson.Version.logVersion:41|Redisson 3.16.0  [] ------
2025-08-21 22:55:53.903|redisson-netty-2-27| INFO|o.r.connection.pool.MasterPubSubConnectionPool.lambda$run$0:166|1 connections initialized for *************/*************:6379  [] ------
2025-08-21 22:55:55.076|redisson-netty-2-18| INFO|org.redisson.connection.pool.MasterConnectionPool.lambda$run$0:166|24 connections initialized for *************/*************:6379  [] ------
2025-08-21 22:55:58.590|main| INFO|com.fangcang.grfp.api.config.OssAutoConfiguration.ossClient:34|---------------Init OssClient ----------------  [] ------
2025-08-21 22:55:59.613|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'queryBidHotelInfoExecutor'  [] ------
2025-08-21 22:56:00.046|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'hotelHexagonGenerateExecutor'  [] ------
2025-08-21 22:56:01.146|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'auditLogExecutor'  [] ------
2025-08-21 22:56:01.611|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'generalExecutor'  [] ------
2025-08-21 22:56:01.612|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'syncGlinkExecutor'  [] ------
2025-08-21 22:56:01.692|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'rfpCommonExecutor'  [] ------
2025-08-21 22:56:02.819|main| INFO|o.s.boot.web.embedded.tomcat.TomcatWebServer.start:220|Tomcat started on port(s): 6888 (http) with context path '/grfp-api'  [] ------
2025-08-21 22:56:06.037|main| INFO|o.s.s.a.ScheduledAnnotationBeanPostProcessor.finishRegistration:309|No TaskScheduler/ScheduledExecutorService bean found for scheduled processing  [] ------
2025-08-21 22:56:06.651|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStarted:61|Started GRfpApiApplication in 19.339 seconds (JVM running for 21.078)  [] ------
2025-08-21 22:56:06.662|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.afterStarted:59|==============================GrfpApi 系统启动完成v1.0.0-20250808===========================================  [] ------
2025-08-21 22:56:06.886|main| INFO|com.zaxxer.hikari.HikariDataSource.getConnection:110|HikariPool-1 - Starting...  [] ------
2025-08-21 22:56:08.010|main| INFO|com.zaxxer.hikari.HikariDataSource.getConnection:123|HikariPool-1 - Start completed.  [] ------
2025-08-21 22:56:09.137|main| INFO|c.f.g.core.cached.impl.CachedHotelBrandServiceImpl.getNameMap:49|HotelBrandNameMap size en-US, 9926  [] ------
2025-08-21 22:56:09.662|main| INFO|c.f.g.core.cached.impl.CachedHotelBrandServiceImpl.getNameMap:49|HotelBrandNameMap size zh-CN, 9926  [] ------
2025-08-21 22:56:10.153|main| INFO|c.f.g.core.cached.impl.CachedHotelGroupServiceImpl.getNameMap:60|HotelGroupNameMap size en-US, 8123  [] ------
2025-08-21 22:56:10.559|main| INFO|c.f.g.core.cached.impl.CachedHotelGroupServiceImpl.getNameMap:60|HotelGroupNameMap size zh-CN, 8123  [] ------
2025-08-21 22:56:23.092|main| INFO|c.f.grfp.core.cached.impl.CachedCityServiceImpl.getNameMap:55|City Map size en-US, 309420  [] ------
2025-08-21 22:56:36.206|main| INFO|c.f.grfp.core.cached.impl.CachedCityServiceImpl.getNameMap:55|City Map size zh-CN, 309420  [] ------
2025-08-21 22:56:51.119|http-nio-6888-exec-1| INFO|o.a.c.c.C.[Tomcat].[localhost].[/grfp-api].log:173|Initializing Spring DispatcherServlet 'dispatcherServlet'  [] ------
2025-08-21 22:56:51.119|http-nio-6888-exec-1| INFO|org.springframework.web.servlet.DispatcherServlet.initServletBean:525|Initializing Servlet 'dispatcherServlet'  [] ------
2025-08-21 22:56:51.136|http-nio-6888-exec-1| INFO|org.springframework.web.servlet.DispatcherServlet.initServletBean:547|Completed initialization in 17 ms  [] ------
2025-08-21 22:56:51.507|http-nio-6888-exec-1| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.beforeRequest:23|Before request [client=127.0.0.1, POST /grfp-api/Api/ProjectContract/QueryBidHotelPriceLevelList]  [68a733b30e00a9469b8a2d54] ------
2025-08-21 22:56:51.887|http-nio-6888-exec-1| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.afterBodyRead:37|request body: {"hotelId":134789,"projectIntentHotelId":236}  [68a733b30e00a9469b8a2d54] ------
2025-08-21 22:56:54.945|http-nio-6888-exec-1| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.beforeBodyWrite:54|response body: {"code":"200","data":[{"bidHotelPriceGroupList":[{"applicableWeeks":"2,3,4,5,6,7,1","bidHotelPriceList":[{"hotelPriceId":164,"lastOnePersonIncludeTaxPrice":385.93,"lastOnePersonPrice":385.93,"lastOnePersonPriceTaxInfo":null,"lastTwoPersonIncludeTaxPrice":0.00,"lastTwoPersonPrice":0.00,"lastTwoPersonPriceTaxInfo":null,"onePersonIncludeTaxPrice":449.99,"onePersonPrice":385.93,"onePersonPriceTaxInfo":{"basePrice":385.93,"bidPrice":385.93,"taxInfo":{"citytxFeeAmount":0,"citytxFeeIsInclude":0,"citytxFeeType":null,"citytxFeeValue":0.00000,"earlyckFeeIsInclude":0,"earlyckFeeType":null,"earlyckFeeValue":0.00000,"lodgtxFeeAmount":0,"lodgtxFeeIsInclude":0,"lodgtxFeeType":null,"lodgtxFeeValue":0.00000,"occFeeAmount":0,"occFeeIsInclude":0,"occFeeType":null,"occFeeValue":0.00000,"othertx1FeeAmount":0,"othertx1FeeDesc":"NA","othertx1FeeIsInclude":0,"othertx1FeeType":null,"othertx1FeeValue":0.00000,"othertx2FeeAmount":0,"othertx2FeeDesc":null,"othertx2FeeIsInclude":null,"othertx2FeeType":null,"othertx2FeeValue":null,"othertx3FeeAmount":0,"othertx3FeeDesc":null,"othertx3FeeIsInclude":null,"othertx3FeeType":null,"othertx3FeeValue":null,"serviceFeeAmount":38.59,"serviceFeeIsInclude":0,"serviceFeeType":1,"serviceFeeValue":10.00000,"statetxFeeAmount":0,"statetxFeeIsInclude":0,"statetxFeeType":null,"statetxFeeValue":0.00000,"vatgstfbFeeAmount":0,"vatgstfbFeeIsInclude":0,"vatgstfbFeeType":null,"vatgstfbFeeValue":0.00000,"vatgstrmFeeAmount":25.47,"vatgstrmFeeIsInclude":0,"vatgstrmFeeType":1,"vatgstrmFeeValue":6.60000},"totalCostAmount":449.99,"totalIncludeTaxAmount":0,"totalUnIncludeTaxAmount":64.06},"priceType":1,"twoPersonIncludeTaxPrice":0.00,"twoPersonPrice":0.00,"twoPersonPriceTaxInfo":null}],"hotelPriceGroupId":104,"hotelPriceLevelId":126,"isIncludeBreakfast":0,"isLocked":null,"lra":0,"remark":""}],"bigBedRoomCount":null,"currencyCode":"CNY","doubleBedRoomCount":null,"exchangeRate":0.**********,"hotelPriceLevelId":126,"roomLevelNo":1,"roomList":[{"customRoomTypeName":null,"displayOrder":null,"hotelPriceLevelId":null,"priceApplicableRoomId":null,"roomName":"Standard","roomTypeId":null}],"roomNameDesc":"Standard","totalRoomCount":133,"viewCurrencyCode":"USD","viewCurrencyExchangeRate":0.**********}, {"bidHotelPriceGroupList":null,"bigBedRoomCount":null,"currencyCode":"CNY","doubleBedRoomCount":null,"exchangeRate":0.**********,"hotelPriceLevelId":127,"roomLevelNo":2,"roomList":[{"customRoomTypeName":null,"displayOrder":null,"hotelPriceLevelId":null,"priceApplicableRoomId":null,"roomName":"Deluxe","roomTypeId":null}],"roomNameDesc":"Deluxe","totalRoomCount":83,"viewCurrencyCode":"USD","viewCurrencyExchangeRate":0.**********}],"message":"Succeeded","successful":true}  [68a733b30e00a9469b8a2d54] ------
2025-08-21 22:56:56.132|http-nio-6888-exec-1| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.afterRequest:28|After request [client=127.0.0.1, POST /grfp-api/Api/ProjectContract/QueryBidHotelPriceLevelList]  [68a733b30e00a9469b8a2d54] ------
2025-08-21 23:02:07.416|http-nio-6888-exec-5| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.beforeRequest:23|Before request [client=127.0.0.1, POST /grfp-api/Api/ProjectContract/QueryBidMapHotelInfo]  [68a734ef0e00a9469b8a2d55] ------
2025-08-21 23:02:07.498|http-nio-6888-exec-5| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.afterBodyRead:37|request body: {"cityCode":null,"hotelGroupBrandIdList":null,"hotelId":134789,"hotelName":null,"orgId":null,"projectId":113,"pageIndex":1,"pageSize":20}  [68a734ef0e00a9469b8a2d55] ------
2025-08-21 23:02:16.216|http-nio-6888-exec-5| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.beforeBodyWrite:54|response body: {"code":"200","data":{"bidApplicableDayList":[{"endDate":"Sat Jan 31 00:00:00 CST 2026","priceType":1,"startDate":"Wed Jan 01 00:00:00 CST 2025"}],"bidHotelInfoQueryResponseList":[],"bidState":1,"bidUnApplicableDayList":[{"endDate":"Sun Feb 02 00:00:00 CST 2025","startDate":"Sat Feb 01 00:00:00 CST 2025"}, {"endDate":"Sat Apr 05 00:00:00 CST 2025","startDate":"Sat Apr 05 00:00:00 CST 2025"}, {"endDate":"Sun May 04 00:00:00 CST 2025","startDate":"Fri May 02 00:00:00 CST 2025"}, {"endDate":"Sun Jun 01 00:00:00 CST 2025","startDate":"Sat May 31 00:00:00 CST 2025"}, {"endDate":"Mon Oct 06 00:00:00 CST 2025","startDate":"Thu Oct 02 00:00:00 CST 2025"}],"bidWeight":0,"breakfastNum":"No Breakfast","cityCode":"NKG","cityName":"Nanjing","cityQueryProjectHotelBidStatResponse":{"lastYearAmount":null,"lastYearAmountOrder":null,"lastYearRoomNightCount":null,"lastYearRoomNightOrder":null,"lastYearServicePoint":null,"lastYearServicePointOrder":null,"rating":"4.6","ratingOrder":1},"cityTheSameLevelQueryProjectHotelBidStatResponse":{"lastYearAmount":0,"lastYearAmountOrder":0,"lastYearRoomNightCount":0,"lastYearRoomNightOrder":0,"lastYearServicePoint":null,"lastYearServicePointOrder":null,"rating":"4.6","ratingOrder":0},"currencyCode":"CNY","hotelId":134789,"hotelImageList":null,"hotelName":"Holiday Inn Nanjing Aqua City","hotelPriceLevelInfoVOList":[{"bidHotelPriceGroupList":[{"applicableWeeks":"2,3,4,5,6,7,1","bidHotelPriceList":[{"hotelPriceId":164,"lastOnePersonIncludeTaxPrice":385.93,"lastOnePersonPrice":385.93,"lastOnePersonPriceTaxInfo":null,"lastTwoPersonIncludeTaxPrice":0.00,"lastTwoPersonPrice":0.00,"lastTwoPersonPriceTaxInfo":null,"onePersonIncludeTaxPrice":449.99,"onePersonPrice":385.93,"onePersonPriceTaxInfo":{"basePrice":385.93,"bidPrice":385.93,"taxInfo":{"citytxFeeAmount":0,"citytxFeeIsInclude":0,"citytxFeeType":null,"citytxFeeValue":0.00000,"earlyckFeeIsInclude":0,"earlyckFeeType":null,"earlyckFeeValue":0.00000,"lodgtxFeeAmount":0,"lodgtxFeeIsInclude":0,"lodgtxFeeType":null,"lodgtxFeeValue":0.00000,"occFeeAmount":0,"occFeeIsInclude":0,"occFeeType":null,"occFeeValue":0.00000,"othertx1FeeAmount":0,"othertx1FeeDesc":"NA","othertx1FeeIsInclude":0,"othertx1FeeType":null,"othertx1FeeValue":0.00000,"othertx2FeeAmount":0,"othertx2FeeDesc":null,"othertx2FeeIsInclude":null,"othertx2FeeType":null,"othertx2FeeValue":null,"othertx3FeeAmount":0,"othertx3FeeDesc":null,"othertx3FeeIsInclude":null,"othertx3FeeType":null,"othertx3FeeValue":null,"serviceFeeAmount":38.59,"serviceFeeIsInclude":0,"serviceFeeType":1,"serviceFeeValue":10.00000,"statetxFeeAmount":0,"statetxFeeIsInclude":0,"statetxFeeType":null,"statetxFeeValue":0.00000,"vatgstfbFeeAmount":0,"vatgstfbFeeIsInclude":0,"vatgstfbFeeType":null,"vatgstfbFeeValue":0.00000,"vatgstrmFeeAmount":25.47,"vatgstrmFeeIsInclude":0,"vatgstrmFeeType":1,"vatgstrmFeeValue":6.60000},"totalCostAmount":449.99,"totalIncludeTaxAmount":0,"totalUnIncludeTaxAmount":64.06},"priceType":1,"twoPersonIncludeTaxPrice":0.00,"twoPersonPrice":0.00,"twoPersonPriceTaxInfo":null}],"hotelPriceGroupId":104,"hotelPriceLevelId":126,"isIncludeBreakfast":0,"isLocked":null,"lra":0,"remark":""}],"bigBedRoomCount":null,"currencyCode":"CNY","doubleBedRoomCount":null,"exchangeRate":0.**********,"hotelPriceLevelId":126,"roomLevelNo":1,"roomList":[{"customRoomTypeName":null,"displayOrder":null,"hotelPriceLevelId":null,"priceApplicableRoomId":null,"roomName":"Standard","roomTypeId":null}],"roomNameDesc":"Standard","totalRoomCount":133,"viewCurrencyCode":"USD","viewCurrencyExchangeRate":0.**********}, {"bidHotelPriceGroupList":null,"bigBedRoomCount":null,"currencyCode":"CNY","doubleBedRoomCount":null,"exchangeRate":0.**********,"hotelPriceLevelId":127,"roomLevelNo":2,"roomList":[{"customRoomTypeName":null,"displayOrder":null,"hotelPriceLevelId":null,"priceApplicableRoomId":null,"roomName":"Deluxe","roomTypeId":null}],"roomNameDesc":"Deluxe","totalRoomCount":83,"viewCurrencyCode":"USD","viewCurrencyExchangeRate":0.**********}],"hotelProjectPoiInfoResponseList":[],"hotelServicePoint":null,"hotelStar":"29","hotelStarName":"Quisa Five Star","isInvitedHotel":0,"isRecommendHotel":null,"lastYearHotelPriceLevelInfoVOList":null,"lastYearTotalRoomNight":"--","lastYearTotalSalesAmount":"--","latGoogle":32.**********,"latestYearOrder":0,"latestYearRoomNight":0,"lngGoogle":118.**********,"mainPicUrl":"http:\/\/fcimage.fangcang.com\/images\/hotels\/789\/134789\/202504291745911080113.jpg","minPrice":385.93,"poiDistance":null,"poiName":null,"projectHotelRemarkResponseList":[],"projectIntentHotelId":236,"projectWeight":72.00,"rating":"4.6","relatedProjectId":111,"roomTypeDesc":"Standard","theSameLevelOrder":0,"threeQueryProjectHotelBidStatResponse":{"lastYearAmount":0,"lastYearAmountOrder":0,"lastYearRoomNightCount":0,"lastYearRoomNightOrder":0,"lastYearServicePoint":null,"lastYearServicePointOrder":null,"rating":"4.6","ratingOrder":0},"totalRoomNight100":null,"totalRoomNight100To200":null,"totalRoomNight200To300":null,"totalRoomNight300To400":null,"totalRoomNight400To500":null,"totalRoomNight500":null,"viewCurrencyCode":"USD","viewCurrencyExchangeRate":0.14},"message":"Succeeded","successful":true}  [68a734ef0e00a9469b8a2d55] ------
2025-08-21 23:02:17.293|http-nio-6888-exec-5| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.afterRequest:28|After request [client=127.0.0.1, POST /grfp-api/Api/ProjectContract/QueryBidMapHotelInfo]  [68a734ef0e00a9469b8a2d55] ------
2025-08-21 23:02:54.740|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStarting:55|Starting GRfpApiApplication on LAPTOP-BVT1ALEM with PID 7952 (D:\RFP\grfp-project\grfp-api\target\classes started by ShamGod in D:\RFP\grfp-project)  [] ------
2025-08-21 23:02:54.742|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStartupProfileInfo:652|The following profiles are active: dev  [] ------
2025-08-21 23:02:56.520|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:249|Multiple Spring Data modules found, entering strict repository configuration mode!  [] ------
2025-08-21 23:02:56.524|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:127|Bootstrapping Spring Data Redis repositories in DEFAULT mode.  [] ------
2025-08-21 23:02:56.574|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:187|Finished Spring Data repository scanning in 34ms. Found 0 Redis repository interfaces.  [] ------
2025-08-21 23:02:57.830|main| INFO|o.s.boot.web.embedded.tomcat.TomcatWebServer.initialize:108|Tomcat initialized with port(s): 6888 (http)  [] ------
2025-08-21 23:02:57.845|main| INFO|org.apache.catalina.core.StandardService.log:173|Starting service [Tomcat]  [] ------
2025-08-21 23:02:57.845|main| INFO|org.apache.catalina.core.StandardEngine.log:173|Starting Servlet engine: [Apache Tomcat/9.0.45]  [] ------
2025-08-21 23:02:58.013|main| INFO|o.a.c.c.C.[Tomcat].[localhost].[/grfp-api].log:173|Initializing Spring embedded WebApplicationContext  [] ------
2025-08-21 23:02:58.013|main| INFO|o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:285|Root WebApplicationContext: initialization completed in 3211 ms  [] ------
2025-08-21 23:02:58.359|main| INFO|org.redisson.Version.logVersion:41|Redisson 3.16.0  [] ------
2025-08-21 23:03:00.426|redisson-netty-2-13| INFO|o.r.connection.pool.MasterPubSubConnectionPool.lambda$run$0:166|1 connections initialized for *************/*************:6379  [] ------
2025-08-21 23:03:02.249|redisson-netty-2-17| INFO|org.redisson.connection.pool.MasterConnectionPool.lambda$run$0:166|24 connections initialized for *************/*************:6379  [] ------
2025-08-21 23:03:05.891|main| INFO|com.fangcang.grfp.api.config.OssAutoConfiguration.ossClient:34|---------------Init OssClient ----------------  [] ------
2025-08-21 23:03:06.877|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'queryBidHotelInfoExecutor'  [] ------
2025-08-21 23:03:07.287|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'hotelHexagonGenerateExecutor'  [] ------
2025-08-21 23:03:08.334|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'auditLogExecutor'  [] ------
2025-08-21 23:03:08.796|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'generalExecutor'  [] ------
2025-08-21 23:03:08.797|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'syncGlinkExecutor'  [] ------
2025-08-21 23:03:08.878|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'rfpCommonExecutor'  [] ------
2025-08-21 23:03:09.928|main| INFO|o.s.boot.web.embedded.tomcat.TomcatWebServer.start:220|Tomcat started on port(s): 6888 (http) with context path '/grfp-api'  [] ------
2025-08-21 23:03:13.098|main| INFO|o.s.s.a.ScheduledAnnotationBeanPostProcessor.finishRegistration:309|No TaskScheduler/ScheduledExecutorService bean found for scheduled processing  [] ------
2025-08-21 23:03:14.025|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStarted:61|Started GRfpApiApplication in 19.861 seconds (JVM running for 21.415)  [] ------
2025-08-21 23:03:14.036|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.afterStarted:59|==============================GrfpApi 系统启动完成v1.0.0-20250808===========================================  [] ------
2025-08-21 23:03:14.267|main| INFO|com.zaxxer.hikari.HikariDataSource.getConnection:110|HikariPool-1 - Starting...  [] ------
2025-08-21 23:03:15.597|main| INFO|com.zaxxer.hikari.HikariDataSource.getConnection:123|HikariPool-1 - Start completed.  [] ------
2025-08-21 23:03:17.534|main| INFO|c.f.g.core.cached.impl.CachedHotelBrandServiceImpl.getNameMap:49|HotelBrandNameMap size en-US, 9926  [] ------
2025-08-21 23:03:19.036|main| INFO|c.f.g.core.cached.impl.CachedHotelBrandServiceImpl.getNameMap:49|HotelBrandNameMap size zh-CN, 9926  [] ------
2025-08-21 23:03:19.724|main| INFO|c.f.g.core.cached.impl.CachedHotelGroupServiceImpl.getNameMap:60|HotelGroupNameMap size en-US, 8123  [] ------
2025-08-21 23:03:20.448|main| INFO|c.f.g.core.cached.impl.CachedHotelGroupServiceImpl.getNameMap:60|HotelGroupNameMap size zh-CN, 8123  [] ------
2025-08-21 23:03:33.040|http-nio-6888-exec-1| INFO|o.a.c.c.C.[Tomcat].[localhost].[/grfp-api].log:173|Initializing Spring DispatcherServlet 'dispatcherServlet'  [] ------
2025-08-21 23:03:33.040|http-nio-6888-exec-1| INFO|org.springframework.web.servlet.DispatcherServlet.initServletBean:525|Initializing Servlet 'dispatcherServlet'  [] ------
2025-08-21 23:03:33.058|http-nio-6888-exec-1| INFO|org.springframework.web.servlet.DispatcherServlet.initServletBean:547|Completed initialization in 18 ms  [] ------
2025-08-21 23:03:33.476|http-nio-6888-exec-1| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.beforeRequest:23|Before request [client=127.0.0.1, POST /grfp-api/Api/ProjectContract/QueryBidMapHotelInfo]  [68a735450e005d3143a54d04] ------
2025-08-21 23:03:33.875|http-nio-6888-exec-1| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.afterBodyRead:37|request body: {"cityCode":null,"hotelGroupBrandIdList":null,"hotelId":134789,"hotelName":null,"orgId":null,"projectId":113,"pageIndex":1,"pageSize":20}  [68a735450e005d3143a54d04] ------
2025-08-21 23:03:34.687|main| INFO|c.f.grfp.core.cached.impl.CachedCityServiceImpl.getNameMap:55|City Map size en-US, 309420  [] ------
2025-08-21 23:04:00.177|main| INFO|c.f.grfp.core.cached.impl.CachedCityServiceImpl.getNameMap:55|City Map size zh-CN, 309420  [] ------
2025-08-21 23:04:01.178|http-nio-6888-exec-1| INFO|c.f.grfp.core.cached.impl.CachedCityServiceImpl.getNameMap:55|City Map size en-US, 309420  [68a735450e005d3143a54d04] ------
2025-08-21 23:04:07.390|http-nio-6888-exec-1| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.beforeBodyWrite:54|response body: {"code":"200","data":{"bidApplicableDayList":[{"endDate":"Sat Jan 31 00:00:00 CST 2026","priceType":1,"startDate":"Wed Jan 01 00:00:00 CST 2025"}],"bidHotelInfoQueryResponseList":[],"bidState":1,"bidUnApplicableDayList":[{"endDate":"Sun Feb 02 00:00:00 CST 2025","startDate":"Sat Feb 01 00:00:00 CST 2025"}, {"endDate":"Sat Apr 05 00:00:00 CST 2025","startDate":"Sat Apr 05 00:00:00 CST 2025"}, {"endDate":"Sun May 04 00:00:00 CST 2025","startDate":"Fri May 02 00:00:00 CST 2025"}, {"endDate":"Sun Jun 01 00:00:00 CST 2025","startDate":"Sat May 31 00:00:00 CST 2025"}, {"endDate":"Mon Oct 06 00:00:00 CST 2025","startDate":"Thu Oct 02 00:00:00 CST 2025"}],"bidWeight":0,"breakfastNum":"No Breakfast","cityCode":"NKG","cityName":"Nanjing","cityQueryProjectHotelBidStatResponse":{"lastYearAmount":null,"lastYearAmountOrder":null,"lastYearRoomNightCount":null,"lastYearRoomNightOrder":null,"lastYearServicePoint":null,"lastYearServicePointOrder":null,"rating":"4.6","ratingOrder":1},"cityTheSameLevelQueryProjectHotelBidStatResponse":{"lastYearAmount":0,"lastYearAmountOrder":0,"lastYearRoomNightCount":0,"lastYearRoomNightOrder":0,"lastYearServicePoint":null,"lastYearServicePointOrder":null,"rating":"4.6","ratingOrder":0},"currencyCode":"CNY","hotelId":134789,"hotelImageList":null,"hotelName":"Holiday Inn Nanjing Aqua City","hotelPriceLevelInfoVOList":[{"bidHotelPriceGroupList":[{"applicableWeeks":"2,3,4,5,6,7,1","bidHotelPriceList":[{"hotelPriceId":164,"lastOnePersonIncludeTaxPrice":385.93,"lastOnePersonPrice":385.93,"lastOnePersonPriceTaxInfo":null,"lastTwoPersonIncludeTaxPrice":0.00,"lastTwoPersonPrice":0.00,"lastTwoPersonPriceTaxInfo":null,"onePersonIncludeTaxPrice":449.99,"onePersonPrice":385.93,"onePersonPriceTaxInfo":{"basePrice":385.93,"bidPrice":385.93,"taxInfo":{"citytxFeeAmount":0,"citytxFeeIsInclude":0,"citytxFeeType":null,"citytxFeeValue":0.00000,"earlyckFeeIsInclude":0,"earlyckFeeType":null,"earlyckFeeValue":0.00000,"lodgtxFeeAmount":0,"lodgtxFeeIsInclude":0,"lodgtxFeeType":null,"lodgtxFeeValue":0.00000,"occFeeAmount":0,"occFeeIsInclude":0,"occFeeType":null,"occFeeValue":0.00000,"othertx1FeeAmount":0,"othertx1FeeDesc":"NA","othertx1FeeIsInclude":0,"othertx1FeeType":null,"othertx1FeeValue":0.00000,"othertx2FeeAmount":0,"othertx2FeeDesc":null,"othertx2FeeIsInclude":null,"othertx2FeeType":null,"othertx2FeeValue":null,"othertx3FeeAmount":0,"othertx3FeeDesc":null,"othertx3FeeIsInclude":null,"othertx3FeeType":null,"othertx3FeeValue":null,"serviceFeeAmount":38.59,"serviceFeeIsInclude":0,"serviceFeeType":1,"serviceFeeValue":10.00000,"statetxFeeAmount":0,"statetxFeeIsInclude":0,"statetxFeeType":null,"statetxFeeValue":0.00000,"vatgstfbFeeAmount":0,"vatgstfbFeeIsInclude":0,"vatgstfbFeeType":null,"vatgstfbFeeValue":0.00000,"vatgstrmFeeAmount":25.47,"vatgstrmFeeIsInclude":0,"vatgstrmFeeType":1,"vatgstrmFeeValue":6.60000},"totalCostAmount":449.99,"totalIncludeTaxAmount":0,"totalUnIncludeTaxAmount":64.06},"priceType":1,"twoPersonIncludeTaxPrice":0.00,"twoPersonPrice":0.00,"twoPersonPriceTaxInfo":null}],"hotelPriceGroupId":104,"hotelPriceLevelId":126,"isIncludeBreakfast":0,"isLocked":null,"lra":0,"remark":""}],"bigBedRoomCount":null,"currencyCode":"CNY","doubleBedRoomCount":null,"exchangeRate":0.**********,"hotelPriceLevelId":126,"roomLevelNo":1,"roomList":[{"customRoomTypeName":null,"displayOrder":null,"hotelPriceLevelId":null,"priceApplicableRoomId":null,"roomName":"Standard","roomTypeId":null}],"roomNameDesc":"Standard","totalRoomCount":133,"viewCurrencyCode":"USD","viewCurrencyExchangeRate":0.**********}, {"bidHotelPriceGroupList":null,"bigBedRoomCount":null,"currencyCode":"CNY","doubleBedRoomCount":null,"exchangeRate":0.**********,"hotelPriceLevelId":127,"roomLevelNo":2,"roomList":[{"customRoomTypeName":null,"displayOrder":null,"hotelPriceLevelId":null,"priceApplicableRoomId":null,"roomName":"Deluxe","roomTypeId":null}],"roomNameDesc":"Deluxe","totalRoomCount":83,"viewCurrencyCode":"USD","viewCurrencyExchangeRate":0.**********}],"hotelProjectPoiInfoResponseList":[],"hotelServicePoint":null,"hotelStar":"29","hotelStarName":"Quisa Five Star","isInvitedHotel":0,"isRecommendHotel":null,"lastYearHotelPriceLevelInfoVOList":null,"lastYearTotalRoomNight":"--","lastYearTotalSalesAmount":"--","latGoogle":32.**********,"latestYearOrder":0,"latestYearRoomNight":0,"lngGoogle":118.**********,"mainPicUrl":"http:\/\/fcimage.fangcang.com\/images\/hotels\/789\/134789\/202504291745911080113.jpg","minPrice":385.93,"poiDistance":null,"poiName":null,"projectHotelRemarkResponseList":[],"projectIntentHotelId":236,"projectWeight":72.00,"rating":"4.6","relatedProjectId":111,"roomTypeDesc":"Standard","theSameLevelOrder":0,"threeQueryProjectHotelBidStatResponse":{"lastYearAmount":0,"lastYearAmountOrder":0,"lastYearRoomNightCount":0,"lastYearRoomNightOrder":0,"lastYearServicePoint":null,"lastYearServicePointOrder":null,"rating":"4.6","ratingOrder":0},"totalRoomNight100":null,"totalRoomNight100To200":null,"totalRoomNight200To300":null,"totalRoomNight300To400":null,"totalRoomNight400To500":null,"totalRoomNight500":null,"viewCurrencyCode":"USD","viewCurrencyExchangeRate":0.**********},"message":"Succeeded","successful":true}  [68a735450e005d3143a54d04] ------
2025-08-21 23:04:08.461|http-nio-6888-exec-1| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.afterRequest:28|After request [client=127.0.0.1, POST /grfp-api/Api/ProjectContract/QueryBidMapHotelInfo]  [68a735450e005d3143a54d04] ------
2025-08-21 23:04:14.768|http-nio-6888-exec-6| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.beforeRequest:23|Before request [client=127.0.0.1, POST /grfp-api/Api/ProjectContract/QueryBidMapHotelInfo]  [68a7356e0e005d3143a54d05] ------
2025-08-21 23:04:14.825|http-nio-6888-exec-6| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.afterBodyRead:37|request body: {"cityCode":null,"hotelGroupBrandIdList":null,"hotelId":134789,"hotelName":null,"orgId":null,"projectId":113,"pageIndex":1,"pageSize":20}  [68a7356e0e005d3143a54d05] ------
2025-08-21 23:04:19.068|http-nio-6888-exec-6| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.beforeBodyWrite:54|response body: {"code":"200","data":{"bidApplicableDayList":[{"endDate":"Sat Jan 31 00:00:00 CST 2026","priceType":1,"startDate":"Wed Jan 01 00:00:00 CST 2025"}],"bidHotelInfoQueryResponseList":[],"bidState":1,"bidUnApplicableDayList":[{"endDate":"Sun Feb 02 00:00:00 CST 2025","startDate":"Sat Feb 01 00:00:00 CST 2025"}, {"endDate":"Sat Apr 05 00:00:00 CST 2025","startDate":"Sat Apr 05 00:00:00 CST 2025"}, {"endDate":"Sun May 04 00:00:00 CST 2025","startDate":"Fri May 02 00:00:00 CST 2025"}, {"endDate":"Sun Jun 01 00:00:00 CST 2025","startDate":"Sat May 31 00:00:00 CST 2025"}, {"endDate":"Mon Oct 06 00:00:00 CST 2025","startDate":"Thu Oct 02 00:00:00 CST 2025"}],"bidWeight":0,"breakfastNum":"No Breakfast","cityCode":"NKG","cityName":"Nanjing","cityQueryProjectHotelBidStatResponse":{"lastYearAmount":null,"lastYearAmountOrder":null,"lastYearRoomNightCount":null,"lastYearRoomNightOrder":null,"lastYearServicePoint":null,"lastYearServicePointOrder":null,"rating":"4.6","ratingOrder":1},"cityTheSameLevelQueryProjectHotelBidStatResponse":{"lastYearAmount":0,"lastYearAmountOrder":0,"lastYearRoomNightCount":0,"lastYearRoomNightOrder":0,"lastYearServicePoint":null,"lastYearServicePointOrder":null,"rating":"4.6","ratingOrder":0},"currencyCode":"CNY","hotelId":134789,"hotelImageList":null,"hotelName":"Holiday Inn Nanjing Aqua City","hotelPriceLevelInfoVOList":[{"bidHotelPriceGroupList":[{"applicableWeeks":"2,3,4,5,6,7,1","bidHotelPriceList":[{"hotelPriceId":164,"lastOnePersonIncludeTaxPrice":385.93,"lastOnePersonPrice":385.93,"lastOnePersonPriceTaxInfo":null,"lastTwoPersonIncludeTaxPrice":0.00,"lastTwoPersonPrice":0.00,"lastTwoPersonPriceTaxInfo":null,"onePersonIncludeTaxPrice":449.99,"onePersonPrice":385.93,"onePersonPriceTaxInfo":{"basePrice":385.93,"bidPrice":385.93,"taxInfo":{"citytxFeeAmount":0,"citytxFeeIsInclude":0,"citytxFeeType":null,"citytxFeeValue":0.00000,"earlyckFeeIsInclude":0,"earlyckFeeType":null,"earlyckFeeValue":0.00000,"lodgtxFeeAmount":0,"lodgtxFeeIsInclude":0,"lodgtxFeeType":null,"lodgtxFeeValue":0.00000,"occFeeAmount":0,"occFeeIsInclude":0,"occFeeType":null,"occFeeValue":0.00000,"othertx1FeeAmount":0,"othertx1FeeDesc":"NA","othertx1FeeIsInclude":0,"othertx1FeeType":null,"othertx1FeeValue":0.00000,"othertx2FeeAmount":0,"othertx2FeeDesc":null,"othertx2FeeIsInclude":null,"othertx2FeeType":null,"othertx2FeeValue":null,"othertx3FeeAmount":0,"othertx3FeeDesc":null,"othertx3FeeIsInclude":null,"othertx3FeeType":null,"othertx3FeeValue":null,"serviceFeeAmount":38.59,"serviceFeeIsInclude":0,"serviceFeeType":1,"serviceFeeValue":10.00000,"statetxFeeAmount":0,"statetxFeeIsInclude":0,"statetxFeeType":null,"statetxFeeValue":0.00000,"vatgstfbFeeAmount":0,"vatgstfbFeeIsInclude":0,"vatgstfbFeeType":null,"vatgstfbFeeValue":0.00000,"vatgstrmFeeAmount":25.47,"vatgstrmFeeIsInclude":0,"vatgstrmFeeType":1,"vatgstrmFeeValue":6.60000},"totalCostAmount":449.99,"totalIncludeTaxAmount":0,"totalUnIncludeTaxAmount":64.06},"priceType":1,"twoPersonIncludeTaxPrice":0.00,"twoPersonPrice":0.00,"twoPersonPriceTaxInfo":null}],"hotelPriceGroupId":104,"hotelPriceLevelId":126,"isIncludeBreakfast":0,"isLocked":null,"lra":0,"remark":""}],"bigBedRoomCount":null,"currencyCode":"CNY","doubleBedRoomCount":null,"exchangeRate":0.**********,"hotelPriceLevelId":126,"roomLevelNo":1,"roomList":[{"customRoomTypeName":null,"displayOrder":null,"hotelPriceLevelId":null,"priceApplicableRoomId":null,"roomName":"Standard","roomTypeId":null}],"roomNameDesc":"Standard","totalRoomCount":133,"viewCurrencyCode":"USD","viewCurrencyExchangeRate":0.**********}, {"bidHotelPriceGroupList":null,"bigBedRoomCount":null,"currencyCode":"CNY","doubleBedRoomCount":null,"exchangeRate":0.**********,"hotelPriceLevelId":127,"roomLevelNo":2,"roomList":[{"customRoomTypeName":null,"displayOrder":null,"hotelPriceLevelId":null,"priceApplicableRoomId":null,"roomName":"Deluxe","roomTypeId":null}],"roomNameDesc":"Deluxe","totalRoomCount":83,"viewCurrencyCode":"USD","viewCurrencyExchangeRate":0.**********}],"hotelProjectPoiInfoResponseList":[],"hotelServicePoint":null,"hotelStar":"29","hotelStarName":"Quisa Five Star","isInvitedHotel":0,"isRecommendHotel":null,"lastYearHotelPriceLevelInfoVOList":null,"lastYearTotalRoomNight":"--","lastYearTotalSalesAmount":"--","latGoogle":32.**********,"latestYearOrder":0,"latestYearRoomNight":0,"lngGoogle":118.**********,"mainPicUrl":"http:\/\/fcimage.fangcang.com\/images\/hotels\/789\/134789\/202504291745911080113.jpg","minPrice":385.93,"poiDistance":null,"poiName":null,"projectHotelRemarkResponseList":[],"projectIntentHotelId":236,"projectWeight":72.00,"rating":"4.6","relatedProjectId":111,"roomTypeDesc":"Standard","theSameLevelOrder":0,"threeQueryProjectHotelBidStatResponse":{"lastYearAmount":0,"lastYearAmountOrder":0,"lastYearRoomNightCount":0,"lastYearRoomNightOrder":0,"lastYearServicePoint":null,"lastYearServicePointOrder":null,"rating":"4.6","ratingOrder":0},"totalRoomNight100":null,"totalRoomNight100To200":null,"totalRoomNight200To300":null,"totalRoomNight300To400":null,"totalRoomNight400To500":null,"totalRoomNight500":null,"viewCurrencyCode":"USD","viewCurrencyExchangeRate":0.**********},"message":"Succeeded","successful":true}  [68a7356e0e005d3143a54d05] ------
2025-08-21 23:04:19.931|http-nio-6888-exec-6| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.afterRequest:28|After request [client=127.0.0.1, POST /grfp-api/Api/ProjectContract/QueryBidMapHotelInfo]  [68a7356e0e005d3143a54d05] ------
2025-08-21 23:04:21.395|http-nio-6888-exec-5| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.beforeRequest:23|Before request [client=127.0.0.1, POST /grfp-api/Api/ProjectContract/QueryBidMapHotelInfo]  [68a735750e005d3143a54d06] ------
2025-08-21 23:04:21.452|http-nio-6888-exec-5| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.afterBodyRead:37|request body: {"cityCode":null,"hotelGroupBrandIdList":null,"hotelId":134789,"hotelName":null,"orgId":null,"projectId":113,"pageIndex":1,"pageSize":20}  [68a735750e005d3143a54d06] ------
2025-08-21 23:04:26.383|http-nio-6888-exec-5| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.beforeBodyWrite:54|response body: {"code":"200","data":{"bidApplicableDayList":[{"endDate":"Sat Jan 31 00:00:00 CST 2026","priceType":1,"startDate":"Wed Jan 01 00:00:00 CST 2025"}],"bidHotelInfoQueryResponseList":[],"bidState":1,"bidUnApplicableDayList":[{"endDate":"Sun Feb 02 00:00:00 CST 2025","startDate":"Sat Feb 01 00:00:00 CST 2025"}, {"endDate":"Sat Apr 05 00:00:00 CST 2025","startDate":"Sat Apr 05 00:00:00 CST 2025"}, {"endDate":"Sun May 04 00:00:00 CST 2025","startDate":"Fri May 02 00:00:00 CST 2025"}, {"endDate":"Sun Jun 01 00:00:00 CST 2025","startDate":"Sat May 31 00:00:00 CST 2025"}, {"endDate":"Mon Oct 06 00:00:00 CST 2025","startDate":"Thu Oct 02 00:00:00 CST 2025"}],"bidWeight":0,"breakfastNum":"No Breakfast","cityCode":"NKG","cityName":"Nanjing","cityQueryProjectHotelBidStatResponse":{"lastYearAmount":null,"lastYearAmountOrder":null,"lastYearRoomNightCount":null,"lastYearRoomNightOrder":null,"lastYearServicePoint":null,"lastYearServicePointOrder":null,"rating":"4.6","ratingOrder":1},"cityTheSameLevelQueryProjectHotelBidStatResponse":{"lastYearAmount":0,"lastYearAmountOrder":0,"lastYearRoomNightCount":0,"lastYearRoomNightOrder":0,"lastYearServicePoint":null,"lastYearServicePointOrder":null,"rating":"4.6","ratingOrder":0},"currencyCode":"CNY","hotelId":134789,"hotelImageList":null,"hotelName":"Holiday Inn Nanjing Aqua City","hotelPriceLevelInfoVOList":[{"bidHotelPriceGroupList":[{"applicableWeeks":"2,3,4,5,6,7,1","bidHotelPriceList":[{"hotelPriceId":164,"lastOnePersonIncludeTaxPrice":385.93,"lastOnePersonPrice":385.93,"lastOnePersonPriceTaxInfo":null,"lastTwoPersonIncludeTaxPrice":0.00,"lastTwoPersonPrice":0.00,"lastTwoPersonPriceTaxInfo":null,"onePersonIncludeTaxPrice":449.99,"onePersonPrice":385.93,"onePersonPriceTaxInfo":{"basePrice":385.93,"bidPrice":385.93,"taxInfo":{"citytxFeeAmount":0,"citytxFeeIsInclude":0,"citytxFeeType":null,"citytxFeeValue":0.00000,"earlyckFeeIsInclude":0,"earlyckFeeType":null,"earlyckFeeValue":0.00000,"lodgtxFeeAmount":0,"lodgtxFeeIsInclude":0,"lodgtxFeeType":null,"lodgtxFeeValue":0.00000,"occFeeAmount":0,"occFeeIsInclude":0,"occFeeType":null,"occFeeValue":0.00000,"othertx1FeeAmount":0,"othertx1FeeDesc":"NA","othertx1FeeIsInclude":0,"othertx1FeeType":null,"othertx1FeeValue":0.00000,"othertx2FeeAmount":0,"othertx2FeeDesc":null,"othertx2FeeIsInclude":null,"othertx2FeeType":null,"othertx2FeeValue":null,"othertx3FeeAmount":0,"othertx3FeeDesc":null,"othertx3FeeIsInclude":null,"othertx3FeeType":null,"othertx3FeeValue":null,"serviceFeeAmount":38.59,"serviceFeeIsInclude":0,"serviceFeeType":1,"serviceFeeValue":10.00000,"statetxFeeAmount":0,"statetxFeeIsInclude":0,"statetxFeeType":null,"statetxFeeValue":0.00000,"vatgstfbFeeAmount":0,"vatgstfbFeeIsInclude":0,"vatgstfbFeeType":null,"vatgstfbFeeValue":0.00000,"vatgstrmFeeAmount":25.47,"vatgstrmFeeIsInclude":0,"vatgstrmFeeType":1,"vatgstrmFeeValue":6.60000},"totalCostAmount":449.99,"totalIncludeTaxAmount":0,"totalUnIncludeTaxAmount":64.06},"priceType":1,"twoPersonIncludeTaxPrice":0.00,"twoPersonPrice":0.00,"twoPersonPriceTaxInfo":null}],"hotelPriceGroupId":104,"hotelPriceLevelId":126,"isIncludeBreakfast":0,"isLocked":null,"lra":0,"remark":""}],"bigBedRoomCount":null,"currencyCode":"CNY","doubleBedRoomCount":null,"exchangeRate":0.**********,"hotelPriceLevelId":126,"roomLevelNo":1,"roomList":[{"customRoomTypeName":null,"displayOrder":null,"hotelPriceLevelId":null,"priceApplicableRoomId":null,"roomName":"Standard","roomTypeId":null}],"roomNameDesc":"Standard","totalRoomCount":133,"viewCurrencyCode":"USD","viewCurrencyExchangeRate":0.**********}, {"bidHotelPriceGroupList":null,"bigBedRoomCount":null,"currencyCode":"CNY","doubleBedRoomCount":null,"exchangeRate":0.**********,"hotelPriceLevelId":127,"roomLevelNo":2,"roomList":[{"customRoomTypeName":null,"displayOrder":null,"hotelPriceLevelId":null,"priceApplicableRoomId":null,"roomName":"Deluxe","roomTypeId":null}],"roomNameDesc":"Deluxe","totalRoomCount":83,"viewCurrencyCode":"USD","viewCurrencyExchangeRate":0.**********}],"hotelProjectPoiInfoResponseList":[],"hotelServicePoint":null,"hotelStar":"29","hotelStarName":"Quisa Five Star","isInvitedHotel":0,"isRecommendHotel":null,"lastYearHotelPriceLevelInfoVOList":null,"lastYearTotalRoomNight":"--","lastYearTotalSalesAmount":"--","latGoogle":32.**********,"latestYearOrder":0,"latestYearRoomNight":0,"lngGoogle":118.**********,"mainPicUrl":"http:\/\/fcimage.fangcang.com\/images\/hotels\/789\/134789\/202504291745911080113.jpg","minPrice":385.93,"poiDistance":null,"poiName":null,"projectHotelRemarkResponseList":[],"projectIntentHotelId":236,"projectWeight":72.00,"rating":"4.6","relatedProjectId":111,"roomTypeDesc":"Standard","theSameLevelOrder":0,"threeQueryProjectHotelBidStatResponse":{"lastYearAmount":0,"lastYearAmountOrder":0,"lastYearRoomNightCount":0,"lastYearRoomNightOrder":0,"lastYearServicePoint":null,"lastYearServicePointOrder":null,"rating":"4.6","ratingOrder":0},"totalRoomNight100":null,"totalRoomNight100To200":null,"totalRoomNight200To300":null,"totalRoomNight300To400":null,"totalRoomNight400To500":null,"totalRoomNight500":null,"viewCurrencyCode":"USD","viewCurrencyExchangeRate":0.**********},"message":"Succeeded","successful":true}  [68a735750e005d3143a54d06] ------
2025-08-21 23:04:27.521|http-nio-6888-exec-5| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.afterRequest:28|After request [client=127.0.0.1, POST /grfp-api/Api/ProjectContract/QueryBidMapHotelInfo]  [68a735750e005d3143a54d06] ------
2025-08-21 23:04:41.379|http-nio-6888-exec-7| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.beforeRequest:23|Before request [client=127.0.0.1, POST /grfp-api/Api/ProjectContract/QueryBidMapHotelInfo]  [68a735890e005d3143a54d07] ------
2025-08-21 23:04:41.435|http-nio-6888-exec-7| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.afterBodyRead:37|request body: {"cityCode":null,"hotelGroupBrandIdList":null,"hotelId":134789,"hotelName":null,"orgId":null,"projectId":113,"pageIndex":1,"pageSize":20}  [68a735890e005d3143a54d07] ------
2025-08-21 23:04:45.513|http-nio-6888-exec-7| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.beforeBodyWrite:54|response body: {"code":"200","data":{"bidApplicableDayList":[{"endDate":"Sat Jan 31 00:00:00 CST 2026","priceType":1,"startDate":"Wed Jan 01 00:00:00 CST 2025"}],"bidHotelInfoQueryResponseList":[],"bidState":1,"bidUnApplicableDayList":[{"endDate":"Sun Feb 02 00:00:00 CST 2025","startDate":"Sat Feb 01 00:00:00 CST 2025"}, {"endDate":"Sat Apr 05 00:00:00 CST 2025","startDate":"Sat Apr 05 00:00:00 CST 2025"}, {"endDate":"Sun May 04 00:00:00 CST 2025","startDate":"Fri May 02 00:00:00 CST 2025"}, {"endDate":"Sun Jun 01 00:00:00 CST 2025","startDate":"Sat May 31 00:00:00 CST 2025"}, {"endDate":"Mon Oct 06 00:00:00 CST 2025","startDate":"Thu Oct 02 00:00:00 CST 2025"}],"bidWeight":0,"breakfastNum":"No Breakfast","cityCode":"NKG","cityName":"Nanjing","cityQueryProjectHotelBidStatResponse":{"lastYearAmount":null,"lastYearAmountOrder":null,"lastYearRoomNightCount":null,"lastYearRoomNightOrder":null,"lastYearServicePoint":null,"lastYearServicePointOrder":null,"rating":"4.6","ratingOrder":1},"cityTheSameLevelQueryProjectHotelBidStatResponse":{"lastYearAmount":0,"lastYearAmountOrder":0,"lastYearRoomNightCount":0,"lastYearRoomNightOrder":0,"lastYearServicePoint":null,"lastYearServicePointOrder":null,"rating":"4.6","ratingOrder":0},"currencyCode":"CNY","hotelId":134789,"hotelImageList":null,"hotelName":"Holiday Inn Nanjing Aqua City","hotelPriceLevelInfoVOList":[{"bidHotelPriceGroupList":[{"applicableWeeks":"2,3,4,5,6,7,1","bidHotelPriceList":[{"hotelPriceId":164,"lastOnePersonIncludeTaxPrice":385.93,"lastOnePersonPrice":385.93,"lastOnePersonPriceTaxInfo":null,"lastTwoPersonIncludeTaxPrice":0.00,"lastTwoPersonPrice":0.00,"lastTwoPersonPriceTaxInfo":null,"onePersonIncludeTaxPrice":449.99,"onePersonPrice":385.93,"onePersonPriceTaxInfo":{"basePrice":385.93,"bidPrice":385.93,"taxInfo":{"citytxFeeAmount":0,"citytxFeeIsInclude":0,"citytxFeeType":null,"citytxFeeValue":0.00000,"earlyckFeeIsInclude":0,"earlyckFeeType":null,"earlyckFeeValue":0.00000,"lodgtxFeeAmount":0,"lodgtxFeeIsInclude":0,"lodgtxFeeType":null,"lodgtxFeeValue":0.00000,"occFeeAmount":0,"occFeeIsInclude":0,"occFeeType":null,"occFeeValue":0.00000,"othertx1FeeAmount":0,"othertx1FeeDesc":"NA","othertx1FeeIsInclude":0,"othertx1FeeType":null,"othertx1FeeValue":0.00000,"othertx2FeeAmount":0,"othertx2FeeDesc":null,"othertx2FeeIsInclude":null,"othertx2FeeType":null,"othertx2FeeValue":null,"othertx3FeeAmount":0,"othertx3FeeDesc":null,"othertx3FeeIsInclude":null,"othertx3FeeType":null,"othertx3FeeValue":null,"serviceFeeAmount":38.59,"serviceFeeIsInclude":0,"serviceFeeType":1,"serviceFeeValue":10.00000,"statetxFeeAmount":0,"statetxFeeIsInclude":0,"statetxFeeType":null,"statetxFeeValue":0.00000,"vatgstfbFeeAmount":0,"vatgstfbFeeIsInclude":0,"vatgstfbFeeType":null,"vatgstfbFeeValue":0.00000,"vatgstrmFeeAmount":25.47,"vatgstrmFeeIsInclude":0,"vatgstrmFeeType":1,"vatgstrmFeeValue":6.60000},"totalCostAmount":449.99,"totalIncludeTaxAmount":0,"totalUnIncludeTaxAmount":64.06},"priceType":1,"twoPersonIncludeTaxPrice":0.00,"twoPersonPrice":0.00,"twoPersonPriceTaxInfo":null}],"hotelPriceGroupId":104,"hotelPriceLevelId":126,"isIncludeBreakfast":0,"isLocked":null,"lra":0,"remark":""}],"bigBedRoomCount":null,"currencyCode":"CNY","doubleBedRoomCount":null,"exchangeRate":0.**********,"hotelPriceLevelId":126,"roomLevelNo":1,"roomList":[{"customRoomTypeName":null,"displayOrder":null,"hotelPriceLevelId":null,"priceApplicableRoomId":null,"roomName":"Standard","roomTypeId":null}],"roomNameDesc":"Standard","totalRoomCount":133,"viewCurrencyCode":"USD","viewCurrencyExchangeRate":0.**********}, {"bidHotelPriceGroupList":null,"bigBedRoomCount":null,"currencyCode":"CNY","doubleBedRoomCount":null,"exchangeRate":0.**********,"hotelPriceLevelId":127,"roomLevelNo":2,"roomList":[{"customRoomTypeName":null,"displayOrder":null,"hotelPriceLevelId":null,"priceApplicableRoomId":null,"roomName":"Deluxe","roomTypeId":null}],"roomNameDesc":"Deluxe","totalRoomCount":83,"viewCurrencyCode":"USD","viewCurrencyExchangeRate":0.**********}],"hotelProjectPoiInfoResponseList":[],"hotelServicePoint":null,"hotelStar":"29","hotelStarName":"Quisa Five Star","isInvitedHotel":0,"isRecommendHotel":null,"lastYearHotelPriceLevelInfoVOList":null,"lastYearTotalRoomNight":"--","lastYearTotalSalesAmount":"--","latGoogle":32.**********,"latestYearOrder":0,"latestYearRoomNight":0,"lngGoogle":118.**********,"mainPicUrl":"http:\/\/fcimage.fangcang.com\/images\/hotels\/789\/134789\/202504291745911080113.jpg","minPrice":385.93,"poiDistance":null,"poiName":null,"projectHotelRemarkResponseList":[],"projectIntentHotelId":236,"projectWeight":72.00,"rating":"4.6","relatedProjectId":111,"roomTypeDesc":"Standard","theSameLevelOrder":0,"threeQueryProjectHotelBidStatResponse":{"lastYearAmount":0,"lastYearAmountOrder":0,"lastYearRoomNightCount":0,"lastYearRoomNightOrder":0,"lastYearServicePoint":null,"lastYearServicePointOrder":null,"rating":"4.6","ratingOrder":0},"totalRoomNight100":null,"totalRoomNight100To200":null,"totalRoomNight200To300":null,"totalRoomNight300To400":null,"totalRoomNight400To500":null,"totalRoomNight500":null,"viewCurrencyCode":"USD","viewCurrencyExchangeRate":0.**********},"message":"Succeeded","successful":true}  [68a735890e005d3143a54d07] ------
2025-08-21 23:04:46.450|http-nio-6888-exec-7| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.afterRequest:28|After request [client=127.0.0.1, POST /grfp-api/Api/ProjectContract/QueryBidMapHotelInfo]  [68a735890e005d3143a54d07] ------
2025-08-21 23:06:22.098|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStarting:55|Starting GRfpApiApplication on LAPTOP-BVT1ALEM with PID 9092 (D:\RFP\grfp-project\grfp-api\target\classes started by ShamGod in D:\RFP\grfp-project)  [] ------
2025-08-21 23:06:22.100|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStartupProfileInfo:652|The following profiles are active: dev  [] ------
2025-08-21 23:06:23.728|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:249|Multiple Spring Data modules found, entering strict repository configuration mode!  [] ------
2025-08-21 23:06:23.731|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:127|Bootstrapping Spring Data Redis repositories in DEFAULT mode.  [] ------
2025-08-21 23:06:23.792|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:187|Finished Spring Data repository scanning in 37ms. Found 0 Redis repository interfaces.  [] ------
2025-08-21 23:06:25.183|main| INFO|o.s.boot.web.embedded.tomcat.TomcatWebServer.initialize:108|Tomcat initialized with port(s): 6888 (http)  [] ------
2025-08-21 23:06:25.198|main| INFO|org.apache.catalina.core.StandardService.log:173|Starting service [Tomcat]  [] ------
2025-08-21 23:06:25.199|main| INFO|org.apache.catalina.core.StandardEngine.log:173|Starting Servlet engine: [Apache Tomcat/9.0.45]  [] ------
2025-08-21 23:06:25.369|main| INFO|o.a.c.c.C.[Tomcat].[localhost].[/grfp-api].log:173|Initializing Spring embedded WebApplicationContext  [] ------
2025-08-21 23:06:25.369|main| INFO|o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:285|Root WebApplicationContext: initialization completed in 3188 ms  [] ------
2025-08-21 23:06:25.686|main| INFO|org.redisson.Version.logVersion:41|Redisson 3.16.0  [] ------
2025-08-21 23:06:27.995|redisson-netty-2-25| INFO|o.r.connection.pool.MasterPubSubConnectionPool.lambda$run$0:166|1 connections initialized for *************/*************:6379  [] ------
2025-08-21 23:06:29.511|redisson-netty-2-19| INFO|org.redisson.connection.pool.MasterConnectionPool.lambda$run$0:166|24 connections initialized for *************/*************:6379  [] ------
2025-08-21 23:06:33.234|main| INFO|com.fangcang.grfp.api.config.OssAutoConfiguration.ossClient:34|---------------Init OssClient ----------------  [] ------
2025-08-21 23:06:34.571|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'queryBidHotelInfoExecutor'  [] ------
2025-08-21 23:06:35.006|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'hotelHexagonGenerateExecutor'  [] ------
2025-08-21 23:06:36.082|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'auditLogExecutor'  [] ------
2025-08-21 23:06:36.541|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'generalExecutor'  [] ------
2025-08-21 23:06:36.542|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'syncGlinkExecutor'  [] ------
2025-08-21 23:06:36.622|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'rfpCommonExecutor'  [] ------
2025-08-21 23:06:37.849|main| INFO|o.s.boot.web.embedded.tomcat.TomcatWebServer.start:220|Tomcat started on port(s): 6888 (http) with context path '/grfp-api'  [] ------
2025-08-21 23:06:41.103|main| INFO|o.s.s.a.ScheduledAnnotationBeanPostProcessor.finishRegistration:309|No TaskScheduler/ScheduledExecutorService bean found for scheduled processing  [] ------
2025-08-21 23:06:41.712|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStarted:61|Started GRfpApiApplication in 20.64 seconds (JVM running for 22.888)  [] ------
2025-08-21 23:06:41.722|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.afterStarted:59|==============================GrfpApi 系统启动完成v1.0.0-20250808===========================================  [] ------
2025-08-21 23:06:41.940|main| INFO|com.zaxxer.hikari.HikariDataSource.getConnection:110|HikariPool-1 - Starting...  [] ------
2025-08-21 23:06:43.842|main| INFO|com.zaxxer.hikari.HikariDataSource.getConnection:123|HikariPool-1 - Start completed.  [] ------
2025-08-21 23:06:45.425|main| INFO|c.f.g.core.cached.impl.CachedHotelBrandServiceImpl.getNameMap:49|HotelBrandNameMap size en-US, 9926  [] ------
2025-08-21 23:06:46.465|main| INFO|c.f.g.core.cached.impl.CachedHotelBrandServiceImpl.getNameMap:49|HotelBrandNameMap size zh-CN, 9926  [] ------
2025-08-21 23:06:46.506|http-nio-6888-exec-1| INFO|o.a.c.c.C.[Tomcat].[localhost].[/grfp-api].log:173|Initializing Spring DispatcherServlet 'dispatcherServlet'  [] ------
2025-08-21 23:06:46.507|http-nio-6888-exec-1| INFO|org.springframework.web.servlet.DispatcherServlet.initServletBean:525|Initializing Servlet 'dispatcherServlet'  [] ------
2025-08-21 23:06:46.523|http-nio-6888-exec-1| INFO|org.springframework.web.servlet.DispatcherServlet.initServletBean:547|Completed initialization in 16 ms  [] ------
2025-08-21 23:06:46.901|http-nio-6888-exec-1| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.beforeRequest:23|Before request [client=127.0.0.1, POST /grfp-api/Api/ProjectContract/QueryHotelBidDetail]  [68a736060e00564a656786f8] ------
2025-08-21 23:06:47.412|main| INFO|c.f.g.core.cached.impl.CachedHotelGroupServiceImpl.getNameMap:60|HotelGroupNameMap size en-US, 8123  [] ------
2025-08-21 23:06:47.499|http-nio-6888-exec-1| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.afterBodyRead:37|request body: {"hotelId":0,"projectId":0,"projectIntentHotelId":0}  [68a736060e00564a656786f8] ------
2025-08-21 23:06:48.278|main| INFO|c.f.g.core.cached.impl.CachedHotelGroupServiceImpl.getNameMap:60|HotelGroupNameMap size zh-CN, 8123  [] ------
2025-08-21 23:06:48.341|http-nio-6888-exec-1| INFO|c.f.g.c.cached.impl.CachedTextResourceServiceImpl.getMsgValue:65|missTextResource msg: BID_NOT_EXIST  [68a736060e00564a656786f8] ------
2025-08-21 23:06:48.368|http-nio-6888-exec-1| INFO|c.fangcang.grfp.core.util.ControllerLoggingUtility.logFinish:101|POST /Api/ProjectContract/QueryHotelBidDetail FINISH in -1ms returning {"code":"500","data":null,"message":"[BID_NOT_EXIST]","successful":false}  [68a736060e00564a656786f8] ------
2025-08-21 23:06:48.417|http-nio-6888-exec-1| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.beforeBodyWrite:54|response body: {"code":"500","data":null,"message":"[BID_NOT_EXIST]","successful":false}  [] ------
2025-08-21 23:06:49.954|http-nio-6888-exec-1| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.afterRequest:28|After request [client=127.0.0.1, POST /grfp-api/Api/ProjectContract/QueryHotelBidDetail]  [] ------
2025-08-21 23:07:01.397|main| INFO|c.f.grfp.core.cached.impl.CachedCityServiceImpl.getNameMap:55|City Map size en-US, 309420  [] ------
2025-08-21 23:07:15.401|main| INFO|c.f.grfp.core.cached.impl.CachedCityServiceImpl.getNameMap:55|City Map size zh-CN, 309420  [] ------
2025-08-21 23:07:54.961|http-nio-6888-exec-3| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.beforeRequest:23|Before request [client=127.0.0.1, POST /grfp-api/Api/ProjectContract/QueryHotelBidDetail]  [68a7364a0e00564a656786f9] ------
2025-08-21 23:07:55.020|http-nio-6888-exec-3| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.afterBodyRead:37|request body: {"hotelId":null,"projectId":null,"projectIntentHotelId":236}  [68a7364a0e00564a656786f9] ------
2025-08-21 23:07:59.449|http-nio-6888-exec-3| INFO|c.f.g.core.remote.tmchub.manager.TmcHubApiManager.executeApiRequest:184|DHUB API Response: queryHotelImage, Request Body: {"businessRequest":{"hotelIds":[134789]},"header":{"partnerCode":"TP210000850","requestType":"queryHotelImage","signature":"A9F018DA28DE7B180A69292C4733A89F","timestamp":1755788878418,"version":"1.0.0"}}  [68a7364a0e00564a656786f9] ------
2025-08-21 23:08:03.510|http-nio-6888-exec-3| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.beforeBodyWrite:54|response body: {"code":"200","data":{"bidApplicableDayList":[{"endDate":"Sat Jan 31 00:00:00 CST 2026","priceType":1,"startDate":"Wed Jan 01 00:00:00 CST 2025"}],"bidCustomStrategyList":[{"customTendStrategyId":152,"options":null,"projectId":113,"projectIntentHotelGroupId":null,"strategyName":"2323","strategyType":2,"supportStrategyName":0,"supportStrategyText":"Y"}],"bidHotelPriceLevelInfoVOList":null,"bidHotelTaxSettings":{"citytxFeeIsInclude":0,"citytxFeeType":null,"citytxFeeValue":0.00000,"earlyckFeeIsInclude":0,"earlyckFeeType":null,"earlyckFeeValue":0.00000,"lodgtxFeeIsInclude":0,"lodgtxFeeType":null,"lodgtxFeeValue":0.00000,"occFeeIsInclude":0,"occFeeType":null,"occFeeValue":0.00000,"othertx1FeeDesc":"NA","othertx1FeeIsInclude":0,"othertx1FeeType":null,"othertx1FeeValue":0.00000,"othertx2FeeDesc":null,"othertx2FeeIsInclude":null,"othertx2FeeType":null,"othertx2FeeValue":null,"othertx3FeeDesc":null,"othertx3FeeIsInclude":null,"othertx3FeeType":null,"othertx3FeeValue":null,"serviceFeeIsInclude":0,"serviceFeeType":1,"serviceFeeValue":10.00000,"statetxFeeIsInclude":0,"statetxFeeType":null,"statetxFeeValue":0.00000,"vatgstfbFeeIsInclude":0,"vatgstfbFeeType":null,"vatgstfbFeeValue":0.00000,"vatgstrmFeeIsInclude":0,"vatgstrmFeeType":1,"vatgstrmFeeValue":6.60000},"bidOperateLogList":[{"bidOperateLogId":145,"hotelId":134789,"operateContent":"BID_STATUS_UPDATE_TO_NEW_BID","operateTime":"Wed Aug 13 00:36:04 CST 2025","operator":"\u9996\u65C5\u5982\u5BB6","orgTypeId":4,"projectId":113,"projectIntentHotelId":236}],"bidProjectStrategy":{"commission":null,"doAfterLateReserveTime":null,"hasCommission":null,"isIncludeBreakfast":null,"lateReserveTime":null,"supportCancelDay":0,"supportCancelTime":"18:00","supportCheckinInfo":null,"supportIncludeTaxService":null,"supportNoGuarantee":null,"supportPayAtHotel":null,"supportPayEarlyCheckout":null,"supportVccPay":null,"supportWifi":1},"bidUnApplicableDayList":[{"endDate":"Sun Feb 02 00:00:00 CST 2025","startDate":"Sat Feb 01 00:00:00 CST 2025"}, {"endDate":"Sat Apr 05 00:00:00 CST 2025","startDate":"Sat Apr 05 00:00:00 CST 2025"}, {"endDate":"Sun May 04 00:00:00 CST 2025","startDate":"Fri May 02 00:00:00 CST 2025"}, {"endDate":"Sun Jun 01 00:00:00 CST 2025","startDate":"Sat May 31 00:00:00 CST 2025"}, {"endDate":"Mon Oct 06 00:00:00 CST 2025","startDate":"Thu Oct 02 00:00:00 CST 2025"}],"currencyCode":"CNY","hotelId":134789,"hotelImageList":[Image(imageId=92305280, imageType=2, isMain=0, url=http://fcimage.fangcang.com/images/hotels/789/134789/202504291745911116844.jpg), Image(imageId=92305281, imageType=2, isMain=0, url=http://fcimage.fangcang.com/images/hotels/789/134789/202504291745911117305.jpg), Image(imageId=92305282, imageType=5, isMain=0, url=http://fcimage.fangcang.com/images/hotels/789/134789/202504291745911117609.jpg), Image(imageId=92305283, imageType=5, isMain=0, url=http://fcimage.fangcang.com/images/hotels/789/134789/202504291745911118220.jpg), Image(imageId=92305284, imageType=5, isMain=0, url=http://fcimage.fangcang.com/images/hotels/789/134789/202504291745911118613.jpg), Image(imageId=92305285, imageType=5, isMain=0, url=http://fcimage.fangcang.com/images/hotels/789/134789/202504291745911118999.jpg), Image(imageId=92305286, imageType=5, isMain=0, url=http://fcimage.fangcang.com/images/hotels/789/134789/202504291745911119312.jpg), Image(imageId=92305287, imageType=2, isMain=0, url=http://fcimage.fangcang.com/images/hotels/789/134789/202504291745911119615.jpg), Image(imageId=92305288, imageType=2, isMain=0, url=http://fcimage.fangcang.com/images/hotels/789/134789/202504291745911119761.jpg), Image(imageId=92305289, imageType=2, isMain=0, url=http://fcimage.fangcang.com/images/hotels/789/134789/202504291745911119897.jpg), Image(imageId=92305290, imageType=2, isMain=0, url=http://fcimage.fangcang.com/images/hotels/789/134789/202504291745911120365.jpg), Image(imageId=92305291, imageType=2, isMain=0, url=http://fcimage.fangcang.com/images/hotels/789/134789/202504291745911120516.jpg), Image(imageId=92305292, imageType=2, isMain=0, url=http://fcimage.fangcang.com/images/hotels/789/134789/202504291745911120816.jpg), Image(imageId=92305293, imageType=2, isMain=0, url=http://fcimage.fangcang.com/images/hotels/789/134789/202504291745911121018.jpg), Image(imageId=92305294, imageType=2, isMain=0, url=http://fcimage.fangcang.com/images/hotels/789/134789/202504291745911121392.jpg), Image(imageId=92305295, imageType=2, isMain=0, url=http://fcimage.fangcang.com/images/hotels/789/134789/202504291745911121709.jpg), Image(imageId=92305296, imageType=2, isMain=0, url=http://fcimage.fangcang.com/images/hotels/789/134789/202504291745911122093.jpg), Image(imageId=92305297, imageType=5, isMain=0, url=http://fcimage.fangcang.com/images/hotels/789/134789/202504291745911122448.jpg), Image(imageId=92305298, imageType=5, isMain=0, url=http://fcimage.fangcang.com/images/hotels/789/134789/202504291745911122631.jpg), Image(imageId=92305299, imageType=5, isMain=0, url=http://fcimage.fangcang.com/images/hotels/789/134789/202504291745911123101.jpg), Image(imageId=92305300, imageType=5, isMain=0, url=http://fcimage.fangcang.com/images/hotels/789/134789/202504291745911123491.jpg), Image(imageId=92305301, imageType=5, isMain=0, url=http://fcimage.fangcang.com/images/hotels/789/134789/202504291745911123824.jpg), Image(imageId=92305302, imageType=5, isMain=0, url=http://fcimage.fangcang.com/images/hotels/789/134789/202504291745911123985.jpg), Image(imageId=92305303, imageType=5, isMain=0, url=http://fcimage.fangcang.com/images/hotels/789/134789/202504291745911124266.jpg), Image(imageId=92305304, imageType=5, isMain=0, url=http://fcimage.fangcang.com/images/hotels/789/134789/202504291745911124570.jpg), Image(imageId=92305305, imageType=5, isMain=0, url=http://fcimage.fangcang.com/images/hotels/789/134789/202504291745911124992.jpg), Image(imageId=1274534, imageType=5, isMain=1, url=http://fcimage.fangcang.com/images/hotels/789/134789/202504291745911080113.jpg), Image(imageId=92305266, imageType=2, isMain=0, url=http://fcimage.fangcang.com/images/hotels/789/134789/202504291745911110215.jpg), Image(imageId=92305267, imageType=2, isMain=0, url=http://fcimage.fangcang.com/images/hotels/789/134789/202504291745911112123.jpg), Image(imageId=92305268, imageType=2, isMain=0, url=http://fcimage.fangcang.com/images/hotels/789/134789/202504291745911112762.jpg), Image(imageId=92305269, imageType=2, isMain=0, url=http://fcimage.fangcang.com/images/hotels/789/134789/202504291745911112952.jpg), Image(imageId=92305270, imageType=2, isMain=0, url=http://fcimage.fangcang.com/images/hotels/789/134789/202504291745911113531.jpg), Image(imageId=92305271, imageType=5, isMain=0, url=http://fcimage.fangcang.com/images/hotels/789/134789/202504291745911113885.jpg), Image(imageId=92305272, imageType=5, isMain=0, url=http://fcimage.fangcang.com/images/hotels/789/134789/202504291745911114207.jpg), Image(imageId=92305273, imageType=5, isMain=0, url=http://fcimage.fangcang.com/images/hotels/789/134789/202504291745911114482.jpg), Image(imageId=92305274, imageType=5, isMain=0, url=http://fcimage.fangcang.com/images/hotels/789/134789/202504291745911114820.jpg), Image(imageId=92305275, imageType=5, isMain=0, url=http://fcimage.fangcang.com/images/hotels/789/134789/202504291745911115114.jpg), Image(imageId=92305276, imageType=2, isMain=0, url=http://fcimage.fangcang.com/images/hotels/789/134789/202504291745911115411.jpg), Image(imageId=92305277, imageType=2, isMain=0, url=http://fcimage.fangcang.com/images/hotels/789/134789/202504291745911115577.jpg), Image(imageId=92305278, imageType=2, isMain=0, url=http://fcimage.fangcang.com/images/hotels/789/134789/202504291745911115735.jpg), Image(imageId=92305279, imageType=2, isMain=0, url=http://fcimage.fangcang.com/images/hotels/789/134789/202504291745911116446.jpg)],"hotelInfo":{"cityCode":"NKG","cityName":null,"countryCode":"CN","countryName":null,"currencyCode":"CNY","fitmentDate":null,"hotelAddress":"No. 1 Jiankang Road","hotelBrandId":10342,"hotelBrandName":"Holiday Inn","hotelGroupId":30060,"hotelId":134789,"hotelName":"Holiday Inn Nanjing Aqua City","hotelStar":"29","hotelStarName":"Quisa Five Star","isActive":1,"latGoogle":32.**********,"lngGoogle":118.**********,"openingDate":null,"provinceCode":"JSU","provinceName":null,"rating":"4.6","roomCount":313,"telephone":"+86-25-82233888"},"isLanyonImport":1,"lanyonImportDataCount":0,"mainPicUrl":"http:\/\/fcimage.fangcang.com\/images\/hotels\/789\/134789\/202504291745911080113.jpg","poiDistance":null,"poiName":null,"projectCustomTendStrategyList":[{"displayOrder":1,"id":114,"options":null,"strategyName":"\u81EA\u5B9A\u4E491","strategyType":1,"supportStrategyName":0}, {"displayOrder":2,"id":115,"options":null,"strategyName":"\u81EA\u5B9A\u4E49\u6587\u672C2","strategyType":2,"supportStrategyName":0}, {"displayOrder":3,"id":124,"options":null,"strategyName":"111","strategyType":1,"supportStrategyName":0}, {"displayOrder":4,"id":125,"options":null,"strategyName":"333","strategyType":1,"supportStrategyName":0}, {"displayOrder":5,"id":126,"options":null,"strategyName":"11","strategyType":1,"supportStrategyName":0}, {"displayOrder":6,"id":127,"options":null,"strategyName":"3333","strategyType":2,"supportStrategyName":0}, {"displayOrder":7,"id":128,"options":null,"strategyName":"3333111","strategyType":1,"supportStrategyName":0}, {"displayOrder":8,"id":129,"options":null,"strategyName":"12121212","strategyType":2,"supportStrategyName":0}, {"displayOrder":9,"id":130,"options":null,"strategyName":"31313131","strategyType":2,"supportStrategyName":0}, {"displayOrder":10,"id":131,"options":null,"strategyName":"3313131","strategyType":2,"supportStrategyName":0}, {"displayOrder":11,"id":132,"options":null,"strategyName":"1212121","strategyType":2,"supportStrategyName":0}, {"displayOrder":12,"id":133,"options":null,"strategyName":"1212121131313","strategyType":1,"supportStrategyName":0}, {"displayOrder":13,"id":134,"options":null,"strategyName":"1313131313","strategyType":2,"supportStrategyName":0}, {"displayOrder":14,"id":135,"options":null,"strategyName":"13131313131313","strategyType":2,"supportStrategyName":0}, {"displayOrder":15,"id":136,"options":null,"strategyName":"13131313131","strategyType":1,"supportStrategyName":0}, {"displayOrder":16,"id":137,"options":null,"strategyName":"13131313131111","strategyType":1,"supportStrategyName":0}, {"displayOrder":17,"id":138,"options":null,"strategyName":"3434343434","strategyType":2,"supportStrategyName":0}, {"displayOrder":18,"id":139,"options":null,"strategyName":"121212","strategyType":2,"supportStrategyName":0}, {"displayOrder":19,"id":140,"options":null,"strategyName":"131313","strategyType":1,"supportStrategyName":0}, {"displayOrder":20,"id":141,"options":null,"strategyName":"1313131","strategyType":2,"supportStrategyName":0}, {"displayOrder":21,"id":142,"options":null,"strategyName":"13131313","strategyType":1,"supportStrategyName":0}, {"displayOrder":22,"id":143,"options":null,"strategyName":"1313131131313","strategyType":2,"supportStrategyName":0}, {"displayOrder":23,"id":144,"options":null,"strategyName":"bbbbb","strategyType":2,"supportStrategyName":0}, {"displayOrder":24,"id":145,"options":null,"strategyName":"121212a","strategyType":2,"supportStrategyName":0}, {"displayOrder":25,"id":146,"options":null,"strategyName":"12121","strategyType":2,"supportStrategyName":0}, {"displayOrder":26,"id":147,"options":null,"strategyName":"bbbb","strategyType":2,"supportStrategyName":0}, {"displayOrder":27,"id":148,"options":null,"strategyName":"vvvv","strategyType":2,"supportStrategyName":0}, {"displayOrder":28,"id":149,"options":null,"strategyName":"121212331313","strategyType":1,"supportStrategyName":0}, {"displayOrder":29,"id":150,"options":null,"strategyName":"vvvvvv","strategyType":2,"supportStrategyName":0}, {"displayOrder":30,"id":151,"options":null,"strategyName":"vvvvvvvvv","strategyType":1,"supportStrategyName":0}, {"displayOrder":31,"id":152,"options":null,"strategyName":"2323","strategyType":2,"supportStrategyName":0}, {"displayOrder":32,"id":153,"options":null,"strategyName":"asdsad","strategyType":2,"supportStrategyName":0}, {"displayOrder":33,"id":173,"options":[{"displayOrder":1,"id":8,"optionName":"\u81EA\u5B9A\u4E49\u7B56\u756511","strategyId":173,"weightScore":null}, {"displayOrder":2,"id":9,"optionName":"\u81EA\u5B9A\u4E49\u7B56\u756522","strategyId":173,"weightScore":null}, {"displayOrder":3,"id":10,"optionName":"\u81EA\u5B9A\u4E49\u7B56\u7565333","strategyId":173,"weightScore":null}, {"displayOrder":4,"id":11,"optionName":"\u81EA\u5B9A\u4E49\u7B56\u756544","strategyId":173,"weightScore":null}],"strategyName":"\u56DE\u590D\u591A\u9009\u7B56\u7565","strategyType":3,"supportStrategyName":0}],"projectHotelRemarkResponseList":[],"projectHotelTendStrategy":{"createTime":"Mon Jun 30 15:49:46 CST 2025","creator":"gang","id":135,"isRecommendIncludeAllTaxes":0,"isRecommendNoBreakfastDouble":0,"isRecommendNoBreakfastSingle":0,"isRecommendWithBreakfastDouble":0,"isRecommendWithBreakfastSingle":0,"isRequireIncludeAllTaxes":0,"isRequireNoBreakfastDouble":0,"isRequireNoBreakfastSingle":0,"isRequireTaxDetails":0,"isRequireWithBreakfastDouble":0,"isRequireWithBreakfastSingle":0,"limitMaxPrice":1000.00,"limitMinPrice":1.00,"limitPriceCurrencyCode":"USD","maxNotApplicableDay":50,"maxRoomTypeCount":10,"maxSeasonDay":null,"modifier":"gang","modifyTime":"Tue Aug 19 19:03:21 CST 2025","projectId":113,"supportCancel":1,"supportCancelDay":1,"supportCancelTime":"12:00","supportCheckinInfo":1,"supportIncludeTaxService":1,"supportLra":1,"supportMaxNotApplicableDay":1,"supportMaxRoomTypeCount":1,"supportNoGuarantee":1,"supportPayAtHotel":1,"supportPayEarlyCheckout":1,"supportPriceLimit":true,"supportSeasonDayLimit":0,"supportVccPay":0,"supportWifi":1},"projectId":113,"projectIntentHotelBid":{"bidOrgId":138,"bidOrgType":4,"bidState":1,"bidUploadSource":2,"bidWeight":null,"employRight":null,"employRightFileUrl":null,"hotelBidContactEmail":"<EMAIL>","hotelBidContactMobile":"82233888","hotelBidContactName":"Annie Mao","hotelGroupBidContactEmail":"<EMAIL>","hotelGroupBidContactMobile":"8619979578487","hotelGroupBidContactName":"\u9996\u65C5\u5982\u5BB6","hotelId":134789,"hotelServicePoints":100.00,"inviteStatus":0,"isUpload":1,"lastYearRoomNight":null,"projectId":113,"projectIntentHotelId":236,"sendMailStatus":0,"tenderAvgPrice":null},"roomNameList":[{"hotelId":null,"roomId":6858081,"roomName":"Superior room"}, {"hotelId":null,"roomId":6858084,"roomName":"Enjoy non-smoking twin rooms"}, {"hotelId":null,"roomId":6858085,"roomName":"Enjoy non-smoking queen rooms"}, {"hotelId":null,"roomId":6858086,"roomName":"Standard twin rooms are non-smoking"}, {"hotelId":null,"roomId":6858087,"roomName":"Club room"}, {"hotelId":null,"roomId":6466674,"roomName":"Standard Room"}, {"hotelId":null,"roomId":10739894,"roomName":"B.Duck Parent-Child Theme Twin Room"}, {"hotelId":null,"roomId":10739893,"roomName":"Family Room for Parents and Children"}, {"hotelId":null,"roomId":3778337,"roomName":"Executive Superior Twin Room"}, {"hotelId":null,"roomId":3564334,"roomName":"Club Room"}, {"hotelId":null,"roomId":3778336,"roomName":"Holiday deluxe twin room"}, {"hotelId":null,"roomId":3778339,"roomName":"Executive deluxe twin room"}, {"hotelId":null,"roomId":3778338,"roomName":"Executive Deluxe Big bed Room"}, {"hotelId":null,"roomId":3564333,"roomName":"Deluxe Room"}, {"hotelId":null,"roomId":3778340,"roomName":"Holiday executive big bed room"}, {"hotelId":null,"roomId":7381840,"roomName":"Club Superior Room (Double Bed)"}, {"hotelId":null,"roomId":7381841,"roomName":"1 King 1 Bdrm Ste Living Dining Areas"}, {"hotelId":null,"roomId":3778331,"roomName":"Holiday superior twin room"}, {"hotelId":null,"roomId":3778330,"roomName":"Holiday superior queen room"}, {"hotelId":null,"roomId":3778335,"roomName":"Holiday deluxe queen room"}, {"hotelId":null,"roomId":3702801,"roomName":"Executive Superior Room"}, {"hotelId":null,"roomId":3702804,"roomName":"Presidential Suite"}, {"hotelId":null,"roomId":3702805,"roomName":"Cloth fan parent-child room"}, {"hotelId":null,"roomId":6858072,"roomName":"One bedroom suite is non-smoking (with living room)"}, {"hotelId":null,"roomId":6858073,"roomName":"Superior Queen Room (non-smoking)"}, {"hotelId":null,"roomId":6858074,"roomName":"Superior twin rooms are non-smoking"}, {"hotelId":null,"roomId":6858078,"roomName":"Standard queen room is non-smoking"}, {"hotelId":null,"roomId":7381834,"roomName":"Family Friendly Suite"}, {"hotelId":null,"roomId":7381835,"roomName":"Superior Room (2 Beds)"}, {"hotelId":null,"roomId":7381836,"roomName":"Superior Double Room"}, {"hotelId":null,"roomId":7381837,"roomName":"Club Standard Room (2 Beds)"}, {"hotelId":null,"roomId":7381838,"roomName":"Club Standard Room (Double Bed)"}, {"hotelId":null,"roomId":7381839,"roomName":"Club Superior Room (2 Beds)"}, {"hotelId":null,"roomId":6466337,"roomName":"One bedroom Suite"}, {"hotelId":null,"roomId":7554102,"roomName":"1 King 1 Bedroom Suite With Living Area"}, {"hotelId":null,"roomId":6466354,"roomName":"Standard Double Room"}, {"hotelId":null,"roomId":6466355,"roomName":"Standard Room (2 Beds)"}, {"hotelId":null,"roomId":330334,"roomName":"Executive Deluxe Room"}, {"hotelId":null,"roomId":330341,"roomName":"Holiday Deluxe Room"}, {"hotelId":null,"roomId":330340,"roomName":"Holiday Superior Room"}, {"hotelId":null,"roomId":330343,"roomName":"Executive Suite"}, {"hotelId":null,"roomId":330342,"roomName":"Holiday Executive Room"}, {"hotelId":null,"roomId":330337,"roomName":"Presidential Suite"}, {"hotelId":null,"roomId":330348,"roomName":"Holiday Superior Twin Room"}, {"hotelId":null,"roomId":330344,"roomName":"1 King 1 Bedroom Suite with Living Area"}, {"hotelId":null,"roomId":3568453,"roomName":"Executive Room, 1 King Bed, Smoking"}, {"hotelId":null,"roomId":330358,"roomName":"Deluxe King Room Smoking"}, {"hotelId":null,"roomId":330353,"roomName":"Superior King Room - Smoking"}, {"hotelId":null,"roomId":330355,"roomName":"Superior Twin Room Smoking"}, {"hotelId":null,"roomId":10624292,"roomName":"Premier Room (King Bed)"}, {"hotelId":null,"roomId":10547760,"roomName":"Cozy Queen Room"}, {"hotelId":null,"roomId":4600276,"roomName":"Family-friendly Suite"}, {"hotelId":null,"roomId":4600274,"roomName":"Business Suite"}, {"hotelId":null,"roomId":10555444,"roomName":"Cozy 2-bed Room"}, {"hotelId":null,"roomId":4600275,"roomName":"Big-billed monkey theme room"}, {"hotelId":null,"roomId":6063808,"roomName":"Suite"}, {"hotelId":null,"roomId":8757794,"roomName":"One bedroom suite queen bed 1 bedroom with living room child theme"}, {"hotelId":null,"roomId":3602846,"roomName":"Executive deluxe room"}, {"hotelId":null,"roomId":10487305,"roomName":"Family Room"}, {"hotelId":null,"roomId":10487306,"roomName":"Nai Long Family Theme Twin Room"}, {"hotelId":null,"roomId":8552492,"roomName":"Number one and theme room-big bed"}, {"hotelId":null,"roomId":8552493,"roomName":"Number one and theme room-twin beds"}, {"hotelId":null,"roomId":3562892,"roomName":"Superior Suite"}, {"hotelId":null,"roomId":6063806,"roomName":"Premier Queen Room"}, {"hotelId":null,"roomId":6063807,"roomName":"Premier Twin Room"}, {"hotelId":null,"roomId":10793538,"roomName":"\u5965\u7279\u66FC\u4EB2\u5B50\u4E3B\u9898\u5927\u5E8A\u623F"}, {"hotelId":null,"roomId":3569884,"roomName":"Superior Room"}, {"hotelId":null,"roomId":10400876,"roomName":"Standard 2-bed Room"}, {"hotelId":null,"roomId":10400877,"roomName":"Standard Queen Room"}, {"hotelId":null,"roomId":3565765,"roomName":"Deluxe Room, 1 King Bed, Smoking (Deluxe)"}, {"hotelId":null,"roomId":10400874,"roomName":"2-bedroom Suite"}, {"hotelId":null,"roomId":10400875,"roomName":"Family-friendly Suite"}],"viewCurrencyCode":"USD","viewCurrencyExchangeRate":0.**********},"message":"Succeeded","successful":true}  [68a7364a0e00564a656786f9] ------
2025-08-21 23:08:04.538|http-nio-6888-exec-3| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.afterRequest:28|After request [client=127.0.0.1, POST /grfp-api/Api/ProjectContract/QueryHotelBidDetail]  [68a7364a0e00564a656786f9] ------
2025-08-21 23:13:20.263|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStarting:55|Starting GRfpApiApplication on LAPTOP-BVT1ALEM with PID 13264 (D:\RFP\grfp-project\grfp-api\target\classes started by ShamGod in D:\RFP\grfp-project)  [] ------
2025-08-21 23:13:20.265|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStartupProfileInfo:652|The following profiles are active: dev  [] ------
2025-08-21 23:13:21.845|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:249|Multiple Spring Data modules found, entering strict repository configuration mode!  [] ------
2025-08-21 23:13:21.848|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:127|Bootstrapping Spring Data Redis repositories in DEFAULT mode.  [] ------
2025-08-21 23:13:21.899|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:187|Finished Spring Data repository scanning in 34ms. Found 0 Redis repository interfaces.  [] ------
2025-08-21 23:13:23.159|main| INFO|o.s.boot.web.embedded.tomcat.TomcatWebServer.initialize:108|Tomcat initialized with port(s): 6888 (http)  [] ------
2025-08-21 23:13:23.175|main| INFO|org.apache.catalina.core.StandardService.log:173|Starting service [Tomcat]  [] ------
2025-08-21 23:13:23.175|main| INFO|org.apache.catalina.core.StandardEngine.log:173|Starting Servlet engine: [Apache Tomcat/9.0.45]  [] ------
2025-08-21 23:13:23.346|main| INFO|o.a.c.c.C.[Tomcat].[localhost].[/grfp-api].log:173|Initializing Spring embedded WebApplicationContext  [] ------
2025-08-21 23:13:23.346|main| INFO|o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:285|Root WebApplicationContext: initialization completed in 3012 ms  [] ------
2025-08-21 23:13:23.687|main| INFO|org.redisson.Version.logVersion:41|Redisson 3.16.0  [] ------
2025-08-21 23:13:26.563|redisson-netty-2-32| INFO|o.r.connection.pool.MasterPubSubConnectionPool.lambda$run$0:166|1 connections initialized for *************/*************:6379  [] ------
2025-08-21 23:13:27.864|redisson-netty-2-20| INFO|org.redisson.connection.pool.MasterConnectionPool.lambda$run$0:166|24 connections initialized for *************/*************:6379  [] ------
2025-08-21 23:13:32.047|main| INFO|com.fangcang.grfp.api.config.OssAutoConfiguration.ossClient:34|---------------Init OssClient ----------------  [] ------
2025-08-21 23:13:33.050|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'queryBidHotelInfoExecutor'  [] ------
2025-08-21 23:13:33.498|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'hotelHexagonGenerateExecutor'  [] ------
2025-08-21 23:13:34.785|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'auditLogExecutor'  [] ------
2025-08-21 23:13:35.253|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'generalExecutor'  [] ------
2025-08-21 23:13:35.254|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'syncGlinkExecutor'  [] ------
2025-08-21 23:13:35.352|main| INFO|o.s.scheduling.concurrent.ThreadPoolTaskExecutor.initialize:181|Initializing ExecutorService 'rfpCommonExecutor'  [] ------
2025-08-21 23:13:36.677|main| INFO|o.s.boot.web.embedded.tomcat.TomcatWebServer.start:220|Tomcat started on port(s): 6888 (http) with context path '/grfp-api'  [] ------
2025-08-21 23:13:40.346|main| INFO|o.s.s.a.ScheduledAnnotationBeanPostProcessor.finishRegistration:309|No TaskScheduler/ScheduledExecutorService bean found for scheduled processing  [] ------
2025-08-21 23:13:41.110|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStarted:61|Started GRfpApiApplication in 21.562 seconds (JVM running for 23.793)  [] ------
2025-08-21 23:13:41.121|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.afterStarted:59|==============================GrfpApi 系统启动完成v1.0.0-20250808===========================================  [] ------
2025-08-21 23:13:41.354|main| INFO|com.zaxxer.hikari.HikariDataSource.getConnection:110|HikariPool-1 - Starting...  [] ------
2025-08-21 23:13:44.152|main| INFO|com.zaxxer.hikari.HikariDataSource.getConnection:123|HikariPool-1 - Start completed.  [] ------
2025-08-21 23:13:45.723|main| INFO|c.f.g.core.cached.impl.CachedHotelBrandServiceImpl.getNameMap:49|HotelBrandNameMap size en-US, 9926  [] ------
2025-08-21 23:13:46.270|main| INFO|c.f.g.core.cached.impl.CachedHotelBrandServiceImpl.getNameMap:49|HotelBrandNameMap size zh-CN, 9926  [] ------
2025-08-21 23:13:46.786|main| INFO|c.f.g.core.cached.impl.CachedHotelGroupServiceImpl.getNameMap:60|HotelGroupNameMap size en-US, 8123  [] ------
2025-08-21 23:13:47.258|main| INFO|c.f.g.core.cached.impl.CachedHotelGroupServiceImpl.getNameMap:60|HotelGroupNameMap size zh-CN, 8123  [] ------
2025-08-21 23:14:01.620|main| INFO|c.f.grfp.core.cached.impl.CachedCityServiceImpl.getNameMap:55|City Map size en-US, 309420  [] ------
2025-08-21 23:14:05.117|http-nio-6888-exec-1| INFO|o.a.c.c.C.[Tomcat].[localhost].[/grfp-api].log:173|Initializing Spring DispatcherServlet 'dispatcherServlet'  [] ------
2025-08-21 23:14:05.117|http-nio-6888-exec-1| INFO|org.springframework.web.servlet.DispatcherServlet.initServletBean:525|Initializing Servlet 'dispatcherServlet'  [] ------
2025-08-21 23:14:05.133|http-nio-6888-exec-1| INFO|org.springframework.web.servlet.DispatcherServlet.initServletBean:547|Completed initialization in 15 ms  [] ------
2025-08-21 23:14:05.497|http-nio-6888-exec-1| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.beforeRequest:23|Before request [client=127.0.0.1, POST /grfp-api/Api/ProjectContract/QueryMapHotelListByBidState]  [68a737bd0e00975c1f95d02b] ------
2025-08-21 23:14:05.864|http-nio-6888-exec-1| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.afterBodyRead:37|request body: {"bidState":1,"cityCode":null,"hotelBrandId":null,"hotelGroupId":null,"hotelName":null,"projectId":113,"userId":null,"userIds":null,"pageIndex":1,"pageSize":20}  [68a737bd0e00975c1f95d02b] ------
2025-08-21 23:14:09.799|http-nio-6888-exec-1| INFO|c.f.g.c.logging.RequestResponseBodyLoggingAdvice.beforeBodyWrite:54|response body: {"code":"200","data":"4","message":"Succeeded","successful":true}  [68a737bd0e00975c1f95d02b] ------
2025-08-21 23:14:10.996|http-nio-6888-exec-1| INFO|com.fangcang.grfp.core.filter.RequestLoggingFilter.afterRequest:28|After request [client=127.0.0.1, POST /grfp-api/Api/ProjectContract/QueryMapHotelListByBidState]  [68a737bd0e00975c1f95d02b] ------
2025-08-21 23:14:15.868|main| INFO|c.f.grfp.core.cached.impl.CachedCityServiceImpl.getNameMap:55|City Map size zh-CN, 309420  [] ------
2025-08-21 23:42:37.898|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStarting:55|Starting GRfpApiApplication on LAPTOP-BVT1ALEM with PID 7060 (D:\RFP\grfp-project\grfp-api\target\classes started by ShamGod in D:\RFP\grfp-project)  [] ------
2025-08-21 23:42:37.900|main| INFO|com.fangcang.grfp.api.GRfpApiApplication.logStartupProfileInfo:652|The following profiles are active: dev  [] ------
2025-08-21 23:42:39.479|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.multipleStoresDetected:249|Multiple Spring Data modules found, entering strict repository configuration mode!  [] ------
2025-08-21 23:42:39.482|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:127|Bootstrapping Spring Data Redis repositories in DEFAULT mode.  [] ------
2025-08-21 23:42:39.536|main| INFO|o.s.d.r.config.RepositoryConfigurationDelegate.registerRepositoriesIn:187|Finished Spring Data repository scanning in 35ms. Found 0 Redis repository interfaces.  [] ------
2025-08-21 23:42:40.825|main| INFO|o.s.boot.web.embedded.tomcat.TomcatWebServer.initialize:108|Tomcat initialized with port(s): 6888 (http)  [] ------
2025-08-21 23:42:40.842|main| INFO|org.apache.catalina.core.StandardService.log:173|Starting service [Tomcat]  [] ------
2025-08-21 23:42:40.843|main| INFO|org.apache.catalina.core.StandardEngine.log:173|Starting Servlet engine: [Apache Tomcat/9.0.45]  [] ------
2025-08-21 23:42:41.014|main| INFO|o.a.c.c.C.[Tomcat].[localhost].[/grfp-api].log:173|Initializing Spring embedded WebApplicationContext  [] ------
2025-08-21 23:42:41.014|main| INFO|o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:285|Root WebApplicationContext: initialization completed in 3046 ms  [] ------
2025-08-21 23:42:41.368|main| INFO|org.redisson.Version.logVersion:41|Redisson 3.16.0  [] ------
