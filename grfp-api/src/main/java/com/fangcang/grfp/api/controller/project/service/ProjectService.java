package com.fangcang.grfp.api.controller.project.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fangcang.grfp.api.controller.project.vo.AddProjectCustomStrategyOptionRequest;
import com.fangcang.grfp.api.controller.project.vo.AddProjectCustomTendStrategyRequest;
import com.fangcang.grfp.api.controller.project.vo.AddProjectHotelTendStrategyRequest;
import com.fangcang.grfp.api.controller.project.vo.AddProjectHotelTendWeightRequest;
import com.fangcang.grfp.api.controller.project.vo.AddProjectHotelWhiteRequest;
import com.fangcang.grfp.api.controller.project.vo.AddProjectPoiRequest;
import com.fangcang.grfp.api.controller.project.vo.AddProjectRequest;
import com.fangcang.grfp.api.controller.project.vo.BindHistoryProjectRequest;
import com.fangcang.grfp.api.controller.project.vo.DeleteHotelHistoryTradeDataRequest;
import com.fangcang.grfp.api.controller.project.vo.DeleteProjectCustomTendStrategyRequest;
import com.fangcang.grfp.api.controller.project.vo.DeleteProjectHotelWhiteRequest;
import com.fangcang.grfp.api.controller.project.vo.DeleteProjectPoiRequest;
import com.fangcang.grfp.api.controller.project.vo.EditHotelHistoryTradeDataRequest;
import com.fangcang.grfp.api.controller.project.vo.EmailResponse;
import com.fangcang.grfp.api.controller.project.vo.ExportTenderPriceRequest;
import com.fangcang.grfp.api.controller.project.vo.ExportTenderPriceVO;
import com.fangcang.grfp.api.controller.project.vo.GetProjectInfoRequest;
import com.fangcang.grfp.api.controller.project.vo.HistoryProjectInfoVO;
import com.fangcang.grfp.api.controller.project.vo.ImportHotelGroupIntentHotelVO;
import com.fangcang.grfp.api.controller.project.vo.ImportProjectHotelHistoryTradeDataVO;
import com.fangcang.grfp.api.controller.project.vo.ImportProjectIntentHotelVO;
import com.fangcang.grfp.api.controller.project.vo.InviteHotelsRequest;
import com.fangcang.grfp.api.controller.project.vo.JoinIntentHotelGroupRequest;
import com.fangcang.grfp.api.controller.project.vo.JoinIntentHotelRequest;
import com.fangcang.grfp.api.controller.project.vo.MigrateTenderPriceRequest;
import com.fangcang.grfp.api.controller.project.vo.ProjectBasicInfoVO;
import com.fangcang.grfp.api.controller.project.vo.ProjectInvitedHotelInfo;
import com.fangcang.grfp.api.controller.project.vo.QueryHistoryProjectRequest;
import com.fangcang.grfp.api.controller.project.vo.QueryProjectByNameRequest;
import com.fangcang.grfp.api.controller.project.vo.QueryProjectInvitedHotelList;
import com.fangcang.grfp.api.controller.project.vo.RelatedProjectInfoVO;
import com.fangcang.grfp.api.controller.project.vo.SaveProjectLanyonViewColumnInfoRequest;
import com.fangcang.grfp.api.controller.project.vo.SetBrandLimitRequest;
import com.fangcang.grfp.api.controller.project.vo.SwitchCustomTendStrategyDisplayOrderRequest;
import com.fangcang.grfp.api.controller.project.vo.UpdateCustomStrategyOptionWeightRequest;
import com.fangcang.grfp.api.controller.project.vo.UpdateCustomTendStrategyWeightRequest;
import com.fangcang.grfp.api.controller.project.vo.UpdateProjectCustomTendStrategyRequest;
import com.fangcang.grfp.api.controller.project.vo.UpdateProjectIntroductionRequest;
import com.fangcang.grfp.api.controller.project.vo.UpdateProjectRequest;
import com.fangcang.grfp.api.controller.project.vo.UpdateProjectStateRequest;
import com.fangcang.grfp.api.thread.GenerateHotelHexagonLngLatThread;
import com.fangcang.grfp.api.util.AppUtility;
import com.fangcang.grfp.core.base.IdRequest;
import com.fangcang.grfp.core.base.IdVO;
import com.fangcang.grfp.core.base.ImportRowErrorVO;
import com.fangcang.grfp.core.base.PageVO;
import com.fangcang.grfp.core.bo.RedisService;
import com.fangcang.grfp.core.constant.ErrorCode;
import com.fangcang.grfp.core.constant.RedisConstant;
import com.fangcang.grfp.core.constant.RfpConstant;
import com.fangcang.grfp.core.dto.dhub.request.baseinfo.FindHotelListRequest;
import com.fangcang.grfp.core.dto.dhub.response.baseinfo.FindHotelInfo;
import com.fangcang.grfp.core.dto.dhub.response.baseinfo.FindHotelListResponse;
import com.fangcang.grfp.core.dto.excel.ExportExcelContext;
import com.fangcang.grfp.core.dto.excel.ImportExcelContext;
import com.fangcang.grfp.core.entity.HotelEntity;
import com.fangcang.grfp.core.entity.OrgEntity;
import com.fangcang.grfp.core.entity.OrgRelatedHotelEntity;
import com.fangcang.grfp.core.entity.ProjectCustomStrategyOptionEntity;
import com.fangcang.grfp.core.entity.ProjectCustomTendStrategyEntity;
import com.fangcang.grfp.core.entity.ProjectEntity;
import com.fangcang.grfp.core.entity.ProjectHotelHistoryDataEntity;
import com.fangcang.grfp.core.entity.ProjectHotelTendStrategyEntity;
import com.fangcang.grfp.core.entity.ProjectHotelTendWeightEntity;
import com.fangcang.grfp.core.entity.ProjectHotelWhiteEntity;
import com.fangcang.grfp.core.entity.ProjectIntentHotelEntity;
import com.fangcang.grfp.core.entity.ProjectIntentHotelGroupEntity;
import com.fangcang.grfp.core.entity.ProjectInviteHotelEntity;
import com.fangcang.grfp.core.entity.ProjectLanyonViewKeysEntity;
import com.fangcang.grfp.core.entity.ProjectLastYearCityStatEntity;
import com.fangcang.grfp.core.entity.ProjectPoiEntity;
import com.fangcang.grfp.core.entity.RecommendHotelEntity;
import com.fangcang.grfp.core.entity.UserEntity;
import com.fangcang.grfp.core.enums.CustomStrategyTypeEnum;
import com.fangcang.grfp.core.enums.HotelBidStateEnum;
import com.fangcang.grfp.core.enums.HotelStarEnum;
import com.fangcang.grfp.core.enums.ImportBizTypeEnum;
import com.fangcang.grfp.core.enums.LanguageEnum;
import com.fangcang.grfp.core.enums.OrgTypeEnum;
import com.fangcang.grfp.core.enums.ProjectStateEnum;
import com.fangcang.grfp.core.enums.ProjectTypeEnum;
import com.fangcang.grfp.core.enums.RoleCodeEnum;
import com.fangcang.grfp.core.enums.TenderTypeEnum;
import com.fangcang.grfp.core.enums.YesOrNoEnum;
import com.fangcang.grfp.core.manager.*;
import com.fangcang.grfp.core.mapper.DisHotelDailyOrderMapper;
import com.fangcang.grfp.core.mapper.HotelMapper;
import com.fangcang.grfp.core.mapper.LanyonImportColumnMapper;
import com.fangcang.grfp.core.mapper.OrgMapper;
import com.fangcang.grfp.core.mapper.OrgRelatedHotelMapper;
import com.fangcang.grfp.core.mapper.ProjectCustomStrategyOptionMapper;
import com.fangcang.grfp.core.mapper.ProjectCustomTendStrategyMapper;
import com.fangcang.grfp.core.mapper.ProjectHotelHistoryDataMapper;
import com.fangcang.grfp.core.mapper.ProjectHotelTendStrategyMapper;
import com.fangcang.grfp.core.mapper.ProjectHotelTendWeightMapper;
import com.fangcang.grfp.core.mapper.ProjectHotelWhiteMapper;
import com.fangcang.grfp.core.mapper.ProjectIntentHotelGroupMapper;
import com.fangcang.grfp.core.mapper.ProjectIntentHotelMapper;
import com.fangcang.grfp.core.mapper.ProjectInviteHotelMapper;
import com.fangcang.grfp.core.mapper.ProjectLanyonViewKeysMapper;
import com.fangcang.grfp.core.mapper.ProjectLastYearCityStatMapper;
import com.fangcang.grfp.core.mapper.ProjectMapper;
import com.fangcang.grfp.core.mapper.ProjectPoiMapper;
import com.fangcang.grfp.core.mapper.RecommendHotelMapper;
import com.fangcang.grfp.core.mapper.UserMapper;
import com.fangcang.grfp.core.oss.OssManager;
import com.fangcang.grfp.core.remote.tmchub.manager.TmcHubApiManager;
import com.fangcang.grfp.core.usersession.UserSession;
import com.fangcang.grfp.core.util.CommonUtil;
import com.fangcang.grfp.core.util.GenericAppUtility;
import com.fangcang.grfp.core.util.HotelUtility;
import com.fangcang.grfp.core.util.LocationUtil;
import com.fangcang.grfp.core.util.StringUtil;
import com.fangcang.grfp.core.util.ValidateUtil;
import com.fangcang.grfp.core.vo.DestinationHotelVO;
import com.fangcang.grfp.core.vo.HotelRelatedOrgContactInfoVO;
import com.fangcang.grfp.core.vo.LanyonImportColumnVO;
import com.fangcang.grfp.core.vo.ListProjectVO;
import com.fangcang.grfp.core.vo.ProjectNameVO;
import com.fangcang.grfp.core.vo.ProjectRelatedCount;
import com.fangcang.grfp.core.vo.TenderHotelPriceVO;
import com.fangcang.grfp.core.vo.request.ListProjectRequest;
import com.fangcang.grfp.core.vo.request.hotel.DisHotelDailyOrderRequest;
import com.fangcang.grfp.core.vo.request.project.DeleteHotelGroupInviteHotelRequest;
import com.fangcang.grfp.core.vo.request.project.HotelHistoryTradeDataListRequest;
import com.fangcang.grfp.core.vo.request.project.QueryCustomTendStrategyRequest;
import com.fangcang.grfp.core.vo.request.project.QueryHotelGroupInviteHotelRequest;
import com.fangcang.grfp.core.vo.request.project.QueryHotelTendStrategyRequest;
import com.fangcang.grfp.core.vo.request.project.QueryInviteHotelGroupRequest;
import com.fangcang.grfp.core.vo.request.project.QueryProjectHotelTendWeightRequest;
import com.fangcang.grfp.core.vo.request.project.QueryProjectHotelWhiteRequest;
import com.fangcang.grfp.core.vo.request.project.QueryProjectIntentHotelRequest;
import com.fangcang.grfp.core.vo.request.project.UpdateProjectIntentHotelRequest;
import com.fangcang.grfp.core.vo.response.bidmap.ProjectPoiInfoResponse;
import com.fangcang.grfp.core.vo.response.hotel.DisHotelDailyOrderResponse;
import com.fangcang.grfp.core.vo.response.project.CustomStrategyOptionVO;
import com.fangcang.grfp.core.vo.response.project.CustomTendStrategyVO;
import com.fangcang.grfp.core.vo.response.project.HotelHistoryTradeDataListVO;
import com.fangcang.grfp.core.vo.response.project.InviteHotelGroupVO;
import com.fangcang.grfp.core.vo.response.project.ProjectHotelTendStrategyVO;
import com.fangcang.grfp.core.vo.response.project.ProjectHotelTendWeightVO;
import com.fangcang.grfp.core.vo.response.project.ProjectHotelWhiteVO;
import com.fangcang.grfp.core.vo.response.project.ProjectIntentHotelVO;
import com.fangcang.grfp.core.vo.response.project.ProjectPoiVO;
import com.fangcang.grfp.core.vo.response.project.QueryHistoryProjectInfoResponse;
import com.fangcang.grfp.core.vo.response.project.QueryHotelGroupInviteHotelVO;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.mutable.MutableInt;
import org.apache.ibatis.session.ExecutorType;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ProjectService {

    @Autowired
    private ProjectMapper projectMapper;

    @Resource
    private ProjectPoiMapper projectPoiMapper;

    @Resource
    private HttpServletRequest httpServletRequest;

    @Resource
    private ProjectCustomTendStrategyMapper projectCustomTendStrategyMapper;
    @Resource
    private ProjectHotelTendStrategyMapper projectHotelTendStrategyMapper;
    @Resource
    private ProjectHotelTendWeightMapper projectHotelTendWeightMapper;
    @Resource
    private HotelMapper hotelMapper;
    @Resource
    private TmcHubApiManager tmcHubApiManager;
    @Resource
    private ProjectHotelWhiteMapper projectHotelWhiteMapper;
    @Resource
    private ProjectIntentHotelMapper projectIntentHotelMapper;
    @Resource
    private RecommendHotelMapper recommendHotelMapper;
    @Resource
    private OrgRelatedHotelMapper orgRelatedHotelMapper;
    @Resource
    private OrgMapper orgMapper;
    @Resource
    private ProjectIntentHotelGroupMapper projectIntentHotelGroupMapper;
    @Resource
    private RedisService redisService;
    @Resource
    private HotelManager hotelManager;
    @Resource
    private ExcelManager excelManager;
    @Resource
    private ProjectInviteHotelMapper projectInviteHotelMapper;
    @Resource
    private ProjectHotelHistoryDataMapper projectHotelHistoryDataMapper;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private DisHotelDailyOrderMapper disHotelDailyOrderMapper;
    @Resource
    private ProjectLastYearCityStatMapper projectLastYearCityStatMapper;
    @Resource
    private TaskExecutor hotelHexagonGenerateExecutor;
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private SqlSessionFactory sqlSessionFactory;
    @Resource
    private ProjectHotelHistoryDataRecommendService projectHotelHistoryDataRecommendService;
    @Autowired
    private HotelHexagonLngLatManager hotelHexagonLngLatManager;
    @Resource
    private ProjectManager projectManager;
    @Resource
    private OssManager ossManager;
    @Resource
    private LanyonImportColumnMapper lanyonImportColumnMapper;
    @Autowired
    private ProjectLanyonViewKeysMapper projectLanyonViewKeysMapper;
    @Resource
    private ProjectCustomStrategyOptionMapper projectCustomStrategyOptionMapper;
    @Resource
    private TransactionTemplate transactionTemplate;

    @Resource
    private BidManager bidManager;

    public PageVO<ListProjectVO> queryProjectPage(HttpServletRequest request, ListProjectRequest listProjectRequest){
        UserSession userSession = UserSession.get();
        // 分页查询
        IPage<ListProjectVO> page = new Page<>(listProjectRequest.getPageIndex(), listProjectRequest.getPageSize());
        if(Objects.equals(userSession.getUserOrg().getOrgType(), OrgTypeEnum.DISTRIBUTOR.key)){
            listProjectRequest.setTenderOrgId(userSession.getUserOrg().getOrgId());
        }
        projectMapper.queryProjectPage(page, listProjectRequest);

        // 已经报价和邀请酒店数量
        if(CollectionUtils.isNotEmpty(page.getRecords())){
            List<Integer> projectIdList = page.getRecords().stream().map(ListProjectVO::getProjectId).collect(Collectors.toList());
            // 查询已经报价数量
            List<ProjectRelatedCount> projectRelatedCountList = projectIntentHotelMapper.selectProjectRelatedCountList(projectIdList);
            Map<Integer, ProjectRelatedCount> projectRelatedCountMap = projectRelatedCountList.stream().collect(Collectors.toMap(ProjectRelatedCount::getProjectId, Function.identity()));
            page.getRecords().forEach(item -> {
                // 设置报价公布日期
                item.setBidResultTime(DateUtil.offset(item.getFirstBidEndTime(), DateField.DAY_OF_YEAR ,1));
                // 设置报价数量
                ProjectRelatedCount projectRelatedCount = projectRelatedCountMap.get(item.getProjectId());
                if(projectRelatedCount != null){
                    item.setBidHotelCount(projectRelatedCount.getBidCount());
                    item.setInviteHotelCount(projectRelatedCount.getInvitedCount());
                } else {
                    item.setBidHotelCount(0);
                    item.setInviteHotelCount(0);
                }
            });
        }
        return new PageVO<>((int) page.getTotal(),  (int) page.getPages(), page.getRecords());
    }

    public ProjectBasicInfoVO queryProjectBasicInfo(HttpServletRequest request, GetProjectInfoRequest getProjectInfoRequest){
        ProjectEntity project = projectMapper.selectById(getProjectInfoRequest.getProjectId());
        ProjectBasicInfoVO result = new ProjectBasicInfoVO();
        BeanUtils.copyProperties(project, result);
        // 查询机构名称
        OrgEntity org = orgMapper.selectById(project.getTenderOrgId());
        result.setTenderOrgName(org.getOrgName());
        return result;
    }

    public Integer addProject(HttpServletRequest request, AddProjectRequest addProjectRequest){
        UserSession userSession = UserSession.get();

        // 设置项目信息
        ProjectEntity project = new ProjectEntity();
        project.setCreator(userSession.getUsername());
        project.setModifier(userSession.getUsername());

        // 设置项目名称
        project.setProjectName(addProjectRequest.getProjectName());

        // 设置项目状态
        project.setProjectState(ProjectStateEnum.NOT_STARTED.key);

        // 设置报价机构
        if(userSession.getUserOrg().getOrgType().equals(OrgTypeEnum.DISTRIBUTOR.key)){
            project.setTenderOrgId(userSession.getUserOrg().getOrgId());
        } else if( userSession.getUserOrg().getOrgType().equals(OrgTypeEnum.PLATFORM.key)){
            project.setTenderOrgId(addProjectRequest.getTenderOrgId());
        } else {
            AppUtility.serviceError(ErrorCode.ORG_CANNOT_ADD_PROJECT);
        }

        //招标类型
        project.setProjectType(1);
        project.setTenderType(addProjectRequest.getTenderType());

        // 设置时间
        project.setBidStartTime(DateUtil.parseDate(addProjectRequest.getBidStartTime()));
        project.setBidEndTime(DateUtil.parseDate(addProjectRequest.getBidEndTime()));
        project.setFirstBidStartTime(DateUtil.parseDate(addProjectRequest.getFirstBidStartTime()));
        project.setFirstBidEndTime(DateUtil.parseDate(addProjectRequest.getFirstBidEndTime()));
        project.setSecondBidStartTime(StringUtil.isValidString(addProjectRequest.getSecondBidStartTime()) ? DateUtil.parseDate(addProjectRequest.getSecondBidStartTime()) : null);
        project.setSecondBidEndTime(StringUtil.isValidString(addProjectRequest.getSecondBidEndTime()) ? DateUtil.parseDate(addProjectRequest.getSecondBidEndTime()) : null);
        project.setThirdBidStartTime(StringUtil.isValidString(addProjectRequest.getThirdBidStartTime()) ? DateUtil.parseDate(addProjectRequest.getThirdBidStartTime()) : null);
        project.setThirdBidEndTime(StringUtil.isValidString(addProjectRequest.getThirdBidEndTime()) ? DateUtil.parseDate(addProjectRequest.getThirdBidEndTime()) : null);
        project.setPriceMonitorStartDate(DateUtil.parseDate(addProjectRequest.getPriceMonitorStartDate()));
        project.setPriceMonitorEndDate(DateUtil.parseDate(addProjectRequest.getPriceMonitorEndDate()));
        // 检查报价时间
        validateProjectBidDate(project);

        // 设置联系人
        project.setContactName(addProjectRequest.getContactName());
        project.setContactMobile(addProjectRequest.getContactMobile());

        // 设置招标方式
        project.setTenderType( addProjectRequest.getTenderType());

        // 设置报价通知方式
        project.setBidStateUpdatedNotifyMode(addProjectRequest.getBidStateUpdatedNotifyMode());

        // 设置预估采购酒店数量
        project.setTenderCount(addProjectRequest.getTenderCount());

        // 设置协议报价开始日期
        project.setPriceMonitorStartDate(DateUtil.parseDate(addProjectRequest.getPriceMonitorStartDate()));
        project.setPriceMonitorEndDate(DateUtil.parseDate(addProjectRequest.getPriceMonitorEndDate()));

        // 设置排序
        project.setDisplayOrder(addProjectRequest.getDisplayOrder());

        // 新增项目
        AppUtility.doInsert(projectMapper.insert(project));

        //TODO
        //  //如果是企业发布项目，需要将推荐酒店中的意向酒店添加到项目意向表

        // 返回项目ID
        return project.getProjectId();

    }

    public int updateProject(HttpServletRequest request, UpdateProjectRequest updateProjectRequest){
        UserSession userSession = UserSession.get();

        // 检查项目
        validateUpdateProject(userSession, updateProjectRequest.getProjectId());

        // 设置项目信息
        ProjectEntity project = new ProjectEntity();
        project.setProjectId(updateProjectRequest.getProjectId());
        project.setProjectName(updateProjectRequest.getProjectName());
        project.setProjectType(updateProjectRequest.getProjectType());
        project.setDisplayOrder(updateProjectRequest.getDisplayOrder());
        project.setModifier(userSession.getUsername());

        // 设置时间
        project.setBidStartTime(DateUtil.parseDate(updateProjectRequest.getBidStartTime()));
        project.setBidEndTime(DateUtil.parseDate(updateProjectRequest.getBidEndTime()));
        project.setFirstBidStartTime(DateUtil.parseDate(updateProjectRequest.getFirstBidStartTime()));
        project.setFirstBidEndTime(DateUtil.parseDate(updateProjectRequest.getFirstBidEndTime()));
        project.setSecondBidStartTime(StringUtil.isValidString(updateProjectRequest.getSecondBidStartTime()) ? DateUtil.parseDate(updateProjectRequest.getSecondBidStartTime()) : null);
        project.setSecondBidEndTime(StringUtil.isValidString(updateProjectRequest.getSecondBidEndTime()) ? DateUtil.parseDate(updateProjectRequest.getSecondBidEndTime()) : null);
        project.setThirdBidStartTime(StringUtil.isValidString(updateProjectRequest.getThirdBidStartTime()) ? DateUtil.parseDate(updateProjectRequest.getThirdBidStartTime()) : null);
        project.setThirdBidEndTime(StringUtil.isValidString(updateProjectRequest.getThirdBidEndTime()) ? DateUtil.parseDate(updateProjectRequest.getThirdBidEndTime()) : null);
        project.setPriceMonitorStartDate(DateUtil.parseDate(updateProjectRequest.getPriceMonitorStartDate()));
        project.setPriceMonitorEndDate(DateUtil.parseDate(updateProjectRequest.getPriceMonitorEndDate()));
        // 检查报价时间
        validateProjectBidDate(project);

        // 设置联系人
        project.setContactName(updateProjectRequest.getContactName());
        project.setContactMobile(updateProjectRequest.getContactMobile());

        // 设置报价通知方式
        project.setBidStateUpdatedNotifyMode(updateProjectRequest.getBidStateUpdatedNotifyMode());

        // 设置预估采购酒店数量
        project.setTenderCount(updateProjectRequest.getTenderCount());

        // 设置协议报价开始日期
        project.setPriceMonitorStartDate(DateUtil.parseDate(updateProjectRequest.getPriceMonitorStartDate()));
        project.setPriceMonitorEndDate(DateUtil.parseDate(updateProjectRequest.getPriceMonitorEndDate()));

        // 修改项目
        AppUtility.doUpdateOneRecord(projectMapper.updateById(project));

        // 返回项目ID
        return project.getProjectId();
    }

    public int updateProjectIntroduction(HttpServletRequest request, UpdateProjectIntroductionRequest updateProjectIntroductionRequest){
        UserSession userSession = UserSession.get();

        // 检查项目
        validateUpdateProject(userSession, updateProjectIntroductionRequest.getProjectId());

        // 设置项目属性
        ProjectEntity project = new ProjectEntity();
        project.setProjectId(updateProjectIntroductionRequest.getProjectId());
        project.setIntroduction(ossManager.replaceTempUrlToPublic(updateProjectIntroductionRequest.getFileKeys(), updateProjectIntroductionRequest.getIntroduction()));
        project.setModifier(userSession.getUsername());

        // 修改
        AppUtility.doUpdateOneRecord(projectMapper.updateById(project));

        return project.getProjectId();

    }

    public PageVO<ProjectInvitedHotelInfo> queryProjectInvitedHotelList(HttpServletRequest request, QueryProjectInvitedHotelList queryProjectInvitedHotelList){
        List<ProjectInvitedHotelInfo> projectInvitedHotelList = new ArrayList<>();
        FindHotelListRequest findHotelListRequest = FindHotelListRequest.builder().build();
        BeanUtils.copyProperties(queryProjectInvitedHotelList, findHotelListRequest);
        findHotelListRequest.setLanguage(LanguageEnum.getValueByKey(GenericAppUtility.getRequestHeaderLanguage(request)));
        findHotelListRequest.setCurrentPage(queryProjectInvitedHotelList.getPageIndex());
        findHotelListRequest.setPageSize(queryProjectInvitedHotelList.getPageSize());
        findHotelListRequest.setPriceBegin(null);
        findHotelListRequest.setPriceEnd(null);
        FindHotelListResponse findHotelListResponse = tmcHubApiManager.findHotelList(findHotelListRequest, null).getBussinessResponse();

        // 数据转换
        int totalCount = 0;
        int totalPage = 0;
        if(findHotelListResponse != null && CollectionUtils.isNotEmpty(findHotelListResponse.getList())){
            totalCount = findHotelListResponse.getTotalCount();
            totalPage = findHotelListResponse.getTotalPage();
            List<Long> hotelIdList = findHotelListResponse.getList().stream().map(FindHotelInfo::getHotelId).collect(Collectors.toList());
            // 查询过滤邀约酒店
            Map<Long, ProjectIntentHotelEntity> projectIntentHotelMap = projectIntentHotelMapper.queryProjectIntentByHotelIdList(queryProjectInvitedHotelList.getProjectId(), hotelIdList).stream().filter(o -> o.getInviteStatus() != null && o.getInviteStatus() == RfpConstant.constant_1).collect(Collectors.toMap(ProjectIntentHotelEntity::getHotelId, Function.identity()));
            for(FindHotelInfo findHotelInfo : findHotelListResponse.getList()){
               ProjectInvitedHotelInfo projectInvitedHotelInfo = new ProjectInvitedHotelInfo();
               BeanUtils.copyProperties(findHotelInfo, projectInvitedHotelInfo);
               projectInvitedHotelInfo.setIsInvited(RfpConstant.constant_0);
               if(projectIntentHotelMap.containsKey(findHotelInfo.getHotelId())){
                   projectInvitedHotelInfo.setIsInvited(RfpConstant.constant_1);
               }
               projectInvitedHotelList.add(projectInvitedHotelInfo);
            }
        }
        return new PageVO<>(totalCount, totalPage, projectInvitedHotelList);
    }

    public void inviteHotels(HttpServletRequest request, InviteHotelsRequest inviteHotelsRequest){
        // 检查酒店是否已经加入意向表
        UserSession userSession = UserSession.get();

        // 检查项目是否存在
        ProjectEntity project = projectMapper.selectById(inviteHotelsRequest.getProjectId());
        if(project == null){
            AppUtility.serviceError(ErrorCode.PROJECT_NOT_EXIST);
        }
        // 酒店意向表已经存在数据，更新邀约状态
        Set<Long> hotelIdSet = inviteHotelsRequest.getHotelInfoList().stream().map(DestinationHotelVO::getHotelId).collect(Collectors.toSet());
        List<ProjectIntentHotelEntity> projectIntentHotelList = projectIntentHotelMapper.queryProjectIntentByHotelIdList(inviteHotelsRequest.getProjectId(), hotelIdSet);
        List<Long> needUpdateInvitedHotelIds = new ArrayList<>();

        // 异步同步酒店信息
        hotelManager.asyncInsertHotelByDestination(inviteHotelsRequest.getHotelInfoList(), userSession.getUsername());

        if (CollectionUtils.isNotEmpty(projectIntentHotelList)) {
            for (ProjectIntentHotelEntity projectIntentHotel : projectIntentHotelList) {
                //添加的酒店是已邀请，存在的酒店如果是未邀请要修改成已邀请
                if(hotelIdSet.contains(projectIntentHotel.getHotelId()) && !Objects.equals(projectIntentHotel.getInviteStatus(), inviteHotelsRequest.getInviteStatus())) {
                    needUpdateInvitedHotelIds.add(projectIntentHotel.getHotelId());
                    hotelIdSet.remove(projectIntentHotel.getHotelId());
                }
            }
            if (needUpdateInvitedHotelIds.size() > 0) {
                //未邀约的酒店更新成已邀约
                projectIntentHotelMapper.updateInviteStatus(inviteHotelsRequest.getProjectId(), needUpdateInvitedHotelIds, inviteHotelsRequest.getInviteStatus(), userSession.getUsername());
                if(userSession.getUserOrg().getOrgType().intValue() == OrgTypeEnum.DISTRIBUTOR.key.intValue()
                        && !userSession.getRoleCode().equals(RoleCodeEnum.ADMIN.key) ) {
                    if(inviteHotelsRequest.getInviteStatus() == RfpConstant.constant_1) {
                        projectIntentHotelMapper.updateDistributorContactInfo(inviteHotelsRequest.getProjectId(), needUpdateInvitedHotelIds, userSession.getUserId(), userSession.getUsername(), userSession.getUsername());
                    } else {
                        projectIntentHotelMapper.updateDistributorContactInfo(inviteHotelsRequest.getProjectId(), needUpdateInvitedHotelIds, null, null, null);
                    }
                }
            }
        }
        // 酒店意向表不存在，新增酒店意向
        if(CollectionUtils.isNotEmpty(hotelIdSet)) {
            // 查询酒店推荐信息
            Map<Long, ProjectIntentHotelEntity> projectIntentHotelsMap = new HashMap<>();
            //酒店销售联系人先从推荐酒店中获取，如果不在推荐酒店中则从机构从获取
            Map<Long, RecommendHotelEntity> recommendHotelMap = recommendHotelMapper.selectBatchIds(hotelIdSet).stream().collect(Collectors.toMap(RecommendHotelEntity::getHotelId, Function.identity()));
            // 查询酒店关联酒单机构
            List<HotelRelatedOrgContactInfoVO> hotelRelatedOrgContactInfoList = orgMapper.queryHotelOrgList(new ArrayList<>(hotelIdSet));
            Map<Long, HotelRelatedOrgContactInfoVO> hotelRelatedOrgContactInfoMap = hotelRelatedOrgContactInfoList.stream().collect(Collectors.toMap(HotelRelatedOrgContactInfoVO::getHotelId, Function.identity()));
            for (Long hotelId : hotelIdSet) {
                if (needUpdateInvitedHotelIds.contains(hotelId)) {
                    continue;
                }
                // 初始化酒店意向报价
                ProjectIntentHotelEntity projectIntentHotel = new ProjectIntentHotelEntity();
                projectIntentHotel.setProjectId(project.getProjectId());
                projectIntentHotel.setHotelId(hotelId);
                projectIntentHotel.setBidState(HotelBidStateEnum.NO_BID.bidState);
                projectIntentHotel.setSendMailStatus(0);
                projectIntentHotel.setIsUpload(RfpConstant.constant_0);
                projectIntentHotel.setHotelServicePoints(new BigDecimal("100"));
                projectIntentHotel.setBidUploadSource(RfpConstant.constant_0);
                projectIntentHotel.setCreator(userSession.getUsername());
                projectIntentHotel.setModifier(userSession.getUsername());
                projectIntentHotel.setInviteStatus(inviteHotelsRequest.getInviteStatus());
                projectIntentHotel.setLastInviteTime(inviteHotelsRequest.getInviteStatus() == RfpConstant.constant_1 ? new Date() : null);

                // 设置酒店联系方式 (优先从推荐表获取酒店联系方式)
                String hotelContactName = null;
                String hotelContactMobile = null;
                String hotelContactEmail = null;
                RecommendHotelEntity recommendHotel = recommendHotelMap.get(hotelId);
                HotelRelatedOrgContactInfoVO hotelRelatedOrgContactInfoVO = hotelRelatedOrgContactInfoMap.get(hotelId);
                if(recommendHotel != null){
                    hotelContactName = recommendHotel.getContactName();
                    hotelContactMobile = recommendHotel.getContactMobile();
                    hotelContactEmail = recommendHotel.getContactEmail();
                } else if(hotelRelatedOrgContactInfoVO != null){
                    hotelContactName = hotelRelatedOrgContactInfoVO.getContactName();
                    hotelContactMobile = CommonUtil.generateContactMobile(hotelRelatedOrgContactInfoVO.getContactMobileAreaCode(), hotelRelatedOrgContactInfoVO.getContactMobile());
                    hotelContactEmail = hotelRelatedOrgContactInfoVO.getContactEmail();
                }
                projectIntentHotel.setHotelBidContactName(hotelContactName);
                projectIntentHotel.setHotelBidContactMobile(hotelContactMobile);
                projectIntentHotel.setHotelBidContactEmail(hotelContactEmail);

                // 设置企业联系方式
                if(userSession.getUserOrg().getOrgType().intValue() == OrgTypeEnum.DISTRIBUTOR.key.intValue()
                        && !userSession.getRoleCode().equals(RoleCodeEnum.ADMIN.key)) {
                    projectIntentHotel.setDistributorContactUid(userSession.getUserId());
                    projectIntentHotel.setDistributorContactName(userSession.getUsername());
                }
                projectIntentHotelsMap.put(projectIntentHotel.getHotelId(), projectIntentHotel);
            }

            // 新增酒店意向报价
            if(!projectIntentHotelsMap.isEmpty()){
                projectIntentHotelMapper.batchInsert(new ArrayList<>(projectIntentHotelsMap.values()));
            }
        }
    }

    public PageVO<InviteHotelGroupVO> queryInviteHotelGroup(QueryInviteHotelGroupRequest req) {
        // 查询数据
        Page<InviteHotelGroupVO> pageRes = projectIntentHotelGroupMapper.queryInviteHotelGroup(new Page<>(req.getPageIndex(), req.getPageSize()), req);
        List<InviteHotelGroupVO> records = pageRes.getRecords();
        if (CollUtil.isEmpty(records)) {
            return new PageVO<>(0, 0, records);
        }
        return new PageVO<>(pageRes.getTotal(), pageRes.getPages(), records);
    }

    public void joinIntentHotelGroup(JoinIntentHotelGroupRequest req) {
        UserSession user = UserSession.get();
        if(!user.getUserOrg().getOrgType().equals(OrgTypeEnum.PLATFORM.key)){
            AppUtility.serviceError(ErrorCode.NO_PERMISSION_ACCESS);
        }

        boolean exists = projectIntentHotelGroupMapper.selectCount(new LambdaQueryWrapper<ProjectIntentHotelGroupEntity>()
                .eq(ProjectIntentHotelGroupEntity::getProjectId, req.getProjectId())
                .eq(ProjectIntentHotelGroupEntity::getHotelGroupOrgId, req.getHotelGroupOrgId())) > 0;
        if (exists) {
            return;
        }

        ProjectIntentHotelGroupEntity entity = new ProjectIntentHotelGroupEntity();
        entity.setProjectId(req.getProjectId().intValue());
        entity.setHotelGroupOrgId(req.getHotelGroupOrgId().intValue());
        entity.setIsBrandLimit(0);
        entity.setInviteSendEmailStatus(0);
        entity.setCreator(user.getUsername());
        entity.setModifier(user.getUsername());
        projectIntentHotelGroupMapper.insert(entity);
    }


    public void removeIntentHotelGroup(JoinIntentHotelGroupRequest req) {
        projectIntentHotelGroupMapper.delete(new LambdaQueryWrapper<ProjectIntentHotelGroupEntity>()
                .eq(ProjectIntentHotelGroupEntity::getProjectId, req.getProjectId())
                .eq(ProjectIntentHotelGroupEntity::getHotelGroupOrgId, req.getHotelGroupOrgId()));
    }

    public EmailResponse checkSendHotelInvite(JoinIntentHotelRequest req) {
        ProjectEntity project = projectMapper.selectById(req.getProjectId());
        OrgEntity tenderOrg = orgMapper.selectById(project.getTenderOrgId());

        List<ProjectIntentHotelEntity> projectIntentHotelEntities = projectIntentHotelMapper.selectList(
                new LambdaQueryWrapper<ProjectIntentHotelEntity>()
                        .eq(ProjectIntentHotelEntity::getProjectId, req.getProjectId())
                        .in(ProjectIntentHotelEntity::getHotelId, req.getProjectIntentHotelIds())
        );
        for (ProjectIntentHotelEntity projectIntentHotelEntity : projectIntentHotelEntities) {
            if (!ValidateUtil.isValidEmail(projectIntentHotelEntity.getHotelSalesContactEmail())) {
                AppUtility.serviceError(ErrorCode.CONTACT_EMAIL_INVALIDATED);
            }
        }

        EmailResponse emailResponse = new EmailResponse();
        emailResponse.setProjectName(project.getProjectName());
        int language = AppUtility.getRequestHeaderLanguage(httpServletRequest);
        emailResponse.setProjectTypeValue(ProjectTypeEnum.getTextByKey(project.getProjectType(), language));
        emailResponse.setProjectStateValue(ProjectStateEnum.geTextByKey(project.getProjectState(), language));
        emailResponse.setEnrollStartTime(project.getBidStartTime());
        emailResponse.setEnrollEndTime(project.getBidEndTime());
        emailResponse.setDiffMinAmount(project.getDiffMinAmount());
        emailResponse.setDiffMaxAmount(project.getDiffMaxAmount());
        emailResponse.setIntroduction(project.getIntroduction());
        emailResponse.setOrgName(tenderOrg.getOrgName());
        emailResponse.setCompanyProfile(tenderOrg.getCompanyProfile());
        emailResponse.setLogoUrl(tenderOrg.getLogoUrl());
        emailResponse.setTenderType(project.getTenderType());
        emailResponse.setTenderTypeValue(TenderTypeEnum.geTextByKey(project.getTenderType(), language));
        return emailResponse;
    }

    public EmailResponse checkSendHotelGroupInvite(JoinIntentHotelGroupRequest req) {
        Long projectId = req.getProjectId();

        OrgEntity orgEntity = orgMapper.selectById(req.getHotelGroupOrgId());
        if (!ValidateUtil.isValidEmail(orgEntity.getContactEmail()) && !ValidateUtil.isValidMobileNo(orgEntity.getContactMobile())) {
            AppUtility.serviceError(ErrorCode.CONTACT_EMAIL_INVALIDATED);
        }

        ProjectEntity project = projectMapper.selectById(projectId);
        OrgEntity tenderOrg = orgMapper.selectById(project.getTenderOrgId());
        EmailResponse emailResponse = new EmailResponse();
        emailResponse.setProjectName(project.getProjectName());
        int language = AppUtility.getRequestHeaderLanguage(httpServletRequest);
        emailResponse.setProjectTypeValue(ProjectTypeEnum.getTextByKey(project.getProjectType(), language));
        emailResponse.setProjectStateValue(ProjectStateEnum.geTextByKey(project.getProjectState(), language));
        emailResponse.setEnrollStartTime(project.getBidStartTime());
        emailResponse.setEnrollEndTime(project.getBidEndTime());
        emailResponse.setDiffMinAmount(project.getDiffMinAmount());
        emailResponse.setDiffMaxAmount(project.getDiffMaxAmount());
        emailResponse.setIntroduction(project.getIntroduction());
        emailResponse.setOrgName(tenderOrg.getOrgName());
        emailResponse.setCompanyProfile(tenderOrg.getCompanyProfile());
        emailResponse.setLogoUrl(tenderOrg.getLogoUrl());
        emailResponse.setTenderType(project.getTenderType());
        emailResponse.setTenderTypeValue(TenderTypeEnum.geTextByKey(project.getTenderType(), language));
        return emailResponse;
    }


    public void batchSendHotelGroupInvite(JoinIntentHotelGroupRequest req) {
        String key = RedisConstant.INVITE_HOTEL_GROUP;
        try {
            redisService.sadd(key, req.getProjectId() + "_" + req.getHotelGroupOrgId());
        } catch (Exception e) {
            log.error("加入邀请酒店集团队列失败,项目ID：{} 邀请酒店集团：{}" , req.getProjectId(), req.getHotelGroupOrgId(), e);
        }

        // 更新邀约状态
        projectIntentHotelGroupMapper.update(
                null,
                new LambdaUpdateWrapper<ProjectIntentHotelGroupEntity>()
                        .eq(ProjectIntentHotelGroupEntity::getProjectId, req.getProjectId())
                        .eq(ProjectIntentHotelGroupEntity::getHotelGroupOrgId, req.getHotelGroupOrgId())
                        .set(ProjectIntentHotelGroupEntity::getInviteSendEmailStatus, 1));
    }

    public void batchSendHotelInvite(JoinIntentHotelRequest req) {
        String key = RedisConstant.INVITE_HOTEL;
        for (Long projectIntentHotelId : req.getProjectIntentHotelIds()) {
            try {
                redisService.sadd(key, req.getProjectId() + "_" + projectIntentHotelId);
            } catch (Exception e) {
                log.error("加入邀请酒店队列失败,项目ID：{}邀请酒店ID：{}", req.getProjectId(), projectIntentHotelId, e);
            }
        }

        // 更新邀约状态
        projectIntentHotelMapper.update(
                null,
                new LambdaUpdateWrapper<ProjectIntentHotelEntity>()
                        .in(ProjectIntentHotelEntity::getProjectIntentHotelId, req.getProjectIntentHotelIds())
                        .set(ProjectIntentHotelEntity::getSendMailStatus, 1)
                        .set(ProjectIntentHotelEntity::getLastInviteTime, new Date())
        );
    }

    public void setBrandLimit(SetBrandLimitRequest req) {
        LambdaUpdateWrapper<ProjectIntentHotelGroupEntity> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ProjectIntentHotelGroupEntity::getProjectId, req.getProjectId())
                .eq(ProjectIntentHotelGroupEntity::getHotelGroupOrgId, req.getHotelGroupOrgId())
                .set(ProjectIntentHotelGroupEntity::getIsBrandLimit, req.getIsBrandLimit())
                .set(ProjectIntentHotelGroupEntity::getModifier, UserSession.get().getUsername());
        projectIntentHotelGroupMapper.update(null, updateWrapper);
    }

    public IdVO<Long> importHotelGroupIntentHotel(MultipartFile file, Integer projectId) throws IOException {
        UserSession userSession = UserSession.get();
        if(!userSession.getUserOrg().getOrgType().equals(OrgTypeEnum.PLATFORM.key)){
            AppUtility.serviceError(ErrorCode.NO_PERMISSION_ACCESS);
        }
        ImportExcelContext<ImportHotelGroupIntentHotelVO> context = new ImportExcelContext<>();
        context.setFile(file);
        context.setBizType(ImportBizTypeEnum.IMPORT_HOTEL_GROUP_INTENT_HOTEL);
        context.setImportVOClass(ImportHotelGroupIntentHotelVO.class);
        context.setExtra("projectId", projectId);
        context.setImportLogic(this::handleImportHotelGroupIntentHotel);
        return new IdVO<>(excelManager.generalImport(context));
    }

    private List<ImportRowErrorVO> handleImportHotelGroupIntentHotel(ImportExcelContext<ImportHotelGroupIntentHotelVO> context, List<ImportHotelGroupIntentHotelVO> importList) {

        List<ImportRowErrorVO> errors = new ArrayList<>();
        List<ProjectInviteHotelEntity> insertList = new ArrayList<>();
        Set<Long> hotelIdSet = new HashSet<>();

        Integer projectId = context.getExtra("projectId", Integer.class);
        String operator = context.getUserSession().getUsername();

        for (ImportHotelGroupIntentHotelVO item : importList) {
            ImportRowErrorVO rowError = new ImportRowErrorVO();

            Long hotelId = AppUtility.parseLong(item.getHotelId());
            if (hotelId == null) {
                rowError.addError(ErrorCode.HOTEL_ID_IS_NOT_NUMBER);
            }

            // 校验不通过, 处理下一条
            if (rowError.hasError()) {
                errors.add(rowError);
                continue;
            }

            hotelIdSet.add(hotelId);

            ProjectInviteHotelEntity entity = new ProjectInviteHotelEntity();
            entity.setProjectId(projectId);
            entity.setHotelId(hotelId);
            entity.setCreator(operator);
            entity.setCreateTime(new Date());
            insertList.add(entity);
        }

        // 查询 t_hotel 中已存在的 hotelId
        Set<Long> existHotelIdSet = hotelMapper.selectActiveHotelIds(hotelIdSet);

        // 补充：异步插入不存在的 hotelId 到 t_hotel 表
        List<Long> notExistHotelIds = hotelIdSet.stream()
                .filter(id -> !existHotelIdSet.contains(id))
                .collect(Collectors.toList());

        if (CollUtil.isNotEmpty(notExistHotelIds)) {
            hotelManager.queryHotelBasicInfo(notExistHotelIds, true);
        }

        // 批量插入意向酒店
        if (CollUtil.isNotEmpty(insertList)) {
            projectInviteHotelMapper.batchInsertOrUpdate(insertList);
        }

        return errors;
    }

    public PageVO<QueryHotelGroupInviteHotelVO> queryHotelGroupInviteHotel(@Valid QueryHotelGroupInviteHotelRequest request) {
        Integer language = AppUtility.getRequestHeaderLanguage(httpServletRequest);

        Page<QueryHotelGroupInviteHotelVO> page = projectInviteHotelMapper.queryProjectInviteHotelList(
                new Page<>(request.getPageIndex(), request.getPageSize()), request, language
        );

        // 设置酒店星级描述
        page.getRecords().forEach(item -> item.setHotelStarDesc(com.fangcang.grfp.core.enums.HotelStarEnum.getTextByKey(item.getHotelStar(), language)));

        return new PageVO<>(page.getTotal(), page.getPages(), page.getRecords());
    }

    /**
     * 删除酒店集团下意向酒店
     */
    public void deleteHotelGroupInviteHotel(DeleteHotelGroupInviteHotelRequest request) {
        // 参数校验
        if (request.getHotelIds() == null || request.getHotelIds().isEmpty()) {
            AppUtility.serviceError(ErrorCode.VALIDATE_REQUEST_PARAMETER_CANNOT_BE_NULL);
        }
        
        // 执行删除
        int deleteCount = projectInviteHotelMapper.deleteByProjectIdAndHotelIds(request.getProjectId(), request.getHotelIds());
        
        // 记录日志
        log.info("删除酒店集团下意向酒店，项目ID：{}，酒店ID列表：{}，删除数量：{}", 
                request.getProjectId(), request.getHotelIds(), deleteCount);
    }








    public int updateProjectState(HttpServletRequest request, UpdateProjectStateRequest updateProjectStateRequest){
        UserSession userSession = UserSession.get();

        // 检查项目
        ProjectEntity dbProject = validateUpdateProject(userSession, updateProjectStateRequest.getProjectId());

        // 检查项目状态是否正确
        if(Objects.equals(updateProjectStateRequest.getProjectState(), dbProject.getProjectState())){
            AppUtility.serviceError(ErrorCode.PROJECT_ALREADY_THE_SAME_STATE);
        }

        Integer projectState = dbProject.getProjectState();
        if (projectState == ProjectStateEnum.NOT_STARTED.key) {
            //项目未启动时，只能启动和废除项目
            if (updateProjectStateRequest.getProjectState() != ProjectStateEnum.STARTED.key && updateProjectStateRequest.getProjectState() != ProjectStateEnum.BID_ABANDONED.key) {
                AppUtility.serviceError(ErrorCode.UPDATE_PROJECT_STATE_PARAMETER_INVALIDATE);
            }
            //更新项目状态
        } else if (projectState == ProjectStateEnum.STARTED.key) {
            //项目已启动，只能终止项目
            if (updateProjectStateRequest.getProjectState() != ProjectStateEnum.BIDDING_COMPLETED.key) {
                AppUtility.serviceError(ErrorCode.UPDATE_PROJECT_STATE_PARAMETER_INVALIDATE);
            }
            //终止项目，所有投标状态为已中标或已否决或未投标状态，可以终止招标。
            // 若还有新建，或议价状态的投标信息，提示：请先处理完新建及议价状态投标才能终止招标。
            List<ProjectIntentHotelEntity> projectIntentHotels = projectIntentHotelMapper.selectInBiddingHotel(dbProject.getProjectId());
            if (CollectionUtils.isNotEmpty(projectIntentHotels)) {
                AppUtility.serviceError(ErrorCode.BIDDING_COMPLETED_FAILED_DUE_TO_HAS_NEW_OR_UNDER_NEGOTIATION_BID);
            }

            //如果是终止项目，同时将该项目内所有未报价的酒店数据【状态变更为——放弃报价】
            List<ProjectIntentHotelEntity> noBidProjectIntentHotels = projectIntentHotelMapper.queryProjectIntentByBidState(dbProject.getProjectId(), HotelBidStateEnum.NEW_BID.bidState);
            if (CollectionUtils.isNotEmpty(noBidProjectIntentHotels)) {
                List<Integer> projectIntentHotelIdList = new ArrayList<>();
                for (ProjectIntentHotelEntity noBidProjectIntentHotel : noBidProjectIntentHotels) {
                    projectIntentHotelIdList.add(noBidProjectIntentHotel.getProjectIntentHotelId());
                }
                List<List<Integer>> splitLists = ListUtil.split(projectIntentHotelIdList, 1000);
                for (List<Integer> splitList : splitLists) {
                    projectIntentHotelMapper.processingBidState(splitList, HotelBidStateEnum.WITHDRAW_THE_QUOTATION.bidState, userSession.getUsername(), "BIDDING_COMPLETED");
                }
            }
        }

        // 设置项目属性
        ProjectEntity project = new ProjectEntity();
        project.setProjectId(dbProject.getProjectId());
        project.setProjectState(updateProjectStateRequest.getProjectState());
        project.setModifier(userSession.getUsername());

        // 修改
        AppUtility.doUpdateOneRecord(projectMapper.updateById(project));

        return project.getProjectId();
    }

    /**
     * 检查项目报价时间
     */
    private void validateProjectBidDate(ProjectEntity project){
        if(project.getThirdBidEndTime() != null){
            if (com.fangcang.grfp.core.util.DateUtil.getDay(project.getBidStartTime(), project.getThirdBidEndTime()) < 0) {
                AppUtility.serviceError(ErrorCode.THRID_BID_END_TIME_CANNOT_ARTER_BID_END_TIME); // 报名开始时间不能晚于第三轮结束时间
            }
            if (com.fangcang.grfp.core.util.DateUtil.getDay(project.getBidStartTime(), project.getThirdBidEndTime()) < 0) {
                AppUtility.serviceError(ErrorCode.THRID_BID_END_TIME_CANNOT_BEFORE_BID_START_TIME); // 报名结束时间不能晚于第三轮结束时间
            }
        }else if(project.getSecondBidEndTime() != null){
            if (com.fangcang.grfp.core.util.DateUtil.getDay(project.getBidStartTime(), project.getSecondBidEndTime()) < 0) {
                AppUtility.serviceError(ErrorCode.SECOND_BID_END_TIME_RANG_UNPASS_VALIDATE); // 报名开始时间不能晚于第二轮结束时间
            }
            if (com.fangcang.grfp.core.util.DateUtil.getDay(project.getBidStartTime(), project.getSecondBidEndTime()) < 0) {
                AppUtility.serviceError(ErrorCode.SECOND_BID_END_TIME_RANG_UNPASS_VALIDATE); // 报名结束时间不能晚于第二轮结束时间
            }
        }else if(project.getFirstBidEndTime() != null){
            if (com.fangcang.grfp.core.util.DateUtil.getDay(project.getBidStartTime(), project.getFirstBidEndTime()) < 0) {
                AppUtility.serviceError(ErrorCode.FIRST_BID_END_TIME_RANG_UNPASS_VALIDATE); // 报名开始时间不能晚于第一轮结束时间
            }
            if (com.fangcang.grfp.core.util.DateUtil.getDay(project.getBidEndTime(), project.getFirstBidEndTime()) < 0) {
                AppUtility.serviceError(ErrorCode.FIRST_BID_END_TIME_RANG_UNPASS_VALIDATE); // 报名结束时间不能晚于第一轮结束时间
            }
        }
        Date firstBidStartTime = project.getFirstBidStartTime();
        Date firstBidEndTime = project.getFirstBidEndTime();
        if (firstBidEndTime != null) {
            if (com.fangcang.grfp.core.util.DateUtil.getDay(firstBidStartTime, firstBidEndTime) < 0) {
                AppUtility.serviceError(ErrorCode.FIRST_BID_START_END_TIME_UNPASS_VALIDATE); // 第一轮报价结束时间不能早于第一轮报价开始时间

            }
            Date secondBidStartTime = project.getSecondBidStartTime();
            if (secondBidStartTime != null) {
                if (com.fangcang.grfp.core.util.DateUtil.getDay(firstBidEndTime, secondBidStartTime) < 0) {
                    AppUtility.serviceError(ErrorCode.SECOND_BID_START_AND_FIRST_END_TIME_UNPASS_VALIDATE); // 第一轮报价结束时间不能早于第一轮报价开始时间
                }
                Date secondBidEndTime = project.getSecondBidEndTime();
                if (secondBidEndTime != null) {
                    if (com.fangcang.grfp.core.util.DateUtil.getDay(secondBidStartTime, secondBidEndTime) < 0) {
                        AppUtility.serviceError(ErrorCode.SECOND_BID_START_AND_END_TIME_UNPASS_VALIDATE);
                    }
                    Date thirdBidStartTime = project.getThirdBidStartTime();
                    if (thirdBidStartTime != null) {
                        if (com.fangcang.grfp.core.util.DateUtil.getDay(secondBidEndTime, thirdBidStartTime) < 0) {
                            AppUtility.serviceError(ErrorCode.SECOND_BID_END_TIME_AND_THIRD_BID_START_TIME_UNPASS_VALIDATE);
                        }
                        Date thirdBidEndTime = project.getThirdBidEndTime();
                        if (thirdBidEndTime != null) {
                            if (com.fangcang.grfp.core.util.DateUtil.getDay(thirdBidStartTime, thirdBidEndTime) < 0) {
                                AppUtility.serviceError(ErrorCode.THIRD_BID_START_END_TIME_UNPASS_VALIDATE);
                            }
                        }
                    }
                }
            }
        }
    }

    private ProjectEntity validateUpdateProject(UserSession userSession, Integer projectId){
        // 检查项目
        ProjectEntity dbProject = projectMapper.selectById(projectId);
        if(dbProject == null){
            AppUtility.serviceError(ErrorCode.DATA_NOT_FOUND);
        }
        if(userSession.getUserOrg().getOrgType().equals(OrgTypeEnum.DISTRIBUTOR.key) &&
                !userSession.getUserOrg().getOrgId().equals(dbProject.getTenderOrgId())){
            AppUtility.serviceError(ErrorCode.CANNOT_UPDATE_OTHER_ORG_PROJECT);
        }
        return dbProject;
    }

    /**
     * 批量添加项目 POI
     */
    public void batchAddProjectPoi(AddProjectPoiRequest req) {
        // 转换为 entity
        List<ProjectPoiEntity> entityList = req.getPoiIds().stream().map(poiId -> {
            ProjectPoiEntity entity = new ProjectPoiEntity();
            entity.setPoiId(poiId);
            entity.setProjectId(req.getProjectId());
            entity.setCreator(UserSession.get().getUsername());
            return entity;
        }).collect(Collectors.toList());

        // 插入
        projectPoiMapper.batchInsert(entityList);
    }

    /**
     * 删除项目 POI
     */
    public void deleteProjectPoi(DeleteProjectPoiRequest req) {
        projectPoiMapper.deleteByProjectIdAndPoiId(req.getProjectId(), req.getPoiId());
    }

    /**
     * 查询项目 POI 信息
     */
    public List<ProjectPoiVO> queryProjectPoiInfo(Integer projectId) {
        List<ProjectPoiVO> projectPoiList = projectPoiMapper.selectPoiInfoByProjectId(projectId, null);
        int language = AppUtility.getRequestHeaderLanguage(httpServletRequest);
        projectPoiList.forEach(poi -> poi.setCityName(AppUtility.getName(language, poi.getCityCode(), poi.getCityNameEnUs(), poi.getCityNameZhCn())));
        return projectPoiList;
    }

    /**
     * 新增项目自定义采购策略
     */
    public Long addProjectCustomTendStrategy(AddProjectCustomTendStrategyRequest req) {

        // 检查项目自定义测试数量不能超过 100
        Integer projectStrategyCount = projectCustomTendStrategyMapper.countByProjectId(req.getProjectId());
        if (projectStrategyCount >= 100) {
            AppUtility.serviceError(ErrorCode.PROJECT_CUSTOM_TEND_STRATEGY_COUNT_EXCEED_LIMIT);
        }

        // 校验策略名称是否存在
        ProjectCustomTendStrategyEntity existStrategy = projectCustomTendStrategyMapper.selectByProjectIdAndStrategyName(req.getProjectId(), req.getStrategyName());
        if (Objects.nonNull(existStrategy)) {
            AppUtility.serviceError(ErrorCode.PROJECT_CUSTOM_TEND_STRATEGY_NAME_EXIST);
        }

        // 查询最大的 display order
        Integer maxDisplayOrder = projectCustomTendStrategyMapper.selectMaxDisplayOrder(req.getProjectId());

        return transactionTemplate.execute(status -> {
            // 新增项目自定义测试策略
            ProjectCustomTendStrategyEntity entity = new ProjectCustomTendStrategyEntity();
            entity.setProjectId(req.getProjectId());
            entity.setStrategyName(req.getStrategyName());
            entity.setStrategyType(req.getStrategyType());
            entity.setDisplayOrder(Optional.ofNullable(maxDisplayOrder).orElse(0) + 1);
            entity.setSupportStrategyName(YesOrNoEnum.NO.getKey());
            entity.setWhtStrategyNameState(YesOrNoEnum.YES.getKey());
            entity.setWhtStrategyName(BigDecimal.ZERO);
            entity.setCreator(UserSession.get().getUsername());
            projectCustomTendStrategyMapper.insert(entity);

            // 如果是选项类型, 需要插入选项子表
            if (CustomStrategyTypeEnum.isOptionType(req.getStrategyType())) {
                List<AddProjectCustomStrategyOptionRequest> options = req.getOptions();
                MutableInt displayOrder = new MutableInt(1);
                List<ProjectCustomStrategyOptionEntity> optionList = options.stream().map(option -> {
                    ProjectCustomStrategyOptionEntity optionEntity = new ProjectCustomStrategyOptionEntity();
                    optionEntity.setStrategyId(entity.getId());
                    optionEntity.setOptionName(option.getOptionName());
                    optionEntity.setDisplayOrder(displayOrder.getAndIncrement());
                    optionEntity.setCreator(UserSession.get().getUsername());
                    return optionEntity;
                }).collect(Collectors.toList());
                // 批量插入
                projectCustomStrategyOptionMapper.batchInsert(optionList);
            }
            return entity.getId();
        });
    }

    /**
     * 删除项目自定义采购策略
     */
    @Transactional(rollbackFor = Throwable.class)
    public void deleteProjectCustomTendStrategy(DeleteProjectCustomTendStrategyRequest req) {
        // 删除策略
        projectCustomTendStrategyMapper.deleteByProjectIdAndStrategyId(req.getProjectId(), req.getStrategyId());
        // 删除策略选项
        projectCustomStrategyOptionMapper.deleteByStrategyId(req.getStrategyId());
    }

    /**
     * 更新自定义采购策略排序
     */
    @Transactional(rollbackFor = Throwable.class)
    public void switchCustomTendStrategyDisplayOrder(SwitchCustomTendStrategyDisplayOrderRequest req) {
        UserSession userSession = UserSession.get();

        // 查询 from 策略
        ProjectCustomTendStrategyEntity fromStrategy = projectCustomTendStrategyMapper.selectById(req.getFromCustomTendStrategyId());

        // 查询 to 策略
        ProjectCustomTendStrategyEntity toStrategy = projectCustomTendStrategyMapper.selectById(req.getToCustomTendStrategyId());

        // 更新 from 策略
        ProjectCustomTendStrategyEntity updateFromEntity = new ProjectCustomTendStrategyEntity();
        updateFromEntity.setId(fromStrategy.getId());
        updateFromEntity.setDisplayOrder(toStrategy.getDisplayOrder());
        updateFromEntity.setModifier(userSession.getUsername());
        projectCustomTendStrategyMapper.updateById(updateFromEntity);

        // 更新 to 策略
        ProjectCustomTendStrategyEntity updateToEntity = new ProjectCustomTendStrategyEntity();
        updateToEntity.setId(toStrategy.getId());
        updateToEntity.setDisplayOrder(fromStrategy.getDisplayOrder());
        updateToEntity.setModifier(userSession.getUsername());
        projectCustomTendStrategyMapper.updateById(updateToEntity);
    }

    /**
     * 查询项目自定义采购策略
     */
    public PageVO<CustomTendStrategyVO> queryProjectCustomTendStrategy(QueryCustomTendStrategyRequest req) {
        // 分页查询
        Page<CustomTendStrategyVO> pageRes = projectCustomTendStrategyMapper.selectByCondition(new Page<>(req.getPageIndex(), req.getPageSize()), req);
        List<CustomTendStrategyVO> records = pageRes.getRecords();
        if (CollUtil.isEmpty(records)) {
            return new PageVO<>(0, 0, records);
        }

        // 查询回复选项
        Set<Long> strategyIds = records.stream()
            .filter(e -> CustomStrategyTypeEnum.isOptionType(e.getStrategyType()))
            .map(CustomTendStrategyVO::getId).collect(Collectors.toSet());
        if (CollUtil.isNotEmpty(strategyIds)) {
            List<CustomStrategyOptionVO> options = projectCustomStrategyOptionMapper.selectByStrategyIds(strategyIds);
            // 按策略 id 分组
            Map<Long, List<CustomStrategyOptionVO>> optionsMap = options.stream().collect(Collectors.groupingBy(CustomStrategyOptionVO::getStrategyId));
            // 设值
            records.forEach(e -> e.setOptions(optionsMap.get(e.getId())));
        }
        return new PageVO<>(pageRes.getTotal(), pageRes.getPages(), records);
    }

    /**
     * 新增或修改酒店项目招标采购策略
     */
    @Transactional(rollbackFor = Throwable.class)
    public void addOrUpdateProjectHotelTendStrategy(AddProjectHotelTendStrategyRequest req) {
        // 新增或更新项目采购策略
        ProjectHotelTendStrategyEntity entity = new ProjectHotelTendStrategyEntity();
        BeanUtils.copyProperties(req, entity);
        entity.setCreator(UserSession.get().getUsername());
        entity.setModifier(UserSession.get().getUsername());
        projectHotelTendStrategyMapper.upsert(entity);

        // 更新自定义采购策略
        List<UpdateProjectCustomTendStrategyRequest> projectCustomTendStrategies = req.getProjectCustomTendStrategies();
        if (Objects.nonNull(projectCustomTendStrategies)) {
            // 分组更新, 这样最多更新两次
            Map<Integer, List<UpdateProjectCustomTendStrategyRequest>> strategiesMap = projectCustomTendStrategies.stream()
                .collect(Collectors.groupingBy(UpdateProjectCustomTendStrategyRequest::getSupportStrategyName));

            // 更新
            strategiesMap.forEach((k, v) -> {
                Set<Long> strategyIds = v.stream().map(UpdateProjectCustomTendStrategyRequest::getCustomTendStrategyId).collect(Collectors.toSet());
                LambdaUpdateWrapper<ProjectCustomTendStrategyEntity> updateWrapper = Wrappers.lambdaUpdate(ProjectCustomTendStrategyEntity.class);
                updateWrapper.in(ProjectCustomTendStrategyEntity::getId, strategyIds);
                updateWrapper.set(ProjectCustomTendStrategyEntity::getSupportStrategyName, k);
                projectCustomTendStrategyMapper.update(null, updateWrapper);
            });
        }
    }

    /**
     * 查询酒店项目招标采购策略
     */
    public ProjectHotelTendStrategyVO queryProjectHotelTendStrategy(QueryHotelTendStrategyRequest req) {
        return projectHotelTendStrategyMapper.selectByProjectId(req.getProjectId());
    }

    /**
     * 新增或修改酒店项目招标权重配置
     */
    @Transactional(rollbackFor = Throwable.class)
    public void addOrUpdateProjectHotelTendWeight(AddProjectHotelTendWeightRequest req) {
        // 计算总权重
        req.setWhtTotalWeight(calculateTotalWeight(req));

        // 插入或更新
        ProjectHotelTendWeightEntity entity = new ProjectHotelTendWeightEntity();
        BeanUtils.copyProperties(req, entity);
        entity.setCreator(UserSession.get().getUsername());
        entity.setModifier(UserSession.get().getUsername());
        projectHotelTendWeightMapper.upsert(entity);

        // 自定义策略为空直接结束
        List<UpdateCustomTendStrategyWeightRequest> projectCustomTendStrategies = req.getProjectCustomTendStrategies();
        if (CollUtil.isEmpty(projectCustomTendStrategies)) {
            return;
        }

        // 更新自定义策略
        List<ProjectCustomTendStrategyEntity> updateStrategies = new ArrayList<>(projectCustomTendStrategies.size());
        List<ProjectCustomStrategyOptionEntity> options = new ArrayList<>();
        projectCustomTendStrategies.forEach(item -> {
            ProjectCustomTendStrategyEntity updateEntity = new ProjectCustomTendStrategyEntity();
            updateEntity.setId(item.getCustomTendStrategyId());
            updateEntity.setWhtStrategyName(item.getWhtStrategyName());
            updateEntity.setWhtStrategyNameState(item.getWhtStrategyNameState());
            updateEntity.setModifier(UserSession.get().getUsername());
            updateStrategies.add(updateEntity);

            // 更新采购策略选项
            if (CollUtil.isEmpty(item.getOptions())) {
                return;
            }
            item.getOptions().forEach(option -> {
                ProjectCustomStrategyOptionEntity updateOptionEntity = new ProjectCustomStrategyOptionEntity();
                updateOptionEntity.setId(option.getOptionId());
                updateOptionEntity.setWeightScore(option.getWeightScore());
                updateOptionEntity.setModifier(UserSession.get().getUsername());
                options.add(updateOptionEntity);
            });
        });
        // 批量更新
        projectCustomTendStrategyMapper.batchUpdate(updateStrategies);
        if (CollUtil.isNotEmpty(options)) {
            projectCustomStrategyOptionMapper.batchUpdate(options);
        }
    }

    /**
     * 计算总权重分数
     */
    private BigDecimal calculateTotalWeight(AddProjectHotelTendWeightRequest req) {
        BigDecimal whtTotalWeight = BigDecimal.ZERO;
        if (YesOrNoEnum.YES.getKey().equals(req.getWhtRoomNightState())) {
            whtTotalWeight = NumberUtil.add(whtTotalWeight, req.getWhtRoomNight());
        }
        if (YesOrNoEnum.YES.getKey().equals(req.getWhtRoomNightExState())) {
            whtTotalWeight = NumberUtil.add(whtTotalWeight, req.getWhtRoomNightEx());
        }
        if (YesOrNoEnum.YES.getKey().equals(req.getWhtCityState())) {
            whtTotalWeight = NumberUtil.add(whtTotalWeight, req.getWhtCity());
        }
        if (YesOrNoEnum.YES.getKey().equals(req.getWhtLocationState())) {
            whtTotalWeight = NumberUtil.add(whtTotalWeight, req.getWhtLocation());
        }
        if (YesOrNoEnum.YES.getKey().equals(req.getWhtPriceAdvantageState())) {
            whtTotalWeight = NumberUtil.add(whtTotalWeight, req.getWhtPriceAdvantage());
        }
        if (YesOrNoEnum.YES.getKey().equals(req.getWhtPriceAdvantageExState())) {
            whtTotalWeight = NumberUtil.add(whtTotalWeight, req.getWhtPriceAdvantageEx());
        }
        if (YesOrNoEnum.YES.getKey().equals(req.getWhtOtaScoreState())) {
            whtTotalWeight = NumberUtil.add(whtTotalWeight, req.getWhtOtaScore());
        }
        if (YesOrNoEnum.YES.getKey().equals(req.getWhtOtaScoreState())) {
            whtTotalWeight = NumberUtil.add(whtTotalWeight, req.getWhtOtaScore());
        }
        if (YesOrNoEnum.YES.getKey().equals(req.getWhtCoPayState())) {
            whtTotalWeight = NumberUtil.add(whtTotalWeight, req.getWhtCoPay());
        }
        if (YesOrNoEnum.YES.getKey().equals(req.getWhtBreakfastState())) {
            whtTotalWeight = NumberUtil.add(whtTotalWeight, req.getWhtBreakfast());
        }
        if (YesOrNoEnum.YES.getKey().equals(req.getWhtLraState())) {
            whtTotalWeight = NumberUtil.add(whtTotalWeight, req.getWhtLra());
        }
        if (YesOrNoEnum.YES.getKey().equals(req.getWhtCancelState())) {
            whtTotalWeight = NumberUtil.add(whtTotalWeight, req.getWhtCancel());
        }

        // 计算自定义采购策略权重
        if (CollectionUtils.isEmpty(req.getProjectCustomTendStrategies())) {
            return whtTotalWeight;
        }
        for (UpdateCustomTendStrategyWeightRequest item : req.getProjectCustomTendStrategies()) {
            // 权重开关没开, 直接跳过
            if (!YesOrNoEnum.YES.getKey().equals(item.getWhtStrategyNameState())) {
                continue;
            }
            // 如果是回复选项类型, 需要累加选项权重
            if (CollUtil.isNotEmpty(item.getOptions())) {
                for (UpdateCustomStrategyOptionWeightRequest option : item.getOptions()) {
                    whtTotalWeight = NumberUtil.add(whtTotalWeight, option.getWeightScore());
                }
                continue;
            }
            // 其它类型直接累加
            whtTotalWeight = NumberUtil.add(whtTotalWeight, item.getWhtStrategyName());
        }
        return whtTotalWeight;
    }

    /**
     * 查询酒店项目招标权重配置
     */
    public ProjectHotelTendWeightVO queryProjectHotelTendWeight(QueryProjectHotelTendWeightRequest req) {
        return projectHotelTendWeightMapper.selectByProjectId(req.getProjectId());
    }

    /**
     * 新增项目酒店白名单
     */
    public void addProjectHotelWhite(@Valid AddProjectHotelWhiteRequest req) {
        // 查询项目
        ProjectEntity project = projectMapper.selectById(req.getProjectId());
        if (Objects.isNull(project)) {
            AppUtility.serviceError(ErrorCode.PROJECT_NOT_EXIST);
        }

        // 查询酒店存不存在, 任意语言都行
        List<HotelEntity> hotelList = hotelManager.queryHotelBasicInfo(Collections.singletonList(req.getHotelId()), true);
        if (CollUtil.isEmpty(hotelList)) {
            AppUtility.serviceError(ErrorCode.HOTEL_NOT_EXIST);
        }

        // 插入或更新
        ProjectHotelWhiteEntity entity = new ProjectHotelWhiteEntity();
        BeanUtils.copyProperties(req, entity);
        entity.setCreator(UserSession.get().getUsername());
        entity.setModifier(UserSession.get().getUsername());
        projectHotelWhiteMapper.upsert(entity);
    }

    /**
     * 删除项目酒店白名单
     */
    public void deleteProjectHotelWhite(DeleteProjectHotelWhiteRequest req) {
        LambdaQueryWrapper<ProjectHotelWhiteEntity> queryWrapper = Wrappers.lambdaQuery(ProjectHotelWhiteEntity.class);
        queryWrapper.eq(ProjectHotelWhiteEntity::getProjectId, req.getProjectId());
        queryWrapper.eq(ProjectHotelWhiteEntity::getHotelId, req.getHotelId());
        queryWrapper.eq(ProjectHotelWhiteEntity::getHotelWhiteType, req.getHotelWhiteType());
        projectHotelWhiteMapper.delete(queryWrapper);
    }

    /**
     * 查询项目酒店白名单详情
     */
    public PageVO<ProjectHotelWhiteVO> queryProjectHotelWhiteDetail(@Valid QueryProjectHotelWhiteRequest req) {
        // 查询数据
        Page<ProjectHotelWhiteVO> pageRes = projectHotelWhiteMapper.selectByCondition(new Page<>(req.getPageIndex(), req.getPageSize()), req);
        List<ProjectHotelWhiteVO> records = pageRes.getRecords();
        if (CollUtil.isEmpty(records)) {
            return new PageVO<>(0, 0, records);
        }

        // 处理数据
        int language = AppUtility.getRequestHeaderLanguage(httpServletRequest);
        records.forEach(record -> {
            record.setHotelName(AppUtility.getName(language, record.getHotelId(), record.getHotelNameEnUs(), record.getHotelNameZhCn()));
            record.setCityName(AppUtility.getName(language, record.getCityCode(), record.getCityNameEnUs(), record.getCityNameZhCn()));
        });
        return new PageVO<>(pageRes.getTotal(), pageRes.getPages(), records);
    }

    /**
     * 查询项目绑定机构历史项目
     */
    public PageVO<HistoryProjectInfoVO> queryHistoryProjectInfo(QueryHistoryProjectRequest req) {
        // 查询项目
        ProjectEntity project = projectMapper.selectById(req.getProjectId());
        if (Objects.isNull(project)) {
            AppUtility.serviceError(ErrorCode.PROJECT_NOT_EXIST);
        }

        // 查询项目招标机构绑定的项目
        LambdaQueryWrapper<ProjectEntity> queryWrapper = Wrappers.lambdaQuery(ProjectEntity.class);
        queryWrapper.eq(ProjectEntity::getTenderOrgId, project.getTenderOrgId());
        queryWrapper.ne(ProjectEntity::getProjectId, project.getProjectId());
        queryWrapper.in(ProjectEntity::getProjectState, ProjectStateEnum.STARTED.key, ProjectStateEnum.BIDDING_COMPLETED.key);
        queryWrapper.orderByDesc(ProjectEntity::getProjectName);
        Page<ProjectEntity> pageRes = projectMapper.selectPage(new Page<>(req.getPageIndex(), req.getPageSize()), queryWrapper);
        List<ProjectEntity> records = pageRes.getRecords();
        if (CollUtil.isEmpty(records)) {
            return new PageVO<>(0, 0, Collections.emptyList());
        }

        // 转换 VO
        List<HistoryProjectInfoVO> res = records.stream().map(item -> {
            HistoryProjectInfoVO vo = new HistoryProjectInfoVO();
            vo.setProjectId(item.getProjectId());
            vo.setProjectName(item.getProjectName());
            return vo;
        }).collect(Collectors.toList());
        return new PageVO<>(pageRes.getTotal(), pageRes.getPages(), res);
    }

    /**
     * 绑定历史项目
     */
    public void bindHistoryProject(@Valid BindHistoryProjectRequest req) {
        // 查询项目
        ProjectEntity project = projectMapper.selectById(req.getProjectId());
        if (Objects.isNull(project)) {
            AppUtility.serviceError(ErrorCode.PROJECT_NOT_EXIST);
        }

        ProjectEntity updateEntity = new ProjectEntity();
        updateEntity.setProjectId(project.getProjectId());
        updateEntity.setRelatedProjectId(req.getRelatedProjectId());
        updateEntity.setModifier(UserSession.get().getUsername());
        projectMapper.updateById(updateEntity);
    }

    /**
     * 查询关联项目信息
     */
    public RelatedProjectInfoVO queryRelatedProjectInfo(@Valid IdRequest<Integer> req) {
        // 查询项目
        ProjectEntity project = projectMapper.selectById(req.getId());
        if (Objects.isNull(project)) {
            AppUtility.serviceError(ErrorCode.PROJECT_NOT_EXIST);
        }

        // 查询关联项目
        Integer relatedProjectId = project.getRelatedProjectId();
        if (Objects.isNull(relatedProjectId)) {
            return null;
        }
        ProjectEntity relatedProject = projectMapper.selectById(relatedProjectId);
        if (Objects.isNull(relatedProject)) {
            return null;
        }

        // 转换对象
        RelatedProjectInfoVO vo = new RelatedProjectInfoVO();
        vo.setProjectId(relatedProject.getProjectId());
        vo.setProjectName(relatedProject.getProjectName());
        return vo;
    }

    /**
     * 批量导入酒店历史数据
     */
    public IdVO<Long> batchAddHotelHistoryTradeData(MultipartFile file, Integer projectId) throws IOException {
        log.info("batchAddHotelHistoryTradeData projectId: {}", projectId);
        // 查询项目
        ProjectEntity project = projectMapper.selectById(projectId);
        if (Objects.isNull(project)) {
            AppUtility.serviceError(ErrorCode.PROJECT_NOT_EXIST);
        }

        // 处理导入
        ImportExcelContext<ImportProjectHotelHistoryTradeDataVO> context = new ImportExcelContext<>();
        context.getExtraParams().put("projectId", projectId);
        context.setFile(file);
        context.setBizType(ImportBizTypeEnum.IMPORT_PROJECT_HOTEL_HISTORY_TRADE_DATA);
        context.setImportVOClass(ImportProjectHotelHistoryTradeDataVO.class);
        context.setImportLogic(this::handleImportHotelTradeData);
        context.setBatchSize(100);
        return new IdVO<>(excelManager.generalImport(context));
    }

    private List<ImportRowErrorVO> handleImportHotelTradeData(ImportExcelContext<ImportProjectHotelHistoryTradeDataVO> context, List<ImportProjectHotelHistoryTradeDataVO> importList) {
        // 查询酒店信息
        List<Long> hotelIds = importList.stream()
            .filter(e -> NumberUtil.isNumber(e.getHotelId()))
            .map(item -> Long.valueOf(item.getHotelId()))
            .collect(Collectors.toList());
        List<HotelEntity> hotelList = hotelManager.queryHotelBasicInfo(hotelIds, true);
        Map<Long, HotelEntity> hotelIdMap = hotelList.stream().collect(Collectors.toMap(HotelEntity::getHotelId, Function.identity(), (k1, k2) -> k1));

        // 校验机构是否存在
        Integer projectId = context.getExtra("projectId", Integer.class);
        List<ImportRowErrorVO> errors = new ArrayList<>();
        List<ProjectHotelHistoryDataEntity> insertList = new ArrayList<>(importList.size());
        importList.forEach(item -> {
            ImportRowErrorVO rowError = new ImportRowErrorVO(item.getRowNum());

            // 校验
            validateHotelTradeData(item, rowError, hotelIdMap);

            // 校验不通过, 跳过
            if (rowError.hasError()) {
                errors.add(rowError);
                return;
            }

            // 获取酒店信息
            HotelEntity hotelInfo = hotelIdMap.get(Long.valueOf(item.getHotelId()));
            // 转换 entity
            ProjectHotelHistoryDataEntity projectHotelHistoryData = new ProjectHotelHistoryDataEntity();
            projectHotelHistoryData.setProjectId(projectId);
            projectHotelHistoryData.setHotelId(Long.valueOf(item.getHotelId()));
            projectHotelHistoryData.setRoomNightCount(Integer.valueOf(item.getRoomNightCount()));
            projectHotelHistoryData.setTotalAmount(new BigDecimal(item.getTotalAmount()));
            projectHotelHistoryData.setIsUploaded(YesOrNoEnum.YES.getKey());
            projectHotelHistoryData.setCityCode(hotelInfo.getCityCode());
            projectHotelHistoryData.setCreator(context.getUserSession().getUsername());
            projectHotelHistoryData.setModifier(context.getUserSession().getUsername());
            insertList.add(projectHotelHistoryData);
        });

        // 插入 POI
        if (CollUtil.isNotEmpty(insertList)) {
            projectHotelHistoryDataMapper.batchUpsert(insertList);
        }
        return errors;
    }

    /**
     * 校验导入数据
     */
    private static void validateHotelTradeData(ImportProjectHotelHistoryTradeDataVO item, ImportRowErrorVO rowError, Map<Long, HotelEntity> hotelIdMap) {
        // 校验酒店存不存在
        if (!NumberUtil.isNumber(item.getHotelId()) || Objects.isNull(hotelIdMap.get(Long.valueOf(item.getHotelId())))) {
            rowError.addError(ErrorCode.HOTEL_NOT_EXIST);
        }
        // 校验间夜数
        if (!NumberUtil.isNumber(item.getRoomNightCount()) || Integer.parseInt(item.getRoomNightCount()) < 0) {
            rowError.addError(ErrorCode.ROOM_NIGHT_COUNT_INVALID);
        }
        // 校验金额
        if (!NumberUtil.isNumber(item.getTotalAmount()) || NumberUtil.isLess(new BigDecimal(item.getTotalAmount()), BigDecimal.ZERO)) {
            rowError.addError(ErrorCode.AMOUNT_INVALID);
        }
    }

    /**
     * 删除项目酒店历史交易数据
     */
    public void deleteHotelHistoryTradeData(@Valid DeleteHotelHistoryTradeDataRequest req) {
        LambdaQueryWrapper<ProjectHotelHistoryDataEntity> queryWrapper = Wrappers.lambdaQuery(ProjectHotelHistoryDataEntity.class);
        queryWrapper.eq(ProjectHotelHistoryDataEntity::getProjectId, req.getProjectId());
        queryWrapper.eq(ProjectHotelHistoryDataEntity::getHotelId, req.getHotelId());
        projectHotelHistoryDataMapper.delete(queryWrapper);
    }

    /**
     * 编辑项目酒店历史交易数据
     */
    public void editHotelHistoryTradeData(@Valid EditHotelHistoryTradeDataRequest req) {
        // 更新数据
        LambdaUpdateWrapper<ProjectHotelHistoryDataEntity> updateWrapper = Wrappers.lambdaUpdate(ProjectHotelHistoryDataEntity.class);
        updateWrapper.eq(ProjectHotelHistoryDataEntity::getProjectId, req.getProjectId());
        updateWrapper.eq(ProjectHotelHistoryDataEntity::getHotelId, req.getHotelId());
        updateWrapper.set(ProjectHotelHistoryDataEntity::getRoomNightCount, req.getRoomNightCount());
        updateWrapper.set(ProjectHotelHistoryDataEntity::getTotalAmount, req.getTotalAmount());
        updateWrapper.set(ProjectHotelHistoryDataEntity::getModifier, UserSession.get().getUsername());
        projectHotelHistoryDataMapper.update(null, updateWrapper);
    }

    /**
     * 查询项目酒店历史交易数据列表
     */
    public PageVO<HotelHistoryTradeDataListVO> queryHotelHistoryTradeDataList(@Valid HotelHistoryTradeDataListRequest req) {
        // 分页查询数据
        Page<HotelHistoryTradeDataListVO> pageRes = projectHotelHistoryDataMapper.selectByCondition(new Page<>(req.getPageIndex(), req.getPageSize()), req);
        List<HotelHistoryTradeDataListVO> records = pageRes.getRecords();
        if (CollUtil.isEmpty(records)) {
            return new PageVO<>(0, 0, records);
        }

        // 处理字段信息
        int language = AppUtility.getRequestHeaderLanguage(httpServletRequest);
        records.forEach(item -> {
            item.setCityName(AppUtility.getCityName(language, item.getCityCode()));
            item.setHotelName(AppUtility.getHotelName(language, item.getHotelId()));
            item.setHotelBrandName(AppUtility.getHotelBrandName(language, item.getHotelBrandId()));
            item.setHotelGroupName(AppUtility.getHotelGroupName(language, item.getHotelGroupId()));
        });

        return new PageVO<>(pageRes.getTotal(), pageRes.getPages(), records);
    }

    public PageVO<ProjectIntentHotelVO> queryProjectIntentHotelList(QueryProjectIntentHotelRequest req) {
        Page<ProjectIntentHotelVO> page = new Page<>(req.getPageIndex(), req.getPageSize());
        IPage<ProjectIntentHotelVO> pageResult = projectIntentHotelMapper.queryProjectIntentHotelList(page, req);

        List<ProjectIntentHotelVO> records = pageResult.getRecords();
        if (!CollectionUtils.isEmpty(records)) {
            int language = AppUtility.getRequestHeaderLanguage(httpServletRequest);
            records.forEach(hotel -> {
                hotel.setHotelName(AppUtility.getName(language, (String) null, hotel.getHotelNameEnUs(), hotel.getHotelNameZhCn()));
                hotel.setHotelAddress(AppUtility.getName(language, (String) null, hotel.getHotelAddressEnUs(), hotel.getHotelAddressZhCn()));
                hotel.setCityName(AppUtility.getName(language, hotel.getCityCode(), hotel.getCityNameEnUs(), hotel.getCityNameZhCn()));
                if (hotel.getHotelStar() != null) {
                    hotel.setHotelStarDesc(HotelStarEnum.getTextByKey(hotel.getHotelStar(), language));
                }
            });
        }

        return new PageVO<>(pageResult.getTotal(), pageResult.getPages(), records);
    }

    public void deleteProjectIntentHotel(Long projectIntentHotelId) {
        if (projectIntentHotelId == null || projectIntentHotelId <= 0) {
            AppUtility.serviceError(ErrorCode.REQUEST_PARAMETER_ERROR);
        }

        // 判断意向酒店的投标状态
        ProjectIntentHotelEntity projectIntentHotel = projectIntentHotelMapper.selectById(projectIntentHotelId);
        if (projectIntentHotel == null) {
            AppUtility.serviceError(ErrorCode.DATA_NOT_FOUND);
        }

        if (!Objects.equals(projectIntentHotel.getBidState(), HotelBidStateEnum.NO_BID.bidState)) {
            AppUtility.serviceError(ErrorCode.HOTEL_HAS_BID_CANNOT_DELETE);
        }

        bidManager.clearProjectHotelBidData(projectIntentHotel);
    }

    /**
     * 生成项目 POI 统计
     */
    public void generateHistoryProjectStat(@Valid IdRequest<Integer> req) {
        Integer projectId = req.getId();
        // 查询项目信息
        ProjectEntity project = projectMapper.selectById(projectId);

        // 获取锁
        String lockKey = String.format(RedisConstant.GENERATE_HISTORY_PROJECT_STAT_LOCK_KEY, projectId);
        RLock lock = redissonClient.getLock(lockKey);
        try {
            if (!lock.tryLock()) {
                log.info("项目 {} 上一次统计未完成", projectId);
                AppUtility.serviceError(ErrorCode.GENERATE_PROJECT_HISTORY_STAT_NOT_FINISH);
            }
            // 项目历史成交间夜和金额数据
            List<QueryHistoryProjectInfoResponse> hotelHistoryTradeDataList = projectHotelHistoryDataMapper.queryHistoryProjectHotelList(projectId, null, null, null,null);

            // 查询过去一年节省金额和间夜数
            DisHotelDailyOrderRequest disHotelDailyOrderRequest = new DisHotelDailyOrderRequest();
            disHotelDailyOrderRequest.setStartTime(DateUtil.formatDate(DateUtil.offset(new Date(), DateField.YEAR, -1)));
            disHotelDailyOrderRequest.setEndTime(DateUtil.formatDate(new Date()));
            disHotelDailyOrderRequest.setOrgId(Long.valueOf(project.getTenderOrgId()));
            List<DisHotelDailyOrderResponse> disHotelDailyOrderResponseList = disHotelDailyOrderMapper.selectHotelSavedAmountStatList(disHotelDailyOrderRequest);
            Map<Long, DisHotelDailyOrderResponse> hotelDailyOrderResponseMap = disHotelDailyOrderResponseList.stream().collect(Collectors.toMap(DisHotelDailyOrderResponse::getHotelId, Function.identity()));

            // 获取项目 POI 信息
            List<ProjectPoiInfoResponse> projectPoiInfoResponseList = projectPoiMapper.selectProjectPoiInfo(project.getProjectId());
            Map<String, List<ProjectPoiInfoResponse>> cityProjectPoiInfoResponseMap = projectPoiInfoResponseList.stream().collect(Collectors.groupingBy(ProjectPoiInfoResponse::getCityCode));

            // 统计城市间夜数总数 （酒店城市间夜数排名，节省金额）
            Map<String, Integer> cityStatPoiRoomNightMap = new HashMap<>();
            Map<String, List<QueryHistoryProjectInfoResponse>> cityQueryHistoryProjectInfoResponseMap = new HashMap<>();
            for (QueryHistoryProjectInfoResponse hotelHistoryData : hotelHistoryTradeDataList) {
                HotelUtility.calculateNullGoogleLngLat(hotelHistoryData);
                int dbCityOrder = hotelHistoryData.getCityOrder();
                BigDecimal dbSavedAmount = hotelHistoryData.getSavedAmount();

                // 城市排名
                List<QueryHistoryProjectInfoResponse> cityList = cityQueryHistoryProjectInfoResponseMap.computeIfAbsent(hotelHistoryData.getCityCode(), k -> new LinkedList<>());
                if (cityList.isEmpty()) {
                    hotelHistoryData.setCityOrder(1);
                } else {
                    QueryHistoryProjectInfoResponse last = cityList.get(cityList.size() - 1);
                    hotelHistoryData.setCityOrder(Objects.equals(last.getRoomNightCount(), hotelHistoryData.getRoomNightCount()) ? last.getCityOrder() : last.getCityOrder() + 1);
                }
                cityList.add(hotelHistoryData);

                // 累加 poi城市间夜总数
                cityStatPoiRoomNightMap.merge(hotelHistoryData.getCityCode(), hotelHistoryData.getRoomNightCount(), Integer::sum);

                // 更新去年节省金额
                DisHotelDailyOrderResponse disHotelDailyOrderResponse = hotelDailyOrderResponseMap.get(hotelHistoryData.getHotelId());
                if (disHotelDailyOrderResponse != null) {
                    hotelHistoryData.setSavedAmount(disHotelDailyOrderResponse.getSavedAmount());
                    hotelHistoryData.setSavedAmountRate(BigDecimal.ZERO);
                    if(disHotelDailyOrderResponse.getTotalOtaPrice() != null && NumberUtil.isGreater(disHotelDailyOrderResponse.getTotalOtaPrice(), BigDecimal.ZERO)){
                        hotelHistoryData.setSavedAmountRate(disHotelDailyOrderResponse.getSavedAmount().divide(disHotelDailyOrderResponse.getTotalOtaPrice(), 3, RoundingMode.HALF_UP));
                    }
                }

                // 更新统计数据 (有变化才更新)
                if (dbCityOrder != hotelHistoryData.getCityOrder() || !NumberUtil.equals(hotelHistoryData.getSavedAmount(), dbSavedAmount)) {
                    projectHotelHistoryDataMapper.updateHistoryProjectDataStat(hotelHistoryData);
                }

                // 获取同城POI列表 获取最近POI距离和ID
                List<ProjectPoiInfoResponse> cityProjectPoiInfoList = cityProjectPoiInfoResponseMap.get(hotelHistoryData.getCityCode());
                if(CollectionUtils.isNotEmpty(cityProjectPoiInfoList) && hotelHistoryData.getLngGoogle() != null && hotelHistoryData.getLatGoogle() != null){
                    BigDecimal minDistance = BigDecimal.ZERO;
                    Long poiId = 0L;
                    for(ProjectPoiInfoResponse projectPoiInfoResponse : cityProjectPoiInfoList){
                        BigDecimal distance = LocationUtil.getKmDistance(projectPoiInfoResponse.getLatGoogle(), projectPoiInfoResponse.getLngGoogle(),
                            hotelHistoryData.getLatGoogle(), hotelHistoryData.getLngGoogle());
                        if(NumberUtil.equals(distance, BigDecimal.ZERO)){
                            poiId = projectPoiInfoResponse.getPoiId();
                            minDistance = distance;
                            break;
                        }
                        if(NumberUtil.equals(minDistance, BigDecimal.ZERO) || NumberUtil.isLess(distance, minDistance)){
                            minDistance = distance;
                            poiId = projectPoiInfoResponse.getPoiId();
                        }
                    }
                    if(poiId > 0 && (!Objects.equals(hotelHistoryData.getPoiId(), poiId) || !NumberUtil.equals(minDistance, hotelHistoryData.getPoiDistance()))){
                        hotelHistoryData.setPoiId(poiId);
                        hotelHistoryData.setPoiDistance(minDistance);
                        projectHotelHistoryDataMapper.updateHistoryProjectPoi(hotelHistoryData);
                    }
                }
            }

            // 统计去年项目POI范围间夜数和城市占成
            // 清空之前统计数据
            projectPoiMapper.clearCityPoiStat(project.getProjectId());

            // 统计POI附近3公里间夜数
            Map<Long, ProjectPoiEntity> statProjectPoiMap = new HashMap<>();
            Map<Long, String> projectPoiCityMap = new HashMap<>();
            Map<Long, List<QueryHistoryProjectInfoResponse>> poiHistoryDataMap =
                hotelHistoryTradeDataList.stream().collect(Collectors.groupingBy(QueryHistoryProjectInfoResponse::getPoiId));
            for (ProjectPoiInfoResponse projectPoiInfoResponse : projectPoiInfoResponseList) {
                List<QueryHistoryProjectInfoResponse> poiHistoryDataList = poiHistoryDataMap.get(projectPoiInfoResponse.getPoiId());
                if(CollectionUtils.isEmpty(poiHistoryDataList)){
                    continue;
                }

                int poiTotalRoomNightCount3Km = 0;
                BigDecimal poiTotalAmount3Km = BigDecimal.ZERO;
                int poiTotalRoomNightCount5Km = 0;
                BigDecimal poiTotalAmount5Km = BigDecimal.ZERO;
                int poiTotalRoomNightCount10Km = 0;
                BigDecimal poiTotalAmount10Km = BigDecimal.ZERO;

                // 统计同城3公里的总数
                for(QueryHistoryProjectInfoResponse queryHistoryProjectInfoResponse : poiHistoryDataList){
                    if(NumberUtil.isLessOrEqual(queryHistoryProjectInfoResponse.getPoiDistance(), new BigDecimal("3"))) {
                        poiTotalRoomNightCount3Km = poiTotalRoomNightCount3Km + queryHistoryProjectInfoResponse.getRoomNightCount();
                        poiTotalAmount3Km = poiTotalAmount3Km.add(queryHistoryProjectInfoResponse.getTotalAmount());
                    }
                    if(NumberUtil.isLessOrEqual(queryHistoryProjectInfoResponse.getPoiDistance(), new BigDecimal("5"))) {
                        poiTotalRoomNightCount5Km = poiTotalRoomNightCount5Km + queryHistoryProjectInfoResponse.getRoomNightCount();
                        poiTotalAmount5Km = poiTotalAmount5Km.add(queryHistoryProjectInfoResponse.getTotalAmount());
                    }
                    if(NumberUtil.isLessOrEqual(queryHistoryProjectInfoResponse.getPoiDistance(), new BigDecimal("10"))) {
                        poiTotalRoomNightCount10Km = poiTotalRoomNightCount10Km + queryHistoryProjectInfoResponse.getRoomNightCount();
                        poiTotalAmount10Km = poiTotalAmount10Km.add(queryHistoryProjectInfoResponse.getTotalAmount());
                    }
                }

                if (poiTotalRoomNightCount3Km > 0 || poiTotalRoomNightCount5Km > 0 || poiTotalRoomNightCount10Km > 0) {
                    ProjectPoiEntity projectPoi = new ProjectPoiEntity();
                    projectPoi.setProjectId(project.getProjectId());
                    projectPoi.setPoiId(projectPoiInfoResponse.getPoiId());
                    projectPoi.setTotalNightRoomCount(poiTotalRoomNightCount3Km);
                    projectPoi.setTotalAmount(poiTotalAmount3Km);
                    projectPoi.setTotalNightRoomCount5km(poiTotalRoomNightCount5Km);
                    projectPoi.setTotalAmount5km(poiTotalAmount5Km);
                    projectPoi.setTotalNightRoomCount10km(poiTotalRoomNightCount10Km);
                    projectPoi.setTotalAmount10km(poiTotalAmount10Km);
                    statProjectPoiMap.put(projectPoiInfoResponse.getPoiId(), projectPoi);
                    projectPoiCityMap.put(projectPoiInfoResponse.getPoiId(), projectPoiInfoResponse.getCityCode());
                }
            }

            // 更新去年项目POI 统计数据
            for (Long poiId : statProjectPoiMap.keySet()) {
                ProjectPoiEntity projectPoi = statProjectPoiMap.get(poiId);
                String cityCode = projectPoiCityMap.get(poiId);
                int totalCityRoomNight = cityStatPoiRoomNightMap.get(cityCode);
                projectPoi.setCityNightRoomRisk(BigDecimal.valueOf(projectPoi.getTotalNightRoomCount()).divide(BigDecimal.valueOf(totalCityRoomNight),
                    2, RoundingMode.HALF_UP));
                projectPoi.setCityNightRoomRisk5km(BigDecimal.valueOf(projectPoi.getTotalNightRoomCount5km()).divide(BigDecimal.valueOf(totalCityRoomNight),
                    2, RoundingMode.HALF_UP));
                projectPoi.setCityNightRoomRisk10km(BigDecimal.valueOf(projectPoi.getTotalNightRoomCount10km()).divide(BigDecimal.valueOf(totalCityRoomNight),
                    2, RoundingMode.HALF_UP));
                projectPoi.setModifier(UserSession.get().getUsername());
                projectPoiMapper.updateProjectPoiStatInfo(projectPoi);
            }

            // 城市维度统计间夜数 （报价页面使用）
            calculateProjectLastYearCityStat(hotelHistoryTradeDataList, project.getProjectId());

            // 生成或者更新酒店10公里范围六边形百度坐标
            List<ProjectIntentHotelEntity> projectIntentHotelList = projectIntentHotelMapper.selectByProjectId(project.getProjectId());
            List<Long> hotelIds = projectIntentHotelList.stream().map(ProjectIntentHotelEntity::getHotelId).collect(Collectors.toList());
            // 异步分批执行
            Lists.partition(hotelIds, 1000).forEach(subHotelIds -> {
                GenerateHotelHexagonLngLatThread generateHotelHexagonLngLatThread = new GenerateHotelHexagonLngLatThread(subHotelIds, hotelMapper, hotelHexagonLngLatManager);
                hotelHexagonGenerateExecutor.execute(generateHotelHexagonLngLatThread);
            });
        } catch (Throwable e) {
            log.error("生成项目 POI 统计错误", e);
            AppUtility.serviceError(ErrorCode.GENERATE_PROJECT_HISTORY_STAT_ERROR);
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }

    }

    /**
     * 计算项目去年城市间夜数统计
     */
    private void calculateProjectLastYearCityStat(List<QueryHistoryProjectInfoResponse> hotelHistoryTradeDataList, int projectId) {
        // 定义常量
        BigDecimal bigDecimal5 = new BigDecimal("500");
        BigDecimal bigDecimal4 = new BigDecimal("400");
        BigDecimal bigDecimal3 = new BigDecimal("300");
        BigDecimal bigDecimal2 = new BigDecimal("200");
        BigDecimal bigDecimal1 = new BigDecimal("100");

        // 按城市分组处理数据
        Map<String, List<QueryHistoryProjectInfoResponse>> cityHistoryDataListMap = hotelHistoryTradeDataList.stream()
                .filter(data -> data.getRoomNightCount() != 0 && !NumberUtil.equals(data.getTotalAmount(), BigDecimal.ZERO))
                .collect(Collectors.groupingBy(QueryHistoryProjectInfoResponse::getCityCode));

        cityHistoryDataListMap.forEach((cityCode, cityHistoryDataList) -> {
            // 新建统计 entity
            ProjectLastYearCityStatEntity projectLastYearCityStat = new ProjectLastYearCityStatEntity();
            projectLastYearCityStat.setProjectId(projectId);
            projectLastYearCityStat.setCityCode(cityCode);
            projectLastYearCityStat.setCreator(RfpConstant.CREATOR);
            projectLastYearCityStat.setModifier(RfpConstant.CREATOR);

            int totalCityRoomCount = 0;
            BigDecimal totalCitySaleAmount = BigDecimal.ZERO;
            int totalRoomNight500 = 0; // 500 以上间夜
            int totalRoomNight400To500 = 0; // 400-500间夜数
            int totalRoomNight300To400 = 0; // 300-400间夜数
            int totalRoomNight200To300 = 0; // 200-300间夜数
            int totalRoomNight100To200 = 0; // 100-200间夜数
            int totalRoomNight100 = 0; // 100 以内

            // 统计数据计算
            for (QueryHistoryProjectInfoResponse historyData : cityHistoryDataList) {
                totalCityRoomCount +=  historyData.getRoomNightCount();
                totalCitySaleAmount = totalCitySaleAmount.add(historyData.getTotalAmount());
                BigDecimal avgPrice = historyData.getTotalAmount().divide(BigDecimal.valueOf(historyData.getRoomNightCount()), 0, RoundingMode.HALF_UP);

                // 统计成交间夜数
                if (avgPrice.compareTo(bigDecimal5) >= 0) {
                    totalRoomNight500 += historyData.getRoomNightCount();
                } else if (avgPrice.compareTo(bigDecimal5) < 0 && avgPrice.compareTo(bigDecimal4) >= 0) {
                    totalRoomNight400To500 += historyData.getRoomNightCount();
                } else if (avgPrice.compareTo(bigDecimal4) < 0 && avgPrice.compareTo(bigDecimal3) >= 0) {
                    totalRoomNight300To400 += historyData.getRoomNightCount();
                } else if (avgPrice.compareTo(bigDecimal3) < 0 && avgPrice.compareTo(bigDecimal2) >= 0) {
                    totalRoomNight200To300 += historyData.getRoomNightCount();
                } else if (avgPrice.compareTo(bigDecimal2) < 0 && avgPrice.compareTo(bigDecimal1) >= 0) {
                    totalRoomNight100To200 += historyData.getRoomNightCount();
                } else if (avgPrice.compareTo(bigDecimal2) < 0) {
                    totalRoomNight100 += historyData.getRoomNightCount();
                }
            }

            projectLastYearCityStat.setTotalRoomNight(totalCityRoomCount);
            projectLastYearCityStat.setTotalSalesAmount(totalCitySaleAmount);
            projectLastYearCityStat.setTotalRoomNight500(totalRoomNight500);
            projectLastYearCityStat.setTotalRoomNight400To500(totalRoomNight400To500);
            projectLastYearCityStat.setTotalRoomNight300To400(totalRoomNight300To400);
            projectLastYearCityStat.setTotalRoomNight200To300(totalRoomNight200To300);
            projectLastYearCityStat.setTotalRoomNight100To200(totalRoomNight100To200);
            projectLastYearCityStat.setTotalRoomNight100(totalRoomNight100);
            projectLastYearCityStatMapper.upsert(projectLastYearCityStat);
        });
    }


    @Transactional
    public Integer updateProjectIntentHotel(UpdateProjectIntentHotelRequest updateProjectIntentHotelRequest) {
        UserSession userSession = UserSession.get();
        updateProjectIntentHotelRequest.setOperator(userSession.getUsername());
        Integer operateType = updateProjectIntentHotelRequest.getOperateType();
        if(operateType == 1){
            String contactEmail = updateProjectIntentHotelRequest.getHotelSalesContactEmail();
            if(StringUtil.isValidString(contactEmail) && !ValidateUtil.isValidEmail(contactEmail)){
                AppUtility.serviceError(ErrorCode.PROJECT_INTENT_HOTEL_SALES_CONTACT_EMAIL);
            }
        }
        //指派企业跟进人时，指派跟进人信息不能为空
        if (updateProjectIntentHotelRequest.getOperateType() == 3) {
            if (ObjectUtils.isEmpty(updateProjectIntentHotelRequest.getDistributorContactName())) {
                AppUtility.serviceError(ErrorCode.PROJECT_INTENT_HOTEL_DISTRIBUTOR_CONTACT_NAME);
            }
            if (ObjectUtils.isEmpty(updateProjectIntentHotelRequest.getDistributorContactUid())) {
                AppUtility.serviceError(ErrorCode.PROJECT_INTENT_HOTEL_DISTRIBUTOR_CONTACT_ID);
            }
        } else if (updateProjectIntentHotelRequest.getOperateType() == 9) {
            //指派平台跟进人时，指派跟进人信息不能为空
            if (ObjectUtils.isEmpty(updateProjectIntentHotelRequest.getPlatformContactName())) {
                AppUtility.serviceError(ErrorCode.PROJECT_INTENT_HOTEL_PLATFORM_CONTACT_NAME);
            }
            if (ObjectUtils.isEmpty(updateProjectIntentHotelRequest.getPlatformContactUid())) {
                AppUtility.serviceError(ErrorCode.PROJECT_INTENT_HOTEL_PLATFORM_CONTACT_UID);
            }
        }
        return projectIntentHotelMapper.updateProjectIntentHotel(updateProjectIntentHotelRequest);
    }

    /**
     * 项目-邀请酒店-导入酒店
     *
     * @param file
     * @param projectId
     * @return
     */
    public IdVO<Long> importProjectIntentHotel(MultipartFile file, Integer projectId) throws IOException {
        if (ObjectUtils.isEmpty(projectId)) {
            AppUtility.serviceError(ErrorCode.VALIDATE_REQUEST_PARAMETER_CANNOT_BE_NULL);
        }
        ProjectEntity projectEntity = projectMapper.selectById(projectId);
        if (ObjectUtils.isEmpty(projectEntity)) {
            AppUtility.serviceError(ErrorCode.PROJECT_NOT_EXIST);
        }
        ImportExcelContext<ImportProjectIntentHotelVO> context = new ImportExcelContext<>();
        context.setFile(file);
        context.setBizType(ImportBizTypeEnum.IMPORT_HOTEL_INTENT_HOTEL);
        context.setImportVOClass(ImportProjectIntentHotelVO.class);
        context.setExtra("projectId", projectId);
        context.setImportLogic(this::handleIImportProjectIntentHotel);
        return new IdVO<>(excelManager.generalImport(context));
    }

    private List<ImportRowErrorVO> handleIImportProjectIntentHotel(ImportExcelContext<ImportProjectIntentHotelVO> context, List<ImportProjectIntentHotelVO> importList) {
        List<ImportRowErrorVO> errors = new ArrayList<>();
        Integer projectId = context.getExtra("projectId", Integer.class);
        String operator = context.getUserSession().getUsername();
        List<Long> hotelIds = new ArrayList<>();
        Map<Long, ProjectIntentHotelEntity> longImportProjectIntentHotelDtoHashMap = new HashMap<>();
        Map<Long, ProjectIntentHotelEntity> insertHotelMap = new HashMap<>();
        List<ProjectIntentHotelEntity> updateHotelList = new ArrayList<>();


        List<ProjectIntentHotelEntity> projectIntentHotelEntityList = projectIntentHotelMapper.selectList(Wrappers.lambdaQuery(ProjectIntentHotelEntity.class)
                .eq(ProjectIntentHotelEntity::getProjectId, projectId)
                .in(ProjectIntentHotelEntity::getHotelId, importList.stream().map(ImportProjectIntentHotelVO::getHotelId).collect(Collectors.toList())));
        Map<Long, ProjectIntentHotelEntity> exsitHotelMap = projectIntentHotelEntityList
                .stream().collect(Collectors.toMap(h -> h.getHotelId(), Function.identity(), (k1, k2) -> k1));

        for (ImportProjectIntentHotelVO item : importList) {
            try {
                //校验房仓酒店id是否有效
                Long hotelId = item.getHotelId();
                HotelEntity hotelEntity = hotelMapper.selectOne(Wrappers.lambdaQuery(HotelEntity.class).eq(HotelEntity::getHotelId, hotelId));
                if (hotelEntity == null) {
                    errors.add(new ImportRowErrorVO(item.getRowNum(), ErrorCode.HOTEL_ID_INVALID));
                    continue;
                }
                ProjectIntentHotelEntity projectIntentHotelEntity = BeanUtil.copyProperties(item, ProjectIntentHotelEntity.class);
                projectIntentHotelEntity.setProjectId(projectId);
                projectIntentHotelEntity.setInviteStatus(1);
                projectIntentHotelEntity.setBidState(HotelBidStateEnum.NO_BID.bidState);
                projectIntentHotelEntity.setCreator(operator);
                projectIntentHotelEntity.setModifier(operator);
                if (!ValidateUtil.isValidMobileNo(projectIntentHotelEntity.getHotelSalesContactMobile())) {
                    hotelIds.add(hotelId);
                    longImportProjectIntentHotelDtoHashMap.put(hotelId, projectIntentHotelEntity);
                }

                String distributorContactEmail = item.getDistributorContactEmail();
                if (ValidateUtil.isValidMobileNo(distributorContactEmail)) {
                    //根据企业跟进人电邮 后台通过电邮查找user系统之后再设置distributorContactUid和dictrobutorContactName
                    UserEntity userEntity = userMapper.selectByEmail(distributorContactEmail);
                    if (userEntity != null && userEntity.getState() == 1) {
                        projectIntentHotelEntity.setDistributorContactUid(userEntity.getUserId());
                        String distributorContactName = !ObjectUtils.isEmpty(userEntity.getUserName()) ? userEntity.getUserName() :
                                !ObjectUtils.isEmpty(userEntity.getNickName()) ? userEntity.getNickName() : "";
                        // !ObjectUtils.isEmpty(userEntity.get) ? userAccount : "";
                        projectIntentHotelEntity.setDistributorContactName(distributorContactName);
                    }
                }
                ProjectIntentHotelEntity existHotel = exsitHotelMap.get(hotelId);
                if(ObjectUtils.isEmpty(existHotel)) {
                    projectIntentHotelEntity.setIsUpload(RfpConstant.constant_0);
                    insertHotelMap.put(hotelId, projectIntentHotelEntity);
                }else {
                    projectIntentHotelEntity.setProjectIntentHotelId(existHotel.getProjectIntentHotelId());
                    projectIntentHotelEntity.setIsUpload(RfpConstant.constant_0);
                    updateHotelList.add(projectIntentHotelEntity);
                }
            } catch (Exception e) {
                log.error("导入邀请酒店异常：", e);
                errors.add(new ImportRowErrorVO(item.getRowNum(), ErrorCode.SYSTEM_ERROR));
            }
        }

        Iterator<Long> iterator = hotelIds.iterator();
        while (iterator.hasNext()){
            ProjectIntentHotelEntity hotelEntity = exsitHotelMap.get(iterator.next());
            if(!ObjectUtils.isEmpty(hotelEntity) && !ObjectUtils.isEmpty(hotelEntity.getHotelSalesContactMobile())){
                iterator.remove();
            }
        }

        //已存在邀请酒店且已有联系人不从推荐酒店或机构中获取联系人信息
        if (CollectionUtils.isNotEmpty(hotelIds)) {
            List<RecommendHotelEntity> recommendHotels = recommendHotelMapper.selectList(Wrappers.lambdaQuery(RecommendHotelEntity.class)
                    .in(RecommendHotelEntity::getHotelId, hotelIds));
            if (CollectionUtils.isNotEmpty(recommendHotels)) {
                for (RecommendHotelEntity recommendHotel : recommendHotels) {
                    Long hotelId = recommendHotel.getHotelId();
                    ProjectIntentHotelEntity projectIntentHotelEntity = longImportProjectIntentHotelDtoHashMap.get(hotelId);
                    projectIntentHotelEntity.setHotelSalesContactName(recommendHotel.getContactName());
                    projectIntentHotelEntity.setHotelSalesContactMobile(recommendHotel.getContactMobile());
                    projectIntentHotelEntity.setHotelSalesContactEmail(recommendHotel.getContactEmail());
                    hotelIds.remove(hotelId);
                }
                if (CollectionUtils.isNotEmpty(hotelIds)) {
                    setHotelSalesContact(hotelIds, longImportProjectIntentHotelDtoHashMap);
                }
            } else {
                setHotelSalesContact(hotelIds, longImportProjectIntentHotelDtoHashMap);
            }
        }

        if(!insertHotelMap.isEmpty()){
            projectIntentHotelMapper.batchInsert(new ArrayList<>(insertHotelMap.values()));
        }
        this.batchUpdateProjectIntentHotel(updateHotelList);
        return errors;


    }

    private void batchUpdateProjectIntentHotel(List<ProjectIntentHotelEntity> list) {
        if (list == null || list.isEmpty()) {
            return;
        }
        int totalSize = list.size();
        int processed = 0;
        while (processed < totalSize) {
            int toIndex = Math.min(processed + 500, totalSize);
            List<ProjectIntentHotelEntity> sublist = list.subList(processed, toIndex);
            try (SqlSession sqlSession = sqlSessionFactory.openSession(ExecutorType.BATCH.BATCH)) {
                ProjectIntentHotelMapper mapper = sqlSession.getMapper(ProjectIntentHotelMapper.class);
                for (ProjectIntentHotelEntity entity : sublist) {
                    mapper.updateById(entity);
                }
                sqlSession.commit();
            } catch (Exception e) {
                log.error("批量导入邀请酒店异常",e);
            }
            processed += 500;
        }
    }
    /**
     * 从机构获取联系人
     *
     * @param hotelIds
     * @param longImportProjectIntentHotelDtoHashMap
     */
    private void setHotelSalesContact(List<Long> hotelIds, Map<Long, ProjectIntentHotelEntity> longImportProjectIntentHotelDtoHashMap) {
        List<OrgEntity> orgByHotelIds = orgMapper.getOrgByHotelIds(hotelIds);
        if (CollectionUtils.isNotEmpty(orgByHotelIds)) {
            for (OrgEntity orgByHotelId : orgByHotelIds) {
                // 查询酒店机构关联酒店
                Integer orgId = 0;
                List<OrgRelatedHotelEntity> relatedHotelList = orgRelatedHotelMapper.queryOrgRelatedHotelList(orgId);
                for (OrgRelatedHotelEntity orgRelatedHotelDTO : relatedHotelList) {
                    if (!hotelIds.contains(orgRelatedHotelDTO.getHotelId())) {
                        continue;
                    }
                    ProjectIntentHotelEntity projectIntentHotelEntity = longImportProjectIntentHotelDtoHashMap.get(orgRelatedHotelDTO.getHotelId());
                    projectIntentHotelEntity.setHotelSalesContactName(orgByHotelId.getContactName());
                    projectIntentHotelEntity.setHotelSalesContactMobile(orgByHotelId.getContactMobile());
                    projectIntentHotelEntity.setHotelSalesContactEmail(orgByHotelId.getContactEmail());
                }
            }
        }
    }

    /**
     * 更新推荐酒店列表
     */
    public void generateHotelRecommendLevel(@NotNull Integer projectId) {
        UserSession userSession = UserSession.get();
        // 查询项目信息
        ProjectEntity project = projectMapper.selectById(projectId);
        if (Objects.isNull(project)) {
            AppUtility.serviceError(ErrorCode.PROJECT_NOT_EXIST);
        }

        // 查询需要统计的酒店ID集合
        List<QueryHistoryProjectInfoResponse> historyProjectInfoList = projectHotelHistoryDataMapper.queryHistoryProjectHotelList(project.getProjectId(), null,null, null, null);
        List<Long> hotelIdList = historyProjectInfoList.stream().map(QueryHistoryProjectInfoResponse::getHotelId).collect(Collectors.toList());

        // 获取锁
        String lockKey = String.format(RedisConstant.GENERATE_HOTEL_RECOMMEND_LEVEL_STAT_LOCK_KEY, projectId);
        RLock lock = redissonClient.getLock(lockKey);
        try {
            if (!lock.tryLock()) {
                log.info("项目 {} 上一次统计未完成", projectId);
                AppUtility.serviceError(ErrorCode.GENERATE_PROJECT_RECOMMEND_LEVEL_NOT_FINISH);
            }

            String statReferenceNo = String.valueOf(System.currentTimeMillis());
            // 记录更新统计参考编号
            projectHotelHistoryDataMapper.updateLastStatReferenceNo(project.getProjectId(), statReferenceNo, userSession.getUsername());

// 第一步：更新基础统计数据
            //并发异步执行 生成推荐等级
            CountDownLatch countDownLatch = new CountDownLatch(4);

            // 异步更新违规统计数量
           // projectHotelHistoryDataRecommendService.updateLastYearViolationsCount(userSession, statReferenceNo, project, hotelIdList, countDownLatch);

            // 异步更新服务分
            projectHotelHistoryDataRecommendService.updateLastYearServicePoint(userSession, statReferenceNo, project, hotelIdList, countDownLatch);

            // 异步更新OTA过去12个月最低和最高价格
           // projectHotelHistoryDataRecommendService.updateLastYearOtaPrice(userSession, statReferenceNo, project, historyProjectInfoList, countDownLatch);

            // 异步生成商旅价格和签约混合价格
             projectHotelHistoryDataRecommendService.updateLowestPrice(userSession, statReferenceNo, project, countDownLatch);

            log.info("更新酒店历史统计数完成, 项目 ID : {}", project.getProjectId());

            // 异步生成POI周边优质酒店邀约推荐
            projectHotelHistoryDataRecommendService.generatePoiNearHotelRecommend(statReferenceNo, userSession, project, historyProjectInfoList, countDownLatch);

            // 异步执行更新推荐等级 依赖混合价格  优质商旅酒店推荐 节省明星酒店推荐
            projectHotelHistoryDataRecommendService.generateRecommendLevel(userSession, statReferenceNo, project.getProjectId(), countDownLatch);

            // 异步生成高频预订邀约推荐和高频预订同档推荐 依赖混合价格
            projectHotelHistoryDataRecommendService.generateFrequencyRecommend(statReferenceNo, userSession, project, historyProjectInfoList, countDownLatch);

            // 异步生成非POI热订区域邀约推荐
            projectHotelHistoryDataRecommendService.generateNoPoiHotAreaRecommend(statReferenceNo, userSession, project, historyProjectInfoList, countDownLatch);

            // 异步生成散布聚量邀约推荐
            projectHotelHistoryDataRecommendService.generateAreaGatherRecommend(statReferenceNo, userSession, project);

            // 调用接口刷新酒店AI缓存
//            AiReviewHotelThread aiReviewHotelThread = new AiReviewHotelThread(project.getProjectId(), statReferenceNo, projectRecommendStatLogDao, hotelAIReviewService);
//            hotelHexagonGenerateExecutor.execute(aiReviewHotelThread);

        } catch (Throwable e) {
            log.error("更新推荐酒店列表错误", e);
            AppUtility.serviceError(ErrorCode.GENERATE_PROJECT_RECOMMEND_LEVEL_ERROR);
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }

    }

    /**
     * 导出报价
     */
    public void exportTenderPrice(@Valid ExportTenderPriceRequest request, HttpServletResponse httpServletResponse) {
        // 查询项目信息
        ProjectEntity project = projectMapper.selectById(request.getProjectId());
        if (Objects.isNull(project)) {
            AppUtility.serviceError(ErrorCode.PROJECT_NOT_EXIST);
        }

        // 构建导出文件名称
        String fileName = project.getProjectName() + "_" + DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN);

        // 写入数据
        ExportExcelContext<ExportTenderPriceVO> exportContext = new ExportExcelContext<>();
        exportContext.setHttpServletResponse(httpServletResponse);
        exportContext.setLanguage(AppUtility.getRequestHeaderLanguage(httpServletRequest));
        exportContext.setRequestNo(request.getRequestSequenceNo());
        exportContext.setExportVOClass(ExportTenderPriceVO.class);
        exportContext.setFileName(fileName);
        exportContext.setDynamicHeaderMap(projectManager.buildCustomStrategyHeader(request.getProjectId()));
        exportContext.setExportData(queryTenderHotelPrice(request.getProjectId()));
        excelManager.generalExport(exportContext);
    }

    /**
     * 查询报价
     */
    public List<ExportTenderPriceVO> queryTenderHotelPrice(Integer projectId) {
        int language = AppUtility.getRequestHeaderLanguage(httpServletRequest);
        // 除了"未报价"和"放弃报价"状态的, 全部导出
        ArrayList<Integer> validBidStates = Lists.newArrayList(
            HotelBidStateEnum.NEW_BID.bidState,
            HotelBidStateEnum.UNDER_NEGOTIATION.bidState,
            HotelBidStateEnum.BID_WINNING.bidState,
            HotelBidStateEnum.REJECTED.bidState,
            HotelBidStateEnum.UPDATED_BID.bidState,
            HotelBidStateEnum.REJECT_NEGOTIATION.bidState
        );

        // 查询报价
        List<TenderHotelPriceVO> projectIntentHotelList = projectIntentHotelMapper.queryHotelTenderInfo(projectId, validBidStates);
        List<TenderHotelPriceVO> priceList = projectManager.queryHotelGroupTenderPriceList(language, projectId, projectIntentHotelList);

        // 转换成要导出的 vo
        return priceList.stream().map(this::convertToExportTenderPriceVO).collect(Collectors.toList());
    }

    /**
     * 迁移数据
     */
    public void migrateTenderPrice(@Valid MigrateTenderPriceRequest request, HttpServletResponse httpServletResponse) {
        // 查询项目信息
        ProjectEntity project = projectMapper.selectById(request.getProjectId());
        if (Objects.isNull(project)) {
            AppUtility.serviceError(ErrorCode.PROJECT_NOT_EXIST);
        }

        // 构建导出文件名称
        String fileName = project.getProjectName() + "_" + DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN);

        // 写入数据
        ExportExcelContext<ExportTenderPriceVO> exportContext = new ExportExcelContext<>();
        exportContext.setHttpServletResponse(httpServletResponse);
        exportContext.setLanguage(AppUtility.getRequestHeaderLanguage(httpServletRequest));
        exportContext.setRequestNo(request.getRequestSequenceNo());
        exportContext.setExportVOClass(ExportTenderPriceVO.class);
        exportContext.setFileName(fileName);
        exportContext.setDynamicHeaderMap(projectManager.buildCustomStrategyHeader(request.getProjectId()));
        exportContext.setExportData(queryMigrateTenderPrice(request.getProjectId()));
        excelManager.generalExport(exportContext);
    }

    /**
     * 查询报价
     */
    public List<ExportTenderPriceVO> queryMigrateTenderPrice(Integer projectId) {
        int language = AppUtility.getRequestHeaderLanguage(httpServletRequest);

        // 只导出"已中签"数据
        Set<Integer> validBidStates = Collections.singleton(HotelBidStateEnum.BID_WINNING.bidState);

        // 查询报价
        List<TenderHotelPriceVO> projectIntentHotelList = projectIntentHotelMapper.queryHotelTenderInfo(projectId, validBidStates);
        List<TenderHotelPriceVO> priceList = projectManager.queryHotelGroupTenderPriceList(language, projectId, projectIntentHotelList);

        // 转换成要导出的 vo
        return priceList.stream().map(this::convertToExportTenderPriceVO).collect(Collectors.toList());
    }

    /**
     * 查询lanyon 列
     */
    public Map<String, List<LanyonImportColumnVO>> getAllLanyonColumns(){
        List<LanyonImportColumnVO> lanyonImportColumns = lanyonImportColumnMapper.queryByDisplayOrder();
        Map<String, List<LanyonImportColumnVO>> lanyonImportColumnMap = lanyonImportColumns.stream().collect(Collectors.groupingBy(LanyonImportColumnVO::getDataCategory));
        return lanyonImportColumnMap;
    }

    /**
     * 保存lanyon显示key
     */
    public void saveProjectLanyonColumnViewKeys(SaveProjectLanyonViewColumnInfoRequest saveProjectLanyonViewColumnInfoRequest) {
        UserSession userSession = UserSession.get();
        if(saveProjectLanyonViewColumnInfoRequest.getProjectLanyonViewKeysId() == null) {
           projectManager.insertLanyonViewKeys(
                    saveProjectLanyonViewColumnInfoRequest.getProjectId(),
                    saveProjectLanyonViewColumnInfoRequest.getBaseInfo(),
                    saveProjectLanyonViewColumnInfoRequest.getHotelVerify(),
                    saveProjectLanyonViewColumnInfoRequest.getHotelFacilities(),
                    saveProjectLanyonViewColumnInfoRequest.getBidInfo(),
                    saveProjectLanyonViewColumnInfoRequest.getMeetingRoomBidInfo(),
                    saveProjectLanyonViewColumnInfoRequest.getLongBidInfo(),
                    saveProjectLanyonViewColumnInfoRequest.getHotelService(),
                    saveProjectLanyonViewColumnInfoRequest.getUserDefined(),
                    saveProjectLanyonViewColumnInfoRequest.getMtgUserDefined(),
                    saveProjectLanyonViewColumnInfoRequest.getLarUnApplicableDayInfo(),
                    saveProjectLanyonViewColumnInfoRequest.getBaseServiceFee(),
                    userSession.getUsername()
            );
        } else {
            projectManager.updateLanyonViewKeys(
                    saveProjectLanyonViewColumnInfoRequest.getProjectLanyonViewKeysId(),
                    saveProjectLanyonViewColumnInfoRequest.getProjectId(),
                    saveProjectLanyonViewColumnInfoRequest.getBaseInfo(),
                    saveProjectLanyonViewColumnInfoRequest.getHotelVerify(),
                    saveProjectLanyonViewColumnInfoRequest.getHotelFacilities(),
                    saveProjectLanyonViewColumnInfoRequest.getBidInfo(),
                    saveProjectLanyonViewColumnInfoRequest.getMeetingRoomBidInfo(),
                    saveProjectLanyonViewColumnInfoRequest.getLongBidInfo(),
                    saveProjectLanyonViewColumnInfoRequest.getHotelService(),
                    saveProjectLanyonViewColumnInfoRequest.getUserDefined(),
                    saveProjectLanyonViewColumnInfoRequest.getMtgUserDefined(),
                    saveProjectLanyonViewColumnInfoRequest.getLarUnApplicableDayInfo(),
                    saveProjectLanyonViewColumnInfoRequest.getBaseServiceFee(),
                    userSession.getUsername()
            );
        }
    }

    /**
     * 对象转换
     */
    private ExportTenderPriceVO convertToExportTenderPriceVO(TenderHotelPriceVO tenderHotelPriceVO) {
        ExportTenderPriceVO exportTenderPriceVO = new ExportTenderPriceVO();
        BeanUtil.copyProperties(tenderHotelPriceVO, exportTenderPriceVO);
        return exportTenderPriceVO;
    }
    /**
     * 获取项目显示key
     */
    public ProjectLanyonViewKeysEntity getProjectLanyonViewKeys(Integer projectId){
        return projectLanyonViewKeysMapper.queryByProjectId(projectId);
    }

    /**
     * 根据名称查询项目接口
     */
    public List<ProjectNameVO> queryProjectByName(QueryProjectByNameRequest request) {
        // 查询
        LambdaQueryWrapper<ProjectEntity> queryWrapper = Wrappers.lambdaQuery(ProjectEntity.class);
        queryWrapper.select(ProjectEntity::getProjectId, ProjectEntity::getProjectName);
        queryWrapper.like(ProjectEntity::getProjectName, request.getProjectName());
        queryWrapper.last("limit " + request.getLimitCount());
        List<ProjectEntity> projects = projectMapper.selectList(queryWrapper);

        // 转换
        return projects.stream().map(project -> {
            ProjectNameVO projectNameVO = new ProjectNameVO();
            projectNameVO.setProjectId(project.getProjectId());
            projectNameVO.setProjectName(project.getProjectName());
            return projectNameVO;
        }).collect(Collectors.toList());
    }
}