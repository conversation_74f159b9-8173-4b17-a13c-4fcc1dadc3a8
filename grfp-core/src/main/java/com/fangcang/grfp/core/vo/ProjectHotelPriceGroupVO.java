package com.fangcang.grfp.core.vo;

import com.fangcang.grfp.core.base.BaseVO;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class ProjectHotelPriceGroupVO extends BaseVO {

    /**
     * 项目酒店价格组ID
     */
    private Integer hotelPriceGroupId;

    /**
     * 项目酒店意向ID
     */
    private Integer projectIntentHotelId;

    /**
     * 项目ID
     */
    private Integer projectId;

    /**
     * 酒店ID
     */
    private Long hotelId;

    /**
     * 项目酒店价格档次ID
     */
    private Integer hotelPriceLevelId;

    /**
     * 适用星期，按国际规范，星期天是第1天
     */
    private String applicableWeeks;

    /**
     * lra承诺：1-是，0-否
     */
    private Integer lra;

    /**
     * 备注，最多500汉字
     */
    private String remark;

    /**
     * 是否被锁定
     */
    private Integer isLocked;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String modifier;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 房档等级号码
     */
    private Integer roomLevelNo;

    /**
     * 总房数量
     */
    private Integer totalRoomCount;

    /**
     * 双床房数量
     */
    private Integer doubleBedRoomCount;

    /**
     * 大床房数量
     */
    private Integer bigBedRoomCount;

    /**
     * Lanyon 房型描述
     * todo
     */
    private String lanyonRoomDesc;

    /**
     * 退款政策：0-免费退改，2-预订后规定时间前可取消或收取全额费用  3 收取首晚房费的退订费
     */
    private Integer cancelRestrictType;

    /**
     * 提前取消日期
     */
    private Integer cancelRestrictDay;

    /**
     * 提前取消时间
     */
    private String cancelRestrictTime;

}
