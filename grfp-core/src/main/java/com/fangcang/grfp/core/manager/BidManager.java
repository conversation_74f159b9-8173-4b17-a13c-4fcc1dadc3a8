package com.fangcang.grfp.core.manager;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.lang.mutable.MutableInt;
import cn.hutool.core.text.StrSplitter;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ReflectUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.fangcang.grfp.core.base.ImportRowErrorVO;
import com.fangcang.grfp.core.cached.CachedCurrencyService;
import com.fangcang.grfp.core.constant.ErrorCode;
import com.fangcang.grfp.core.entity.*;
import com.fangcang.grfp.core.enums.*;
import com.fangcang.grfp.core.mapper.*;
import com.fangcang.grfp.core.usersession.UserSession;
import com.fangcang.grfp.core.util.BidUtil;
import com.fangcang.grfp.core.util.JsonUtil;
import com.fangcang.grfp.core.vo.CustomStrategyBidOptionVO;
import com.fangcang.grfp.core.vo.ImportStandardBidVO;
import com.fangcang.grfp.core.vo.request.bid.*;
import com.fangcang.grfp.core.vo.response.hotel.RoomNameInfoVO;
import com.fangcang.grfp.core.vo.response.project.CustomStrategyOptionVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.Range;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 报价公共方法下沉
 */
@Component
@Slf4j
public class BidManager {

    @Resource
    private ProjectIntentHotelMapper projectIntentHotelMapper;

    @Resource
    private ProjectHotelPriceGroupMapper projectHotelPriceGroupMapper;

    @Resource
    private ProjectHotelPriceMapper projectHotelPriceMapper;

    @Resource
    private PriceApplicableRoomMapper priceApplicableRoomMapper;

    @Resource
    private PriceApplicableDayMapper priceApplicableDayMapper;

    @Resource
    private PriceUnapplicableDayMapper priceUnapplicableDayMapper;

    @Resource
    private ProjectHotelBidStrategyMapper projectHotelBidStrategyMapper;

    @Resource
    private ProjectCustomBidStrategyMapper projectCustomBidStrategyMapper;

    @Resource
    private ProjectHotelPriceLevelMapper projectHotelPriceLevelMapper;

    @Resource
    private BidOperateLogMapper bidOperateLogMapper;

    @Resource
    private ProjectHotelTaxSettingsMapper projectHotelTaxSettingsMapper;

    @Resource
    private ProjectCustomTendStrategyMapper projectCustomTendStrategyMapper;

    @Resource
    private ProjectCustomStrategyOptionMapper projectCustomStrategyOptionMapper;

    @Resource
    private CustomBidStrategyOptionMapper customBidStrategyOptionMapper;

    @Resource
    private OrgRelatedHotelMapper orgRelatedHotelMapper;

    @Resource
    private HotelManager hotelManager;

    @Resource
    private TransactionTemplate transactionTemplate;
    @Autowired
    private LanyonImportDataMapper lanyonImportDataMapper;

    /**
     * 批量创建报价
     */
    public int batchCreateBid(List<CreateBidRequest> createBidRequestList, Map<Long, List<Map<String, String>>> importHotelDataMap, UserSession userSession) {
        AtomicInteger createSucceededCount = new AtomicInteger();
        // 同步酒店信息
        List<Long> hotelIdList = createBidRequestList.stream().map(CreateBidRequest::getHotelId).collect(Collectors.toList());
        hotelManager.queryHotelBasicInfo(hotelIdList, true);

        createBidRequestList.forEach(createBidRequest -> {
            Long hotelId = createBidRequest.getHotelId();
            ProjectIntentHotelEntity existBid = projectIntentHotelMapper.selectByProjectIdAndHotelId(createBidRequest.getProjectId(), hotelId);
            Integer oldBidState = Objects.isNull(existBid) ? HotelBidStateEnum.NO_BID.bidState : existBid.getBidState();

            // 非未报价或者议价中的, 不允许修改报价
            if (!Objects.equals(oldBidState, HotelBidStateEnum.NO_BID.bidState) && !Objects.equals(oldBidState, HotelBidStateEnum.UNDER_NEGOTIATION.bidState)) {
                log.error("当前报价状态不允许修改报价, hotelId: {}, bidState: {}", hotelId, oldBidState);
                return;
            }

            // 单独事务
            transactionTemplate.executeWithoutResult(status -> {
                log.info("开始创建报价, projectId: {}, hotelId: {}", createBidRequest.getProjectId(), hotelId);
                // 报价所属酒店若有其他机构邀约记录，但是未报价（项目邀约了酒店，酒店没有处理）。则删除原邀约记录
                if (Objects.nonNull(existBid) && Objects.equals(oldBidState, HotelBidStateEnum.NO_BID.bidState)) {
                    clearProjectHotelBidData(existBid);
                }

                // 新增或更新项目酒店报价信息
                ProjectIntentHotelEntity projectIntentHotel = handleProjectIntentHotel(oldBidState, existBid, userSession, createBidRequest);
                // 设置 projectIntentHotelId
                createBidRequest.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());

                // 新增或更新项目酒店报价策略
                handleBidStrategy(userSession, createBidRequest);

                // 处理自定义报价策略
                handleCustomBidStrategy(userSession, createBidRequest);

                // 处理税费设置
                handleTaxSettings(userSession, createBidRequest);

                // 处理可用日期
                handlePriceApplicableDay(userSession, createBidRequest);

                // 处理不可用日期
                handlePriceUnapplicableDay(userSession, createBidRequest);

                // 处理房档价格
                handleRoomTypePrice(userSession, createBidRequest);

                // 更新报价状态
                HotelBidStateEnum newBidState = HotelBidStateEnum.NO_BID.bidState.equals(oldBidState) ? HotelBidStateEnum.NEW_BID : HotelBidStateEnum.UPDATED_BID;
                updateNewBidState(userSession, projectIntentHotel.getProjectIntentHotelId(), newBidState);

                // 记录更新日志
                insertOperateLog(userSession, projectIntentHotel, newBidState);

                createSucceededCount.getAndIncrement();

                // 记录lanyon导入报价json数据
                if (Objects.equals(createBidRequest.getBidUploadSource(), BidUploadSourceEnum.LANYON.key) &&
                        importHotelDataMap != null && !importHotelDataMap.isEmpty()) {
                    LanyonImportDataEntity lanyonImportData = new LanyonImportDataEntity();
                    lanyonImportData.setProjectId(projectIntentHotel.getProjectId());
                    lanyonImportData.setHotelId(hotelId);
                    lanyonImportData.setCreator(userSession.getUsername());
                    int j = 1;
                    for (Map<String, String> dataMap : importHotelDataMap.get(hotelId)) {
                        lanyonImportData.setDataType(j);
                        lanyonImportData.setJsonData(JsonUtil.simpleMapToJsonStr(dataMap));
                        this.insertOrUpdateLanyonImportData(lanyonImportData);
                        j++;
                    }
                }
            });
        });
        return createSucceededCount.get();
    }

    /**
     * 处理税费设置
     */
    private void handleTaxSettings(UserSession userSession, CreateBidRequest createBidRequest) {
        // 删除旧的
        projectHotelTaxSettingsMapper.deleteByProjectIdAndHotelId(createBidRequest.getProjectId(), createBidRequest.getHotelId());

        // 添加新的
        CreateTaxSettingRequest taxSetting = createBidRequest.getTaxSetting();
        if (Objects.nonNull(taxSetting)) {
            ProjectHotelTaxSettingsEntity projectHotelTaxSettings = new ProjectHotelTaxSettingsEntity();
            BeanUtils.copyProperties(taxSetting, projectHotelTaxSettings);
            projectHotelTaxSettings.setProjectIntentHotelId(createBidRequest.getProjectIntentHotelId());
            projectHotelTaxSettings.setProjectId(createBidRequest.getProjectId());
            projectHotelTaxSettings.setHotelId(createBidRequest.getHotelId());
            projectHotelTaxSettings.setCreator(userSession.getUsername());
            projectHotelTaxSettingsMapper.insert(projectHotelTaxSettings);
        }
    }

    /**
     * 更新报价状态
     */
    private void updateNewBidState(UserSession userSession, Integer projectIntentHotelId, HotelBidStateEnum newBidState) {
        ProjectIntentHotelEntity updateProjectIntentHotel = new ProjectIntentHotelEntity();
        updateProjectIntentHotel.setProjectIntentHotelId(projectIntentHotelId);
        updateProjectIntentHotel.setBidState(newBidState.bidState);
        updateProjectIntentHotel.setModifier(userSession.getUsername());
        projectIntentHotelMapper.updateById(updateProjectIntentHotel);
    }

    /**
     * 插入操作日志
     */
    private void insertOperateLog(UserSession userSession, ProjectIntentHotelEntity projectIntentHotel, HotelBidStateEnum newBidState) {
        BidOperateLogEntity bidOperateLog = new BidOperateLogEntity();
        bidOperateLog.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
        bidOperateLog.setProjectId(projectIntentHotel.getProjectId());
        bidOperateLog.setHotelId(projectIntentHotel.getHotelId());
        bidOperateLog.setOrgTypeId(userSession.getUserOrg().getOrgType());
        bidOperateLog.setOperateContent("BID_STATUS_UPDATE_TO_" + newBidState.name());
        bidOperateLog.setCreator(userSession.getUsername());
        bidOperateLogMapper.insert(bidOperateLog);
    }

    /**
     * 处理房档
     */
    private void handleRoomTypePrice(UserSession userSession, CreateBidRequest createBidRequest) {
        // 查询旧的房档等级, 并生成房档 levelId Map
        List<ProjectHotelPriceLevelEntity> oldLevels = projectHotelPriceLevelMapper.queryByProjectIdAndHotelId(createBidRequest.getProjectId(), createBidRequest.getHotelId());
        Map<Integer, ProjectHotelPriceLevelEntity> oldLevelIdMap = oldLevels.stream().collect(Collectors.toMap(ProjectHotelPriceLevelEntity::getHotelPriceLevelId, Function.identity()));

        // 处里新房档等级数据, 并返回 levelNo Map
        Map<Integer, ProjectHotelPriceLevelEntity> newRoomLevelNoMap = handleNewHotelLevel(userSession, createBidRequest);

        // 处理可用房型
        handleApplicableRoom(userSession, createBidRequest, newRoomLevelNoMap);

        // 生成旧价格 Map, 用于后面设置最近议价
        Map<String, ProjectHotelPriceEntity> oldPriceKeyMap = generateOldPriceKeyMap(createBidRequest, oldLevelIdMap);

        // 处理价格组
        handPriceGroup(userSession, createBidRequest, newRoomLevelNoMap, oldPriceKeyMap);
    }

    /**
     * 处理价格组
     */
    private void handPriceGroup(UserSession userSession, CreateBidRequest createBidRequest, Map<Integer, ProjectHotelPriceLevelEntity> newRoomLevelNoMap, Map<String, ProjectHotelPriceEntity> oldPriceKeyMap) {
        // 删除旧价格组
        projectHotelPriceGroupMapper.deleteByProjectAndHotelId(createBidRequest.getProjectId(), createBidRequest.getHotelId());

        // 删除旧的价格明细
        projectHotelPriceMapper.deleteByProjectAndHotelId(createBidRequest.getProjectId(), createBidRequest.getHotelId());

        // 添加新的价格组
        createBidRequest.getPriceGroupMap().forEach((roomLevelNo, priceGroups) -> {
            priceGroups.forEach(priceGroupRequest -> {

                // 创建价格组
                ProjectHotelPriceGroupEntity priceGroup = new ProjectHotelPriceGroupEntity();
                priceGroup.setProjectIntentHotelId(createBidRequest.getProjectIntentHotelId());
                priceGroup.setProjectId(createBidRequest.getProjectId());
                priceGroup.setHotelId(createBidRequest.getHotelId());
                priceGroup.setHotelPriceLevelId(newRoomLevelNoMap.get(roomLevelNo).getHotelPriceLevelId());
                priceGroup.setApplicableWeeks("2,3,4,5,6,7,1");
                priceGroup.setLra(priceGroupRequest.getLra());
                priceGroup.setIsIncludeBreakfast(priceGroupRequest.getIsIncludeBreakfast());
                priceGroup.setRemark(priceGroupRequest.getRemark());
                priceGroup.setCreator(userSession.getUsername());
                priceGroup.setModifier(userSession.getUsername());
                projectHotelPriceGroupMapper.insert(priceGroup);

                // 设置价格列表
                List<ProjectHotelPriceEntity> newPrices = new ArrayList<>();
                priceGroupRequest.getPriceList().forEach(hotelPriceRequest -> {
                    ProjectHotelPriceEntity hotelPrice = new ProjectHotelPriceEntity();
                    hotelPrice.setProjectIntentHotelId(createBidRequest.getProjectIntentHotelId());
                    hotelPrice.setProjectId(createBidRequest.getProjectId());
                    hotelPrice.setHotelId(createBidRequest.getHotelId());
                    hotelPrice.setHotelPriceGroupId(priceGroup.getHotelPriceGroupId());
                    hotelPrice.setHotelPriceLevelId(newRoomLevelNoMap.get(roomLevelNo).getHotelPriceLevelId());
                    hotelPrice.setPriceType(hotelPriceRequest.getPriceType());
                    hotelPrice.setOnePersonPrice(NumberUtil.round(hotelPriceRequest.getOnePersonPrice(), 2));
                    hotelPrice.setTwoPersonPrice(NumberUtil.round(hotelPriceRequest.getTwoPersonPrice(), 2));
                    String priceKey = priceKey(roomLevelNo, priceGroupRequest.getLra(), hotelPrice.getPriceType(), priceGroupRequest.getIsIncludeBreakfast());
                    hotelPrice.setLastOnePersonPrice(Optional.ofNullable(oldPriceKeyMap.get(priceKey)).map(ProjectHotelPriceEntity::getOnePersonPrice).orElse(null));
                    hotelPrice.setLastTwoPersonPrice(Optional.ofNullable(oldPriceKeyMap.get(priceKey)).map(ProjectHotelPriceEntity::getTwoPersonPrice).orElse(null));
                    hotelPrice.setCreator(userSession.getUsername());
                    hotelPrice.setModifier(userSession.getUsername());
                    newPrices.add(hotelPrice);
                });
                if (CollUtil.isNotEmpty(newPrices)) {
                    projectHotelPriceMapper.batchInsert(newPrices);
                }
            });
        });
    }

    /**
     * 生成旧价格 map
     */
    private Map<String, ProjectHotelPriceEntity> generateOldPriceKeyMap(CreateBidRequest createBidRequest, Map<Integer, ProjectHotelPriceLevelEntity> oldLevelIdMap) {
        // 查询旧价格组
        List<ProjectHotelPriceGroupEntity> oldGroupPrices = projectHotelPriceGroupMapper.selectByProjectIdAndHotelId(createBidRequest.getProjectId(), createBidRequest.getHotelId());
        Map<Integer, ProjectHotelPriceGroupEntity> groupIdMap = oldGroupPrices.stream().collect(Collectors.toMap(ProjectHotelPriceGroupEntity::getHotelPriceGroupId, Function.identity()));

        // 查询旧价格列表
        List<ProjectHotelPriceEntity> oldHotelPrices = projectHotelPriceMapper.selectByProjectIdAndHotelId(createBidRequest.getProjectId(), createBidRequest.getHotelId());

        // 生成旧价格 Map
        Map<String, ProjectHotelPriceEntity> oldPriceKeyMap = new HashMap<>();
        oldHotelPrices.forEach(oldHotelPrice -> {
            // 获取原价格组
            ProjectHotelPriceGroupEntity priceGroup = groupIdMap.get(oldHotelPrice.getHotelPriceGroupId());
            if (Objects.isNull(priceGroup)) {
                return;
            }
            // 获取原价格组的房档
            ProjectHotelPriceLevelEntity oldPriceLevel = oldLevelIdMap.get(oldHotelPrice.getHotelPriceLevelId());
            String priceKey = priceKey(oldPriceLevel.getRoomLevelNo(), priceGroup.getLra(), oldHotelPrice.getPriceType(), priceGroup.getIsIncludeBreakfast());
            oldPriceKeyMap.put(priceKey, oldHotelPrice);
        });
        return oldPriceKeyMap;
    }

    /**
     * 处理可用房型
     */
    private void handleApplicableRoom(UserSession userSession, CreateBidRequest createBidRequest, Map<Integer, ProjectHotelPriceLevelEntity> newRoomLevelNoMap) {
        // 删除旧的可用房型
        priceApplicableRoomMapper.deleteByProjectIdAndHotelId(createBidRequest.getProjectId(), createBidRequest.getHotelId());

        // Lanyon导入没有房型
        if(createBidRequest.getRoomTypeMap() == null || createBidRequest.getRoomTypeMap().isEmpty()){
            return;
        }

        // 添加新的
        List<PriceApplicableRoomEntity> priceApplicableRoomEntities = new ArrayList<>();
        createBidRequest.getRoomTypeMap().forEach((roomLevel, roomList) -> {
            roomList.forEach(room -> {
                PriceApplicableRoomEntity priceApplicableRoomEntity = new PriceApplicableRoomEntity();
                priceApplicableRoomEntity.setProjectIntentHotelId(createBidRequest.getProjectIntentHotelId());
                priceApplicableRoomEntity.setProjectId(createBidRequest.getProjectId());
                priceApplicableRoomEntity.setHotelId(createBidRequest.getHotelId());
                priceApplicableRoomEntity.setHotelPriceLevelId(newRoomLevelNoMap.get(roomLevel).getHotelPriceLevelId());
                priceApplicableRoomEntity.setRoomTypeId(room.getRoomTypeId());
                priceApplicableRoomEntity.setDisplayOrder(room.getDisplayOrder());
                priceApplicableRoomEntity.setCreator(userSession.getUsername());
                priceApplicableRoomEntity.setModifier(userSession.getUsername());
                priceApplicableRoomEntities.add(priceApplicableRoomEntity);
            });
        });
        // 批量插入
        priceApplicableRoomMapper.batchInsert(priceApplicableRoomEntities);
    }

    /**
     * 处理房档等级
     */
    private Map<Integer, ProjectHotelPriceLevelEntity> handleNewHotelLevel(UserSession userSession, CreateBidRequest createBidRequest) {
        // 删除旧的房档
        projectHotelPriceLevelMapper.deleteByProjectIdAndHotelId(createBidRequest.getProjectId(), createBidRequest.getHotelId());

        // 新增新的房档
        List<ProjectHotelPriceLevelEntity> insertList = buildPriceLevelList(userSession, createBidRequest);
        projectHotelPriceLevelMapper.insertBatch(insertList);

        // 生成 roomLevelHotelPriceLevelIdMap
        return insertList.stream().collect(Collectors.toMap(ProjectHotelPriceLevelEntity::getRoomLevelNo, Function.identity()));
    }

    /**
     * 生成价格 key
     */
    private String priceKey(int levelNo, int lra, int priceType, int isIncludeBreakfast) {
        return String.format("%s:%s:%s:%s", levelNo, lra, priceType, isIncludeBreakfast);
    }

    /**
     * 构建房档等级列表
     */
    private static List<ProjectHotelPriceLevelEntity> buildPriceLevelList(UserSession userSession, CreateBidRequest createBidRequest) {
        Map<Integer, CreatePriceLevelRequest> roomCountMap = createBidRequest.getRoomCountMap();
        List<ProjectHotelPriceLevelEntity> insertList = new ArrayList<>();
        roomCountMap.forEach((roomLevelNo, priceLevelRequest) -> {
            ProjectHotelPriceLevelEntity priceLevelEntity = new ProjectHotelPriceLevelEntity();
            priceLevelEntity.setProjectIntentHotelId(createBidRequest.getProjectIntentHotelId());
            priceLevelEntity.setProjectId(createBidRequest.getProjectId());
            priceLevelEntity.setHotelId(createBidRequest.getHotelId());
            priceLevelEntity.setRoomLevelNo(roomLevelNo);
            priceLevelEntity.setBigBedRoomCount(priceLevelRequest.getBigBedRoomCount());
            priceLevelEntity.setDoubleBedRoomCount(priceLevelRequest.getDoubleBedRoomCount());
            priceLevelEntity.setTotalRoomCount(priceLevelRequest.getTotalRoomCount());
            priceLevelEntity.setLanyonRoomDesc(priceLevelRequest.getLanyonRoomDesc());
            priceLevelEntity.setCreator(userSession.getUsername());
            priceLevelEntity.setModifier(userSession.getUsername());
            insertList.add(priceLevelEntity);
        });
        return insertList;
    }

    /**
     * 处理不可用日期
     */
    private void handlePriceUnapplicableDay(UserSession userSession, CreateBidRequest createBidRequest) {
        // 删除旧的
        priceUnapplicableDayMapper.deleteByProjectIdAndHotelId(createBidRequest.getProjectId(), createBidRequest.getHotelId());

        // 添加新的
        if (CollUtil.isNotEmpty(createBidRequest.getUnapplicableDates())) {
            List<PriceUnapplicableDayEntity> entities = new ArrayList<>();
            createBidRequest.getUnapplicableDates().forEach(item -> {
                PriceUnapplicableDayEntity unapplicableDayEntity = new PriceUnapplicableDayEntity();
                unapplicableDayEntity.setProjectIntentHotelId(createBidRequest.getProjectIntentHotelId());
                unapplicableDayEntity.setProjectId(createBidRequest.getProjectId());
                unapplicableDayEntity.setHotelId(createBidRequest.getHotelId());
                unapplicableDayEntity.setStartDate(item.getMinimum());
                unapplicableDayEntity.setEndDate(item.getMaximum());
                unapplicableDayEntity.setCreator(userSession.getUsername());
                entities.add(unapplicableDayEntity);
            });
            priceUnapplicableDayMapper.batchInsert(entities);
        }
    }

    /**
     * 处理可用日期
     */
    private void handlePriceApplicableDay(UserSession userSession, CreateBidRequest createBidRequest) {
        // 删除旧的
        priceApplicableDayMapper.deleteByProjectAndHotelId(createBidRequest.getProjectId(), createBidRequest.getHotelId());

        // 添加新的
        List<PriceApplicableDayEntity> entities = new ArrayList<>();
        // 设置基础协议日期
        PriceApplicableDayEntity priceApplicableDayEntity = new PriceApplicableDayEntity();
        priceApplicableDayEntity.setProjectIntentHotelId(createBidRequest.getProjectIntentHotelId());
        priceApplicableDayEntity.setProjectId(createBidRequest.getProjectId());
        priceApplicableDayEntity.setHotelId(createBidRequest.getHotelId());
        priceApplicableDayEntity.setPriceType(HotelPriceTypeEnum.BASE_PRICE.key);
        priceApplicableDayEntity.setStartDate(createBidRequest.getBidStartTime());
        priceApplicableDayEntity.setEndDate(createBidRequest.getBidEndTime());
        priceApplicableDayEntity.setCreator(userSession.getUsername());
        priceApplicableDayEntity.setModifier(userSession.getUsername());
        entities.add(priceApplicableDayEntity);

        // 设置 season 1
        if (CollUtil.isNotEmpty(createBidRequest.getSeason1Dates())) {
            createBidRequest.getSeason1Dates().forEach(item -> {
                PriceApplicableDayEntity applicableDayEntity = new PriceApplicableDayEntity();
                applicableDayEntity.setProjectIntentHotelId(createBidRequest.getProjectIntentHotelId());
                applicableDayEntity.setProjectId(createBidRequest.getProjectId());
                applicableDayEntity.setHotelId(createBidRequest.getHotelId());
                applicableDayEntity.setPriceType(HotelPriceTypeEnum.SEASON_1_PRICE.key);
                applicableDayEntity.setStartDate(item.getMinimum());
                applicableDayEntity.setEndDate(item.getMaximum());
                applicableDayEntity.setCreator(userSession.getUsername());
                applicableDayEntity.setModifier(userSession.getUsername());
                entities.add(applicableDayEntity);
            });
        }
        // 设置 season 2
        if (CollUtil.isNotEmpty(createBidRequest.getSeason2Dates())) {
            createBidRequest.getSeason2Dates().forEach(item -> {
                PriceApplicableDayEntity applicableDayEntity = new PriceApplicableDayEntity();
                applicableDayEntity.setProjectIntentHotelId(createBidRequest.getProjectIntentHotelId());
                applicableDayEntity.setProjectId(createBidRequest.getProjectId());
                applicableDayEntity.setHotelId(createBidRequest.getHotelId());
                applicableDayEntity.setPriceType(HotelPriceTypeEnum.SEASON_2_PRICE.key);
                applicableDayEntity.setStartDate(item.getMinimum());
                applicableDayEntity.setEndDate(item.getMaximum());
                applicableDayEntity.setCreator(userSession.getUsername());
                entities.add(applicableDayEntity);
            });
        }
        priceApplicableDayMapper.insertBatch(entities);
    }

    /**
     * 处理自定义报价策略
     */
    private void handleCustomBidStrategy(UserSession userSession, CreateBidRequest createBidRequest) {
        List<CreateCustomBidStrategyRequest> projectCustomBidStrategyList = createBidRequest.getCustomBidStrategies();
        if (CollUtil.isNotEmpty(projectCustomBidStrategyList)) {
            // 先删除旧的
            projectCustomBidStrategyMapper.deleteByProjectIdAndHotelId(createBidRequest.getProjectId(), createBidRequest.getHotelId());
            customBidStrategyOptionMapper.deleteByProjectIdAndHotelId(createBidRequest.getProjectId(), createBidRequest.getHotelId());

            // 新增自定义策略
            List<ProjectCustomBidStrategyEntity> customBidStrategyList = new ArrayList<>();
            List<CustomBidStrategyOptionEntity> strategyOptionList = new ArrayList<>();
            for(CreateCustomBidStrategyRequest item : projectCustomBidStrategyList) {
                ProjectCustomBidStrategyEntity customBidStrategyEntity = new ProjectCustomBidStrategyEntity();
                customBidStrategyEntity.setProjectIntentHotelId(createBidRequest.getProjectIntentHotelId());
                customBidStrategyEntity.setProjectId(createBidRequest.getProjectId());
                customBidStrategyEntity.setHotelId(createBidRequest.getHotelId());
                customBidStrategyEntity.setStrategyType(item.getStrategyType());
                customBidStrategyEntity.setCustomTendStrategyId(item.getCustomTendStrategyId());
                customBidStrategyEntity.setSupportStrategyName(item.getSupportStrategyName());
                customBidStrategyEntity.setSupportStrategyText(item.getSupportStrategyText());
                customBidStrategyEntity.setStrategyName(item.getStrategyName());
                customBidStrategyEntity.setCreator(userSession.getUsername());
                customBidStrategyEntity.setModifier(userSession.getUsername());
                customBidStrategyList.add(customBidStrategyEntity);

                if(CollectionUtils.isNotEmpty(item.getOptions())){
                    for(CustomStrategyBidOptionVO option : item.getOptions()) {
                        CustomBidStrategyOptionEntity customBidStrategyOptionEntity = new CustomBidStrategyOptionEntity();
                        customBidStrategyOptionEntity.setOptionId(option.getOptionId());
                        customBidStrategyOptionEntity.setStrategyId(Long.valueOf(customBidStrategyEntity.getCustomTendStrategyId()));
                        customBidStrategyOptionEntity.setProjectId(customBidStrategyEntity.getProjectId());
                        customBidStrategyOptionEntity.setHotelId(customBidStrategyEntity.getHotelId());
                        customBidStrategyOptionEntity.setProjectIntentHotelId(customBidStrategyEntity.getProjectIntentHotelId());
                        customBidStrategyOptionEntity.setIsSupport(option.getIsSupport());
                        customBidStrategyOptionEntity.setOptionName(option.getOptionName());
                        customBidStrategyOptionEntity.setCreator(userSession.getUsername());
                        customBidStrategyOptionEntity.setModifier(userSession.getUsername());
                        strategyOptionList.add(customBidStrategyOptionEntity);
                    }
                }
            }
            // 新增策略
            projectCustomBidStrategyMapper.batchInsert(customBidStrategyList);

            // 新增选项
            if(CollectionUtils.isNotEmpty(strategyOptionList)){
                customBidStrategyOptionMapper.batchUpsert(strategyOptionList);
            }
        }
    }

    /**
     * 处理报价策略
     */
    private void handleBidStrategy(UserSession userSession, CreateBidRequest createBidRequest) {
        // 删除旧的
        projectHotelBidStrategyMapper.deleteByProjectAndHotelId(createBidRequest.getProjectId(), createBidRequest.getHotelId());

        CreateBidStrategyRequest bidStrategy = createBidRequest.getBidStrategy();
        // 新增新的
        ProjectHotelBidStrategyEntity projectHotelBidStrategy = new ProjectHotelBidStrategyEntity();
        projectHotelBidStrategy.setHotelId(createBidRequest.getHotelId());
        projectHotelBidStrategy.setProjectId(createBidRequest.getProjectId());
        projectHotelBidStrategy.setProjectIntentHotelId(createBidRequest.getProjectIntentHotelId());
        projectHotelBidStrategy.setSupportCancelDay(bidStrategy.getSupportCancelDay());
        projectHotelBidStrategy.setSupportCancelTime(bidStrategy.getSupportCancelTime());
        projectHotelBidStrategy.setSupportWifi(bidStrategy.getSupportWifi());
        projectHotelBidStrategy.setCreator(userSession.getUsername());
        projectHotelBidStrategyMapper.insert(projectHotelBidStrategy);
    }

    /**
     * 插入或更新报价
     * 注意: 目前入口只有导入标准报价, 只处理方法内包含的两种报价状态
     */
    private ProjectIntentHotelEntity handleProjectIntentHotel(Integer oldBidState, ProjectIntentHotelEntity existBid, UserSession userSession, CreateBidRequest createBidRequest) {
        // 新增
        if (Objects.equals(oldBidState, HotelBidStateEnum.NO_BID.bidState)) {
            ProjectIntentHotelEntity projectIntentHotel = new ProjectIntentHotelEntity();
            projectIntentHotel.setProjectId(createBidRequest.getProjectId());
            projectIntentHotel.setHotelId(createBidRequest.getHotelId());
            projectIntentHotel.setHotelSalesContactName(createBidRequest.getHotelSalesContactName());
            projectIntentHotel.setHotelSalesContactEmail(createBidRequest.getHotelSalesContactEmail());
            projectIntentHotel.setHotelSalesContactMobile(createBidRequest.getHotelSalesContactMobile());
            projectIntentHotel.setHotelBidContactName(createBidRequest.getHotelSalesContactName());
            projectIntentHotel.setHotelBidContactEmail(createBidRequest.getHotelSalesContactEmail());
            projectIntentHotel.setHotelBidContactMobile(createBidRequest.getHotelSalesContactMobile());
            projectIntentHotel.setCurrencyCode(createBidRequest.getCurrencyCode());
            projectIntentHotel.setIsUpload(createBidRequest.getIsUpload());
            projectIntentHotel.setBidUploadSource(createBidRequest.getBidUploadSource());
            projectIntentHotel.setInviteStatus(StateEnum.Invalid.key);
            projectIntentHotel.setSendMailStatus(StateEnum.Invalid.key);
            projectIntentHotel.setBidState(HotelBidStateEnum.NO_BID.bidState);
            projectIntentHotel.setBidOrgId(createBidRequest.getBidOrgId());
            projectIntentHotel.setHotelOrgId(createBidRequest.getHotelOrgId());
            projectIntentHotel.setCreator(userSession.getUsername());
            projectIntentHotel.setModifier(userSession.getUsername());
            projectIntentHotel.setBidOrgType(createBidRequest.getBidOrgType());
            projectIntentHotel.setHotelGroupBidContactName(createBidRequest.getHotelGroupBidContactName());
            projectIntentHotel.setHotelGroupBidContactEmail(createBidRequest.getHotelGroupBidContactEmail());
            projectIntentHotel.setHotelGroupBidContactMobile(createBidRequest.getHotelGroupBidContactMobile());
            projectIntentHotelMapper.insert(projectIntentHotel);
            return projectIntentHotel;
        }
        // 更新
        else if (HotelBidStateEnum.UNDER_NEGOTIATION.bidState.equals(oldBidState)) {
            existBid.setHotelSalesContactName(createBidRequest.getHotelSalesContactName());
            existBid.setHotelSalesContactEmail(createBidRequest.getHotelSalesContactEmail());
            existBid.setHotelSalesContactMobile(createBidRequest.getHotelSalesContactMobile());
            existBid.setModifier(userSession.getUsername());
            projectIntentHotelMapper.updateContactInfoOnly(existBid);

            existBid.setBidOrgType(createBidRequest.getBidOrgType());
            existBid.setHotelGroupBidContactName(createBidRequest.getHotelGroupBidContactName());
            existBid.setHotelGroupBidContactEmail(createBidRequest.getHotelGroupBidContactEmail());
            existBid.setHotelGroupBidContactMobile(createBidRequest.getHotelGroupBidContactMobile());
            existBid.setHotelBidContactName(createBidRequest.getHotelSalesContactName());
            existBid.setHotelBidContactMobile(createBidRequest.getHotelSalesContactMobile());
            existBid.setHotelBidContactEmail(createBidRequest.getHotelSalesContactEmail());
            existBid.setIsUpload(createBidRequest.getIsUpload());
            existBid.setBidUploadSource(BidUploadSourceEnum.STAND.key);
            existBid.setInviteStatus(StateEnum.Invalid.key);
            existBid.setSendMailStatus(StateEnum.Invalid.key);
            existBid.setBidState(HotelBidStateEnum.NO_BID.bidState);
            projectIntentHotelMapper.updateBidContactInfo(existBid);
            return existBid;
        }
        // 目前不可能走这个逻辑
        return null;
    }

    /**
     * 清除所有报价相关的表
     */
    @Transactional(rollbackFor = Exception.class)
    public void clearProjectHotelBidData(ProjectIntentHotelEntity projectIntentHotel) {
        projectIntentHotelMapper.deleteById(projectIntentHotel.getProjectIntentHotelId());
        projectHotelPriceMapper.deleteByProjectAndHotelId(projectIntentHotel.getProjectId(), projectIntentHotel.getHotelId());
        projectHotelPriceGroupMapper.deleteByProjectAndHotelId(projectIntentHotel.getProjectId(), projectIntentHotel.getHotelId());
        priceApplicableRoomMapper.deleteByProjectIdAndHotelId(projectIntentHotel.getProjectId(), projectIntentHotel.getHotelId());
        priceApplicableDayMapper.deleteByProjectAndHotelId(projectIntentHotel.getProjectId(), projectIntentHotel.getHotelId());
        priceUnapplicableDayMapper.deleteByProjectIdAndHotelId(projectIntentHotel.getProjectId(), projectIntentHotel.getHotelId());
        projectHotelBidStrategyMapper.deleteByProjectAndHotelId(projectIntentHotel.getProjectId(), projectIntentHotel.getHotelId());
        projectCustomBidStrategyMapper.deleteByProjectIdAndHotelId(projectIntentHotel.getProjectId(), projectIntentHotel.getHotelId());
        customBidStrategyOptionMapper.deleteByProjectIdAndHotelId(projectIntentHotel.getProjectId(), projectIntentHotel.getHotelId());
        projectHotelTaxSettingsMapper.deleteByProjectIdAndHotelId(projectIntentHotel.getProjectId(), projectIntentHotel.getHotelId());
    }

    /**
     * 校验并生成创建报价请求
     */
    public List<CreateBidRequest> validateAndGenerateCreateBidRequest(ValidateBidContext context, List<ImportStandardBidVO> importList, List<ImportRowErrorVO> errors) {
        if (CollUtil.isEmpty(importList)) {
            return Collections.emptyList();
        }

        // 查询酒店
        Set<Long> hotelIds = importList.stream().map(ImportStandardBidVO::getHotelId).collect(Collectors.toSet());
        List<HotelEntity> hotelList = hotelManager.queryHotelBasicInfo(hotelIds, true);
        Map<Long, HotelEntity> hotelMap = hotelList.stream().collect(Collectors.toMap(HotelEntity::getHotelId, Function.identity()));

        // 查询房型
        List<RoomNameInfoVO> roomNameInfoList = hotelManager.queryRoomNameListByHotelIds(context.getLanguage(), hotelIds);
        // 按酒店分组, key 为酒店 id, value 为房型 id
        Map<Long, List<RoomNameInfoVO>> hotelRoomMap = roomNameInfoList.stream().collect(Collectors.groupingBy(RoomNameInfoVO::getHotelId));

        // 查询项目自定义策略
        Set<Integer> projectIds = importList.stream().map(ImportStandardBidVO::getProjectId).collect(Collectors.toSet());
        List<ProjectCustomTendStrategyEntity> customTendStrategies = projectCustomTendStrategyMapper.selectByProjectIds(projectIds);
        Map<Integer, List<ProjectCustomTendStrategyEntity>> customStrategyMap = customTendStrategies.stream().collect(Collectors.groupingBy(ProjectCustomTendStrategyEntity::getProjectId));
        // 查询选项
        Map<Long, List<CustomStrategyOptionVO>> customStrategyOptionMap = queryCustomStrategyOptionMap(customTendStrategies);

        // 查询酒店对应的机构
        List<OrgRelatedHotelEntity> orgRelatedHotelList = orgRelatedHotelMapper.selectByHotelIds(hotelIds);
        Map<Long, Integer> hotelIdOrgIdMap = orgRelatedHotelList.stream().collect(Collectors.toMap(OrgRelatedHotelEntity::getHotelId, OrgRelatedHotelEntity::getOrgId));

        List<CreateBidRequest> createBidRequestList = new ArrayList<>();
        // 校验
        importList.forEach(item -> {

            // 是否通过校验变量
            ImportRowErrorVO rowError = new ImportRowErrorVO(item.getRowNum());

            // 创建报价请求
            CreateBidRequest createBidRequest = new CreateBidRequest();
            createBidRequest.setProjectId(item.getProjectId());
            createBidRequest.setIsUpload(context.getIsUpload());
            createBidRequest.setBidUploadSource(BidUploadSourceEnum.STAND.key);
            createBidRequest.setHotelSalesContactName(item.getHotelSalesContactName());
            createBidRequest.setHotelSalesContactEmail(item.getHotelSalesContactEmail());
            createBidRequest.setHotelSalesContactMobile(item.getHotelSalesContactMobile());
            createBidRequest.setHotelGroupBidContactName(context.getHotelGroupBidContactName());
            createBidRequest.setHotelGroupBidContactMobile(context.getHotelGroupBidContactMobile());
            createBidRequest.setHotelGroupBidContactEmail(context.getHotelGroupBidContactEmail());
            createBidRequest.setBidOrgType(context.getBidOrgType());

            // 导入的酒店名称不能为空
            if (StringUtils.isBlank(item.getPropName())) {
                rowError.addError(ErrorCode.HOTEL_NAME_CANNOT_BE_EMPTY);
            }

            // 判断房仓酒店是否存在
            if (!hotelMap.containsKey(item.getHotelId())) {
                rowError.addError(ErrorCode.HOTEL_NOT_EXIST);
            }
            createBidRequest.setHotelId(item.getHotelId());

            // 报价机构类型为酒店的, 需要校验酒店是否存在机构
            if (BidOrgTypeEnum.HOTEL.key.equals(context.getBidOrgType())) {
                if (!hotelIdOrgIdMap.containsKey(item.getHotelId())) {
                    rowError.addError(ErrorCode.HOTEL_NOT_EXIST_ORG);
                } else {
                    createBidRequest.setHotelOrgId(hotelIdOrgIdMap.get(item.getHotelId()));
                    createBidRequest.setBidOrgId(hotelIdOrgIdMap.get(item.getHotelId()));
                }
            }

            // 报价币种，不可为空
            if (StringUtils.isBlank(item.getCurrencyCode())) {
                rowError.addError(ErrorCode.CURRENCY_CODE_CANNOT_BE_EMPTY);
            }
            createBidRequest.setCurrencyCode(item.getCurrencyCode());

            // 第一档房型不可为空，多个房型用英文逗号隔开
            if (StringUtils.isBlank(item.getRoomType1Define())) {
                rowError.addError(ErrorCode.ROOM_TYPE_1_DEFINE_CANNOT_BE_EMPTY);
            }

            // 房档1单人报价，不可为空，为有效数字类型
            if (Objects.isNull(item.getRt1Sgl())) {
                rowError.addError(ErrorCode.ROOM_TYPE_1_SGL_PRICE_CANNOT_BE_EMPTY);
            }

            // 房档1双人报价，不可为空，为有效数字类型 （导入时若是文本要自动处理成数值
            if (Objects.isNull(item.getRt1Dbl())) {
                rowError.addError(ErrorCode.ROOM_TYPE_1_DBL_PRICE_CANNOT_BE_EMPTY);
            }

            // 合同开始时间不能大于结束时间
            validateBidDate(item, rowError, createBidRequest);

            // 校验报价策略
            validateBidStrategy(item, rowError, createBidRequest);

            // 校验税费
            validateTaxes(item, rowError, createBidRequest);

            // 校验 season  日期
            validateSeasonDate(item, rowError, createBidRequest);

            // 校验自定义策略
            validateCustomStrategy(item, rowError, customStrategyMap.get(item.getProjectId()), customStrategyOptionMap, createBidRequest);

            // 校验不适用日期
            validateUnApplicableDates(item, rowError, createBidRequest);

            // 校验报价状态
            validateBidStatus(rowError, item.getProjectId(), createBidRequest.getHotelId());

            // 校验房型和价格
            List<RoomNameInfoVO> hotelAllRoomList = Optional.ofNullable(hotelRoomMap.get(createBidRequest.getHotelId())).orElse(Collections.emptyList());
            validateRoomTypeAndPrice(item, rowError, createBidRequest, hotelAllRoomList);

            // 校验不通过, 处理下一条
            if (rowError.hasError()) {
                errors.add(rowError);
                return;
            }
            createBidRequestList.add(createBidRequest);
        });
        return createBidRequestList;
    }

    /**
     * 查询自定义策略选项 Map
     */
    private Map<Long, List<CustomStrategyOptionVO>> queryCustomStrategyOptionMap(List<ProjectCustomTendStrategyEntity> customTendStrategies) {
        if (CollUtil.isEmpty(customTendStrategies)) {
            return Collections.emptyMap();
        }

        // 过滤选项类型
        Set<Long> optionTypeStrategyIds = customTendStrategies.stream().filter(e -> CustomStrategyTypeEnum.isOptionType(e.getStrategyType()))
            .map(ProjectCustomTendStrategyEntity::getId).collect(Collectors.toSet());
        if (CollUtil.isEmpty(optionTypeStrategyIds)) {
            return Collections.emptyMap();
        }

        // 返回策略id:选项列表 Map
        List<CustomStrategyOptionVO> options = projectCustomStrategyOptionMapper.selectByStrategyIds(optionTypeStrategyIds);
        return options.stream().collect(Collectors.groupingBy(CustomStrategyOptionVO::getStrategyId));
    }

    /**
     * 校验并生成创建报价请求
     */
    public List<CreateBidRequest> validateAndGenerateLanyonCreateBidRequest(ValidateBidContext context, List<ImportStandardBidVO> importList, List<ImportRowErrorVO> errors) {
        if (CollUtil.isEmpty(importList)) {
            return Collections.emptyList();
        }

        // 查询酒店
        Set<Long> hotelIds = importList.stream().map(ImportStandardBidVO::getHotelId).collect(Collectors.toSet());
        List<HotelEntity> hotelList = hotelManager.queryHotelBasicInfo(hotelIds, true);
        Map<Long, HotelEntity> hotelMap = hotelList.stream().collect(Collectors.toMap(HotelEntity::getHotelId, Function.identity()));

        // 查询房型
        List<RoomNameInfoVO> roomNameInfoList = hotelManager.queryRoomNameListByHotelIds(context.getLanguage(), hotelIds);
        // 按酒店分组, key 为酒店 id, value 为房型 id
        Map<Long, List<RoomNameInfoVO>> hotelRoomMap = roomNameInfoList.stream().collect(Collectors.groupingBy(RoomNameInfoVO::getHotelId));

        // 查询项目自定义策略
        Set<Integer> projectIds = importList.stream().map(ImportStandardBidVO::getProjectId).collect(Collectors.toSet());
        List<ProjectCustomTendStrategyEntity> customTendStrategies = projectCustomTendStrategyMapper.selectByProjectIds(projectIds);
        Map<Integer, List<ProjectCustomTendStrategyEntity>> customStrategyMap = customTendStrategies.stream().collect(Collectors.groupingBy(ProjectCustomTendStrategyEntity::getProjectId));

        // 查询自定义策略选项 Map
        Map<Long, List<CustomStrategyOptionVO>> customStrategyOptionMap = queryCustomStrategyOptionMap(customTendStrategies);

        List<CreateBidRequest> createBidRequestList = new ArrayList<>();
        // 校验
        importList.forEach(item -> {

            // 是否通过校验变量
            ImportRowErrorVO rowError = new ImportRowErrorVO(item.getRowNum());

            // 创建报价请求
            CreateBidRequest createBidRequest = new CreateBidRequest();
            createBidRequest.setProjectId(item.getProjectId());
            createBidRequest.setIsUpload(context.getIsUpload());
            createBidRequest.setHotelSalesContactName(item.getHotelSalesContactName());
            createBidRequest.setHotelSalesContactEmail(item.getHotelSalesContactEmail());
            createBidRequest.setHotelSalesContactMobile(item.getHotelSalesContactMobile());

            // 导入的酒店名称不能为空
            if (StringUtils.isBlank(item.getPropName())) {
                rowError.addError(ErrorCode.HOTEL_NAME_CANNOT_BE_EMPTY);
            }

            // 判断房仓酒店是否存在
            if (!hotelMap.containsKey(item.getHotelId())) {
                rowError.addError(ErrorCode.HOTEL_NOT_EXIST);
            }
            createBidRequest.setHotelId(item.getHotelId());

            // 报价币种，不可为空
            if (StringUtils.isBlank(item.getCurrencyCode())) {
                rowError.addError(ErrorCode.CURRENCY_CODE_CANNOT_BE_EMPTY);
            }
            createBidRequest.setCurrencyCode(item.getCurrencyCode());

            // 第一档房型不可为空，多个房型用英文逗号隔开
            if (StringUtils.isBlank(item.getRoomType1Define())) {
                rowError.addError(ErrorCode.ROOM_TYPE_1_DEFINE_CANNOT_BE_EMPTY);
            }

            // 房档1单人报价，不可为空，为有效数字类型
            if (Objects.isNull(item.getRt1Sgl())) {
                rowError.addError(ErrorCode.ROOM_TYPE_1_SGL_PRICE_CANNOT_BE_EMPTY);
            }

            // 房档1双人报价，不可为空，为有效数字类型 （导入时若是文本要自动处理成数值
            if (Objects.isNull(item.getRt1Dbl())) {
                rowError.addError(ErrorCode.ROOM_TYPE_1_DBL_PRICE_CANNOT_BE_EMPTY);
            }

            // 合同开始时间不能大于结束时间
            validateBidDate(item, rowError, createBidRequest);

            // 校验报价策略
            validateBidStrategy(item, rowError, createBidRequest);

            // 校验税费
            validateTaxes(item, rowError, createBidRequest);

            // 校验 season  日期
            validateSeasonDate(item, rowError, createBidRequest);

            // 校验自定义策略
            validateCustomStrategy(item, rowError, customStrategyMap.get(item.getProjectId()), customStrategyOptionMap, createBidRequest);

            // 校验不适用日期
            validateUnApplicableDates(item, rowError, createBidRequest);

            // 校验报价状态
            validateBidStatus(rowError, item.getProjectId(), createBidRequest.getHotelId());

            // 校验房型和价格
            validateRoomTypeAndPrice(item, rowError, createBidRequest, hotelRoomMap.get(createBidRequest.getHotelId()));

            // 校验不通过, 处理下一条
            if (rowError.hasError()) {
                errors.add(rowError);
                return;
            }
            createBidRequestList.add(createBidRequest);
        });
        return createBidRequestList;
    }


    /**
     * 校验报价策略
     */
    private void validateBidStrategy(ImportStandardBidVO item, ImportRowErrorVO rowError, CreateBidRequest createBidRequest) {
        // 取消条款，必录项，值可以是 数字+PM 比如6PM 表示入住当天18点前免费取消，或者18:00 表示入住当天18点前免费取消，或者数值+H，表示当天18点+N小时前免费取消，比如48H 表示入住前2天的18点前免费取消
        String cancelPolicy = item.getCancelPolicy();
        if (StringUtils.isBlank(cancelPolicy)) {
            rowError.addError(ErrorCode.CANCEL_POLICY_CANNOT_BE_EMPTY);
        }

        // 设置报价策略请求
        CreateBidStrategyRequest bidStrategyRequest = new CreateBidStrategyRequest();

        if (StringUtils.isNotBlank(cancelPolicy)) {
            // 解析取消条款
            int supportCancelDay = 0;
            String supportCancelTime = null;
            cancelPolicy = cancelPolicy.toUpperCase();

            // 处理 AM/PM 格式
            if (cancelPolicy.contains("AM") || cancelPolicy.contains("PM")) {
                String timePart = cancelPolicy.split("[AP]M")[0];
                // 处理带冒号和不带冒号的时间
                if (timePart.contains(":")) {
                    String[] parts = timePart.split(":");
                    int hour = Integer.parseInt(parts[0]);
                    int minute = Integer.parseInt(parts[1]);
                    if (cancelPolicy.contains("PM") && hour != 12) {
                        hour += 12;
                    }
                    if (cancelPolicy.contains("AM") && hour == 12) {
                        hour = 0;
                    }
                    supportCancelTime = String.format("%02d:%02d", hour, minute);
                } else {
                    int hour = Integer.parseInt(timePart);
                    if (cancelPolicy.contains("PM") && hour != 12) {
                        hour += 12;
                    }
                    if (cancelPolicy.contains("AM") && hour == 12) {
                        hour = 0;
                    }
                    supportCancelTime = String.format("%02d:00", hour);
                }
            } else if (cancelPolicy.endsWith("H")) {
                // 处理 H 格式
                String hoursStr = cancelPolicy.replaceAll("[^0-9]", "");
                int hours = Integer.parseInt(hoursStr);

                supportCancelDay = hours / 24;  // 计算天数

                // 计算时间点 (18:00 减去余数小时)
                int remainder = hours % 24;
                int cancelHour = 18 - remainder;
                if (cancelHour < 0) {
                    cancelHour += 24;
                    supportCancelDay += 1;  // 余数超过18小时需增加一天
                }
                supportCancelTime = String.format("%02d:00", cancelHour);
            } else if (cancelPolicy.contains(":")) {
                // 处理时间格式 (18:00 或 19:00:00)
                String timePart = cancelPolicy.substring(0, 5);
                if (timePart.matches("([01]?[0-9]|2[0-3]):[0-5][0-9]")) {
                    supportCancelTime = timePart;
                }
            }
            bidStrategyRequest.setSupportCancelDay(supportCancelDay);
            bidStrategyRequest.setSupportCancelTime(supportCancelTime);
        }

        // 处理是否支持免费 wifi
        bidStrategyRequest.setSupportWifi("Y".equals(item.getWirelessInclude()) ? 1 : 0);

        // 设置报价策略请求
        createBidRequest.setBidStrategy(bidStrategyRequest);
    }

    /**
     * 校验房型和价格
     */
    private void validateRoomTypeAndPrice(ImportStandardBidVO item, ImportRowErrorVO rowError, CreateBidRequest createBidRequest, List<RoomNameInfoVO> hotelAllRoomList) {
        // 临时容器, 存已经校验通过的房档房型 ID
        Map<Integer, Set<Long>> roomTypeIdsMap = new HashMap<>();

        Map<Integer, List<CreateApplicableRoomRequest>> roomTypeMap = new HashMap<>();
        Map<Integer, CreatePriceLevelRequest> roomCountMap = new  HashMap<>();
        Map<Integer, List<CreatePriceGroupRequest>> roomGroupPriceMap = new  HashMap<>();
        // 校验每个房档, 总共 5 档房型
        for (int i = 1; i <= 5; i++) {
            String roomTypeIdsStr = getFieldValue(item, "level" + i + "RoomTypeIds", String.class);
            String roomTypeDefine = getFieldValue(item, "roomType" + i + "Define", String.class);

            // 房型 id 和 房型描述都为空, 直接跳过
            if (StringUtils.isBlank(roomTypeIdsStr) && StringUtils.isBlank(roomTypeDefine)) {
                continue;
            }

            // 设置房档
            CreatePriceLevelRequest createPriceLevelRequest = new CreatePriceLevelRequest();
            // 填了房型 id, 按房型 id 逻辑走
            if (StringUtils.isNotBlank(roomTypeIdsStr)) {
                // 校验房型 id 是否重复
                List<Long> roomTypeIdList = Arrays.stream(roomTypeIdsStr.split(",")).filter(NumberUtil::isNumber).map(Long::valueOf).collect(Collectors.toList());
                Set<Long> roomTypeIdsSet = new HashSet<>(roomTypeIdList);
                if (roomTypeIdsSet.size() != roomTypeIdList.size()) {
                    rowError.addError(ErrorCode.ROOM_TYPE_ID_REPEAT, String.valueOf(i));
                }

                // 校验房型是否都存在
                Set<Long> allRoomTypeIds = hotelAllRoomList.stream().map(RoomNameInfoVO::getRoomId).collect(Collectors.toSet());
                if (!CollUtil.containsAll(allRoomTypeIds, roomTypeIdsSet)) {
                    rowError.addError(ErrorCode.ROOM_TYPE_NOT_EXIST, String.valueOf(i));
                }

                // 校验房型id是否交叉
                for (Map.Entry<Integer, Set<Long>> roomTypeIds : roomTypeIdsMap.entrySet()) {
                    if (CollUtil.containsAny(roomTypeIds.getValue(), roomTypeIdsSet)) {
                        rowError.addError(ErrorCode.ROOM_TYPE_EXIST_IN_OTHER_PRICE_LEVEL, String.valueOf(roomTypeIds.getKey()), String.valueOf(i));
                    }
                }
                // 设值, 用于下一次循环的校验
                roomTypeIdsMap.put(i, roomTypeIdsSet);

                // 设置房型 map
                MutableInt order = new MutableInt();
                List<CreateApplicableRoomRequest> priceApplicableRoomRequestList = roomTypeIdList.stream().map(roomTypeId -> {
                    CreateApplicableRoomRequest createApplicableRoomRequest = new CreateApplicableRoomRequest();
                    createApplicableRoomRequest.setRoomTypeId(roomTypeId);
                    createApplicableRoomRequest.setDisplayOrder(order.increment().get());
                    return createApplicableRoomRequest;
                }).collect(Collectors.toList());
                roomTypeMap.put(i, priceApplicableRoomRequestList);
            } else { // 没有房型 id 则取房型 define 字段
                createPriceLevelRequest.setLanyonRoomDesc(roomTypeDefine);
            }

            // 处理房型数量
            String roomCountStr = getFieldValue(item, "level" + i + "TotalRoomCount", String.class);
            if (StringUtils.isNotBlank(roomCountStr)) {
                String[] roomCountArray = roomCountStr.split(",");
                List<Integer> roomCountList = Arrays.stream(roomCountArray).map(e -> NumberUtil.isNumber(e) ? Integer.parseInt(e) : null).collect(Collectors.toList());
                createPriceLevelRequest.setRoomLevelNo(i);
                createPriceLevelRequest.setBigBedRoomCount(roomCountList.get(0));
                createPriceLevelRequest.setDoubleBedRoomCount(roomCountList.get(1));
                createPriceLevelRequest.setTotalRoomCount(roomCountList.get(2));
            }
            roomCountMap.put(i, createPriceLevelRequest);

            // 设置价格组 Map
            List<CreatePriceGroupRequest> groupPriceList = new ArrayList<>(1);

            // 目前只有一个组
            CreatePriceGroupRequest groupPrice = new CreatePriceGroupRequest();
            StringBuilder remark = new StringBuilder();

            // 处理价格
            List<CreatePriceRequest> hotelPrices = new ArrayList<>(3);

            // 先处理协议价
            BigDecimal baseSinglePrice = getFieldValue(item, "rt" + i + "Sgl", BigDecimal.class);
            BigDecimal baseDoublePrice = getFieldValue(item, "rt" + i + "Dbl", BigDecimal.class);
            if (Objects.nonNull(baseSinglePrice) || Objects.nonNull(baseDoublePrice)) {
                CreatePriceRequest baseHotelPrice = new CreatePriceRequest();
                baseHotelPrice.setPriceType(HotelPriceTypeEnum.BASE_PRICE.key);
                if (Objects.nonNull(baseSinglePrice)) {
                    remark.append("基础协议单人入住含税价");
                    BigDecimal totalBaseSinglePrice = buildRemarkAndReturnTotalPrice(remark, baseSinglePrice, createBidRequest.getTaxSetting());
                    baseHotelPrice.setOnePersonPrice(totalBaseSinglePrice);
                }
                if (Objects.nonNull(baseDoublePrice)) {
                    remark.append(",基础协议双人入住含税价");
                    BigDecimal totalBaseDoublePrice = buildRemarkAndReturnTotalPrice(remark, baseDoublePrice, createBidRequest.getTaxSetting());
                    baseHotelPrice.setTwoPersonPrice(totalBaseDoublePrice);
                }
                hotelPrices.add(baseHotelPrice);
            }

            // 处理 season 1
            BigDecimal season1SinglePrice = getFieldValue(item, "season1Rt" + i + "Sgl", BigDecimal.class);
            BigDecimal season1DoublePrice = getFieldValue(item, "season1Rt" + i + "Dbl", BigDecimal.class);
            if (Objects.nonNull(season1SinglePrice) || Objects.nonNull(season1DoublePrice)) {
                CreatePriceRequest season1HotelPrice = new CreatePriceRequest();
                season1HotelPrice.setPriceType(HotelPriceTypeEnum.SEASON_1_PRICE.key);
                if (Objects.nonNull(season1SinglePrice)) {
                    remark.append(",Season1 单人入住含税价");
                    BigDecimal season1TotalSinglePrice = buildRemarkAndReturnTotalPrice(remark, season1SinglePrice, createBidRequest.getTaxSetting());
                    season1HotelPrice.setOnePersonPrice(season1TotalSinglePrice);
                }
                if (Objects.nonNull(season1DoublePrice)) {
                    remark.append(",Season1 双人入住含税价");
                    BigDecimal season1TotalDoublePrice = buildRemarkAndReturnTotalPrice(remark, season1DoublePrice, createBidRequest.getTaxSetting());
                    season1HotelPrice.setTwoPersonPrice(season1TotalDoublePrice);
                }
                hotelPrices.add(season1HotelPrice);
            }

            // 处理 season 2
            BigDecimal season2SinglePrice = getFieldValue(item, "season2Rt" + i + "Sgl", BigDecimal.class);
            BigDecimal season2DoublePrice = getFieldValue(item, "season2Rt" + i + "Dbl", BigDecimal.class);
            if (Objects.nonNull(season1SinglePrice) || Objects.nonNull(season1DoublePrice)) {
                CreatePriceRequest season2HotelPrice = new CreatePriceRequest();
                season2HotelPrice.setPriceType(HotelPriceTypeEnum.SEASON_2_PRICE.key);
                if (Objects.nonNull(season1SinglePrice)) {
                    remark.append(",Season2 单人入住含税价");
                    BigDecimal season2SingleTotalPrice = buildRemarkAndReturnTotalPrice(remark, season2SinglePrice, createBidRequest.getTaxSetting());
                    season2HotelPrice.setOnePersonPrice(season2SingleTotalPrice);
                }
                if (Objects.nonNull(season1DoublePrice)) {
                    remark.append(",Season2 双人入住含税价");
                    BigDecimal season2TotalDoublePrice = buildRemarkAndReturnTotalPrice(remark, season2DoublePrice, createBidRequest.getTaxSetting());
                    season2HotelPrice.setTwoPersonPrice(season2TotalDoublePrice);
                }
                hotelPrices.add(season2HotelPrice);
            }

            groupPrice.setPriceList(hotelPrices);
            groupPrice.setRemark(StringUtils.removeStart(remark.toString(), ","));
            groupPriceList.add(groupPrice);

            // 设置每个房档的价格组
            roomGroupPriceMap.put(i, groupPriceList);
        }
        createBidRequest.setRoomCountMap(roomCountMap);
        createBidRequest.setRoomTypeMap(roomTypeMap);
        createBidRequest.setPriceGroupMap(roomGroupPriceMap);
    }

    /**
     * 构建备注并返回总价
     */
    private BigDecimal buildRemarkAndReturnTotalPrice(StringBuilder remark, BigDecimal price, CreateTaxSettingRequest taxSettingRequest) {
        BigDecimal totalPrice = price;
        remark.append(price);
        totalPrice = buildSubRemark(price, totalPrice, remark, taxSettingRequest.getVatgstrmFeeIsInclude(), taxSettingRequest.getVatgstrmFeeType(), taxSettingRequest.getVatgstrmFeeValue());
        totalPrice = buildSubRemark(price, totalPrice, remark, taxSettingRequest.getVatgstfbFeeIsInclude(), taxSettingRequest.getVatgstfbFeeType(), taxSettingRequest.getVatgstfbFeeValue());
        totalPrice = buildSubRemark(price, totalPrice, remark, taxSettingRequest.getServiceFeeIsInclude(), taxSettingRequest.getServiceFeeType(), taxSettingRequest.getServiceFeeValue());
        totalPrice = buildSubRemark(price, totalPrice, remark, taxSettingRequest.getOccFeeIsInclude(), taxSettingRequest.getOccFeeType(), taxSettingRequest.getOccFeeValue());
        totalPrice = buildSubRemark(price, totalPrice, remark, taxSettingRequest.getOthertx1FeeIsInclude(), taxSettingRequest.getOthertx1FeeType(), taxSettingRequest.getOthertx1FeeValue());
        totalPrice = buildSubRemark(price, totalPrice, remark, taxSettingRequest.getOthertx2FeeIsInclude(), taxSettingRequest.getOthertx2FeeType(), taxSettingRequest.getOthertx2FeeValue());
        totalPrice = buildSubRemark(price, totalPrice, remark, taxSettingRequest.getOthertx3FeeIsInclude(), taxSettingRequest.getOthertx3FeeType(), taxSettingRequest.getOthertx3FeeValue());
        if (!NumberUtil.equals(price, totalPrice)) {
            remark.append("=").append(totalPrice);
        }
        return totalPrice;
    }

    /**
     * 构建子备注
     */
    private static BigDecimal buildSubRemark(BigDecimal price, BigDecimal totalPrice, StringBuilder remark, Integer includeType, Integer feeType, BigDecimal feeValue) {
        // 如果包含了税费或者税费值小于等于0, 跳不处理
        if (Objects.isNull(includeType) || Objects.equals(includeType, 0) || Objects.isNull(feeValue) ||NumberUtil.isLessOrEqual(feeValue, BigDecimal.ZERO)) {
            return totalPrice;
        }
        // 百分比
        if (Objects.equals(feeType, 1)) {
            BigDecimal percent = NumberUtil.mul(feeValue, new BigDecimal("0.01"));
            remark.append("+(").append(price).append("*").append(percent).append(")");
            BigDecimal addPrice = NumberUtil.mul(price, percent);
            return NumberUtil.add(addPrice, totalPrice);
        }
        // 固定值
        remark.append("+(").append(feeValue).append(")");
        return NumberUtil.add(totalPrice, feeValue);
    }

    /**
     * 校验税费
     */
    private void validateTaxes(ImportStandardBidVO item, ImportRowErrorVO rowError, CreateBidRequest createBidRequest) {
        // 校验客房增值税字段是否合法, 只能为空或者 "Y" 或者 "N"
        if (!BidUtil.validateYN(item.getVatgstrmInclude())) {
            rowError.addError(ErrorCode.VATGSTRM_INCLUDE_INVALID);
        }
        // 校验客房增值税收费方式, 只能是空或者"P", "F", "N"
        if (!BidUtil.validateUom(item.getVatgstrmUom())) {
            rowError.addError(ErrorCode.VATGSTRM_UOM_INVALID);
        }

        CreateTaxSettingRequest taxSettingRequest = new CreateTaxSettingRequest();
        // 填值
        if (StringUtils.isNotEmpty(item.getVatgstrmInclude()) && StringUtils.isNotEmpty(item.getVatgstrmUom())) {
            taxSettingRequest.setVatgstrmFeeIsInclude("Y".equals(item.getVatgstrmInclude()) ? 1 : 0);
            taxSettingRequest.setVatgstrmFeeType("P".equals(item.getVatgstrmUom()) ? 1 : 2);
            taxSettingRequest.setVatgstrmFeeValue(item.getVatgstrmFee());
        }

        // 校验餐饮增值税
        if (!BidUtil.validateYN(item.getVatgstfbInclude())) {
            rowError.addError(ErrorCode.VATGSTFB_INCLUDE_INVALID);
        }
        if (!BidUtil.validateUom(item.getVatgstfbUom())) {
            rowError.addError(ErrorCode.VATGSTFB_UOM_INVALID);
        }
        if (StringUtils.isNotEmpty(item.getVatgstfbInclude()) && StringUtils.isNotEmpty(item.getVatgstfbUom())) {
            taxSettingRequest.setVatgstfbFeeIsInclude("Y".equals(item.getVatgstfbInclude()) ? 1 : 0);
            taxSettingRequest.setVatgstfbFeeType("P".equals(item.getVatgstfbUom()) ? 1 : 2);
            taxSettingRequest.setVatgstfbFeeValue(item.getVatgstfbFee());
        }

        // 校验服务费
        if (!BidUtil.validateYN(item.getServiceInclude())) {
            rowError.addError(ErrorCode.SERVICE_INCLUDE_INVALID);
        }
        if (!BidUtil.validateUom(item.getServiceUom())) {
            rowError.addError(ErrorCode.SERVICE_UOM_INVALID);
        }
        if (StringUtils.isNotEmpty(item.getServiceInclude()) && StringUtils.isNotEmpty(item.getServiceUom())) {
            taxSettingRequest.setServiceFeeIsInclude("Y".equals(item.getServiceInclude()) ? 1 : 0);
            taxSettingRequest.setServiceFeeType("P".equals(item.getServiceUom()) ? 1 : 2);
            taxSettingRequest.setServiceFeeValue(item.getServiceFee());
        }

        // 校验占用费
        if (!BidUtil.validateYN(item.getOccInclude())) {
            rowError.addError(ErrorCode.OCC_INCLUDE_INVALID);
        }
        if (!BidUtil.validateUom(item.getOccUom())) {
            rowError.addError(ErrorCode.OCC_UOM_INVALID);
        }
        if (StringUtils.isNotEmpty(item.getOccInclude()) && StringUtils.isNotEmpty(item.getOccUom())) {
            taxSettingRequest.setOccFeeIsInclude("Y".equals(item.getOccInclude()) ? 1 : 0);
            taxSettingRequest.setOccFeeType("P".equals(item.getOccUom()) ? 1 : 2);
            taxSettingRequest.setOccFeeValue(item.getOccFee());
        }

        // 校验其他税费1
        if (!BidUtil.validateYN(item.getOtherTxFee1Incl())) {
            rowError.addError(ErrorCode.OTHER_TX_FEE_1_INCLUDE_INVALID);
        }
        if (!BidUtil.validateUom(item.getOtherTxFee1Uom())) {
            rowError.addError(ErrorCode.OTHER_TX_FEE_1_UOM_INVALID);
        }
        if (StringUtils.isNotEmpty(item.getOtherTxFee1Incl()) && StringUtils.isNotEmpty(item.getOtherTxFee1Uom())) {
            taxSettingRequest.setOthertx1FeeIsInclude("Y".equals(item.getOtherTxFee1Incl()) ? 1 : 0);
            taxSettingRequest.setOthertx1FeeType("P".equals(item.getOtherTxFee1Uom()) ? 1 : 2);
            taxSettingRequest.setOthertx1FeeValue(item.getOtherTxFee1());
            taxSettingRequest.setOthertx1FeeDesc(item.getOtherTxFee1Desc());
        }

        // 校验其他税费2
        if (!BidUtil.validateYN(item.getOtherTxFee2Incl())) {
            rowError.addError(ErrorCode.OTHER_TX_FEE_2_INCLUDE_INVALID);
        }
        if (!BidUtil.validateUom(item.getOtherTxFee2Uom())) {
            rowError.addError(ErrorCode.OTHER_TX_FEE_2_UOM_INVALID);
        }
        if (StringUtils.isNotEmpty(item.getOtherTxFee2Incl()) && StringUtils.isNotEmpty(item.getOtherTxFee2Uom())) {
            taxSettingRequest.setOthertx2FeeIsInclude("Y".equals(item.getOtherTxFee2Incl()) ? 1 : 0);
            taxSettingRequest.setOthertx2FeeType("P".equals(item.getOtherTxFee2Uom()) ? 1 : 2);
            taxSettingRequest.setOthertx2FeeValue(item.getOtherTxFee2());
            taxSettingRequest.setOthertx2FeeDesc(item.getOtherTxFee2Desc());
        }

        // 校验其他税费3
        if (!BidUtil.validateYN(item.getOtherTxFee3Incl())) {
            rowError.addError(ErrorCode.OTHER_TX_FEE_3_INCLUDE_INVALID);
        }
        if (!BidUtil.validateUom(item.getOtherTxFee3Uom())) {
            rowError.addError(ErrorCode.OTHER_TX_FEE_3_UOM_INVALID);
        }
        if (StringUtils.isNotEmpty(item.getOtherTxFee3Incl()) && StringUtils.isNotEmpty(item.getOtherTxFee3Uom())) {
            taxSettingRequest.setOthertx3FeeIsInclude("Y".equals(item.getOtherTxFee3Incl()) ? 1 : 0);
            taxSettingRequest.setOthertx3FeeType("P".equals(item.getOtherTxFee3Uom()) ? 1 : 2);
            taxSettingRequest.setOthertx3FeeValue(item.getOtherTxFee3());
            taxSettingRequest.setOthertx3FeeDesc(item.getOtherTxFee3Desc());
        }
        createBidRequest.setTaxSetting(taxSettingRequest);
    }

    /**
     * 校验报价日期
     */
    private static void validateBidDate(ImportStandardBidVO item, ImportRowErrorVO rowError, CreateBidRequest createBidRequest) {
        // 合同开始时间不可为空  为有效日期格式 例如：2025/1/1
        if (StringUtils.isBlank(item.getCorpStart())) {
            rowError.addError(ErrorCode.CONTRACT_START_DATE_CANNOT_BE_EMPTY);
        }

        // 合同结束时间不可为空 为有效日期格式 例如：2025/12/31
        if (StringUtils.isBlank(item.getCorpEnd())) {
            rowError.addError(ErrorCode.CONTRACT_END_DATE_CANNOT_BE_EMPTY);
        }

        // 两个时间都为空, 下面的都不用校验了
        if (StringUtils.isBlank(item.getCorpEnd()) || StringUtils.isBlank(item.getCorpStart())) {
            return;
        }

        // 校验时间格式
        DateTime corpStartDate;
        DateTime corpEndDate;
        try {
            corpStartDate = cn.hutool.core.date.DateUtil.parse(item.getCorpStart(), "yyyy/MM/dd");
            corpEndDate = cn.hutool.core.date.DateUtil.parse(item.getCorpEnd(), "yyyy/MM/dd");

            // 报价开始时间不能小于结束时间
            if (corpStartDate.after(corpEndDate)) {
                rowError.addError(ErrorCode.CONTRACT_START_DATE_CANNOT_AFTER_END_DATE);
            }
            createBidRequest.setBidStartTime(corpStartDate);
            createBidRequest.setBidEndTime(corpEndDate);
        } catch (Exception e) {
            rowError.addError(ErrorCode.CONTRACT_DATE_FORMAT_INVALID);
        }
    }

    /**
     * 校验报价
     */
    private void validateBidStatus(ImportRowErrorVO rowError, Integer projectId, Long hotelId) {
        ProjectIntentHotelEntity existBid = projectIntentHotelMapper.selectByProjectIdAndHotelId(projectId, hotelId);
        // 校验状态
        if (Objects.nonNull(existBid) && !Objects.equals(existBid.getBidState(), HotelBidStateEnum.NO_BID.bidState) &&
            !Objects.equals(existBid.getBidState(), HotelBidStateEnum.UNDER_NEGOTIATION.bidState)) {
            // 当前报价状态不是议价中不允许修改报价
            rowError.addError(ErrorCode.CANNOT_UPDATE_BID_DUE_TO_BID_STATE_NOT_UNDER_NEGOTIATION);
        }
        // 校验存在的报价是否是酒店集团报价, 不是的话不允许修改
        if (Objects.nonNull(existBid) && !Objects.equals(existBid.getBidOrgType(), OrgTypeEnum.HOTELGROUP.key)) {
            rowError.addError(ErrorCode.CANNOT_UPDATE_BID_DUE_TO_BID_ORG_NOT_HOTEL_GROUP);
        }
    }

    /**
     * 校验不适用日期
     */
    private void validateUnApplicableDates(ImportStandardBidVO item, ImportRowErrorVO rowError, CreateBidRequest createBidRequest) {
        // 暂存不适用日期, 用于校验是否重叠
        Map<Integer, Range<Date>> unapplicableDateMap = new HashMap<>();
        for (int index = 1; index <= 10; index++) {
            // 利用反射取对应的字段值
            String bdStart = getFieldValue(item, "bd" + index + "Start", String.class);
            String bdEnd = getFieldValue(item, "bd" + index + "End", String.class);
            if (StringUtils.isBlank(bdStart) || StringUtils.isBlank(bdEnd)) {
                continue;
            }
            Date bdStartDate = null;
            Date bdEndDate = null;
            try {
                bdStartDate = cn.hutool.core.date.DateUtil.parse(bdStart, "yyyy/MM/dd");
                bdEndDate = cn.hutool.core.date.DateUtil.parse(bdEnd, "yyyy/MM/dd");
            } catch (Exception e) {
                rowError.addError(ErrorCode.CONTRACT_DATE_FORMAT_INVALID, String.valueOf(index));
            }

            Date bidStartDate = createBidRequest.getBidStartTime();
            Date bidEndDate = createBidRequest.getBidEndTime();
            if (Objects.nonNull(bidStartDate) && Objects.nonNull(bidEndDate)) {
                // 开始时间不能超过协议开始日期
                if (Objects.nonNull(bdStartDate) && !cn.hutool.core.date.DateUtil.isIn(bdStartDate, bidStartDate, bidEndDate)) {
                    rowError.addError(ErrorCode.BD_START_DATE_NOT_IN_CONTRACT_DATE_RANGE, String.valueOf(index));
                }
                // 结束时间不能超过协议结束日期
                if (Objects.nonNull(bdEndDate) && !cn.hutool.core.date.DateUtil.isIn(bdEndDate, bidStartDate, bidEndDate)) {
                    rowError.addError(ErrorCode.BD_END_DATE_NOT_IN_CONTRACT_DATE_RANGE, String.valueOf(index));
                }
            }

            // 校验和前面的日期是否重叠
            if (Objects.nonNull(bdStartDate) && Objects.nonNull(bdEndDate)) {
                for (Map.Entry<Integer, Range<Date>> dateRange : unapplicableDateMap.entrySet()) {
                    Integer i = dateRange.getKey();
                    Range<Date> date = dateRange.getValue();
                    if (cn.hutool.core.date.DateUtil.isIn(bdStartDate, date.getMinimum(), date.getMaximum()) ||
                        cn.hutool.core.date.DateUtil.isIn(bdEndDate, date.getMinimum(), date.getMaximum())) {
                        rowError.addError(ErrorCode.BD_DATE_CROSS, String.valueOf(index), String.valueOf(i));
                    }
                }
                unapplicableDateMap.put(index, Range.between(bdStartDate, bdEndDate));
            }
        }
        createBidRequest.setUnapplicableDates(new ArrayList<>(unapplicableDateMap.values()));
    }

    /**
     * 校验项目自定义策略
     */
    private void validateCustomStrategy(ImportStandardBidVO item, ImportRowErrorVO rowError,
                                        List<ProjectCustomTendStrategyEntity> customTendStrategies, Map<Long, List<CustomStrategyOptionVO>> customStrategyOptionMap,
                                        CreateBidRequest createBidRequest) {
        List<CreateCustomBidStrategyRequest> projectCustomBidStrategyList = new ArrayList<>();
        if (CollUtil.isEmpty(customTendStrategies)) {
            createBidRequest.setCustomBidStrategies(projectCustomBidStrategyList);
            return;
        }
        for (int i = 0; i < customTendStrategies.size(); i++) {
            ProjectCustomTendStrategyEntity customTendStrategy = customTendStrategies.get(i);

            // 使用反射根据索引获取对应的字段的值
            int strategyIndex = i + 1;
            String supportCustomBidStrategy = getFieldValue(item, "supportCustomBidStrategy" + strategyIndex, String.class);

            // 校验
            if (CustomStrategyTypeEnum.YSE_OR_NO.key == customTendStrategy.getStrategyType() && !BidUtil.validateYN(supportCustomBidStrategy)) {
                rowError.addError(ErrorCode.CUSTOM_STRATEGY_INVALID, supportCustomBidStrategy);
                continue;
            }

            CreateCustomBidStrategyRequest customBidStrategyRequest = new CreateCustomBidStrategyRequest();
            customBidStrategyRequest.setCustomTendStrategyId(customTendStrategy.getId().intValue());
            customBidStrategyRequest.setStrategyName(customTendStrategy.getStrategyName());
            customBidStrategyRequest.setStrategyType(customTendStrategy.getStrategyType());
            if (CustomStrategyTypeEnum.YSE_OR_NO.key == customTendStrategy.getStrategyType()) {
                customBidStrategyRequest.setSupportStrategyName("Y".equals(supportCustomBidStrategy) ? 1 : 0);
            }
            if (CustomStrategyTypeEnum.TEXT.key == customTendStrategy.getStrategyType()) {
                customBidStrategyRequest.setSupportStrategyText(supportCustomBidStrategy);
            }
            if (CustomStrategyTypeEnum.isOptionType(customTendStrategy.getStrategyType())) {
                // 导入值按分号分割
                Set<String> inputOptions = Optional.ofNullable(supportCustomBidStrategy)
                    .map(str -> StrSplitter.splitTrim(str, ";", true))
                    .map(HashSet::new)
                    .orElseGet(HashSet::new);

                // 构建选项
                List<CustomStrategyBidOptionVO> options = Optional.ofNullable(customStrategyOptionMap.get(customTendStrategy.getId()))
                    .filter(CollUtil::isNotEmpty)
                    .map(optionList ->
                        optionList.stream().map(option -> {
                            CustomStrategyBidOptionVO vo = new CustomStrategyBidOptionVO();
                            vo.setOptionId(option.getId());
                            vo.setOptionName(option.getOptionName());
                            vo.setIsSupport(inputOptions.contains(option.getOptionName()) ? YesOrNoEnum.YES.getKey() : YesOrNoEnum.NO.getKey());
                            return vo;
                        }).collect(Collectors.toList())
                    ).orElseGet(ArrayList::new);
                customBidStrategyRequest.setOptions(options);
            }
            projectCustomBidStrategyList.add(customBidStrategyRequest);
        }
        createBidRequest.setCustomBidStrategies(projectCustomBidStrategyList);
    }

    /**
     * 校验 season 1 和 2 日期是否交叉
     */
    private void validateSeasonDateCross(ImportRowErrorVO rowError, CreateBidRequest createBidRequest) {
        if (CollUtil.isEmpty(createBidRequest.getSeason1Dates()) || CollUtil.isEmpty(createBidRequest.getSeason2Dates())) {
            return;
        }

        // 遍历 season 1
        List<Range<Date>> season1Dates = createBidRequest.getSeason1Dates();
        List<Range<Date>> season2Dates = createBidRequest.getSeason2Dates();
        for (Range<Date> season1Date : season1Dates) {
            Date season1StartDate = season1Date.getMinimum();
            Date season1EndDate = season1Date.getMaximum();
            for (Range<Date> season2Date : season2Dates) {
                Date season2StartDate = season2Date.getMinimum();
                Date season2EndDate = season2Date.getMaximum();
                // 判断是否交叉
                if (cn.hutool.core.date.DateUtil.isIn(season1StartDate, season2StartDate, season2EndDate) ||
                    cn.hutool.core.date.DateUtil.isIn(season1EndDate, season2StartDate, season2EndDate)) {
                    rowError.addError(ErrorCode.SEASON_1_AND_2_DATE_CROSS);
                }
            }
        }
    }

    /**
     * 校验 season 日期
     */
    private void validateSeasonDate(ImportStandardBidVO item, ImportRowErrorVO rowError, CreateBidRequest createBidRequest) {
        // 遍历校验 season 1 和 season 2
        for (int i = 1; i <= 2; i++) {
            String seasonStartEndListStr = getFieldValue(item, "season" + i + "StartEnd", String.class);
            if (StringUtils.isBlank(seasonStartEndListStr)) {
                continue;
            }
            List<Range<Date>> seasonDates = new ArrayList<>();
            String[] seasonStartEndList = seasonStartEndListStr.split(",");
            for (String seasonStartEnd : seasonStartEndList) {
                // 按 - 分割
                String[] seasonStartEndArr = seasonStartEnd.split("-");
                if (seasonStartEndArr.length != 2) {
                    rowError.addError(ErrorCode.SEASON_START_END_DATE_FORMAT_INVALID, String.valueOf(i));
                    continue;
                }

                // 校验开始时间
                DateTime seasonStartDate = null;
                try {
                    seasonStartDate = cn.hutool.core.date.DateUtil.parse(seasonStartEndArr[0], "yyyy/MM/dd");
                    // 判断开始时间是否在合同时间内
                    if (!cn.hutool.core.date.DateUtil.isIn(seasonStartDate, createBidRequest.getBidStartTime(), createBidRequest.getBidEndTime())) {
                        rowError.addError(ErrorCode.SEASON_START_DATE_NOT_IN_CONTRACT_DATE_RANGE, String.valueOf(i));
                    }
                } catch (Exception e) {
                    rowError.addError(ErrorCode.SEASON_START_END_DATE_FORMAT_INVALID, String.valueOf(i));
                }

                // 转换结束时间
                DateTime seasonEndDate = null;
                try {
                    seasonEndDate = cn.hutool.core.date.DateUtil.parse(seasonStartEndArr[1], "yyyy/MM/dd");
                    // 判断结束时间是否在合同时间内
                    if (!cn.hutool.core.date.DateUtil.isIn(seasonEndDate, createBidRequest.getBidStartTime(), createBidRequest.getBidEndTime())) {
                        rowError.addError(ErrorCode.SEASON_END_DATE_NOT_IN_CONTRACT_DATE_RANGE, String.valueOf(i));
                    }
                } catch (Exception e) {
                    rowError.addError(ErrorCode.SEASON_START_END_DATE_FORMAT_INVALID, String.valueOf(i));
                }

                if (Objects.nonNull(seasonStartDate) && Objects.nonNull(seasonEndDate)) {
                    seasonDates.add(Range.between(seasonStartDate, seasonEndDate));
                }
            }
            // 设置对应的字段值
            ReflectUtil.setFieldValue(createBidRequest, "season" + i + "Dates", seasonDates);
        }

        // 校验两个日期是否交叉
        validateSeasonDateCross(rowError, createBidRequest);
    }

    /**
     * 反射获取字段值, 并转换成对应的类型
     */
    private static <T> T getFieldValue(Object obj, String fieldName, Class<T> clazz) {
        return Optional.ofNullable(ReflectUtil.getFieldValue(obj, fieldName)).map(clazz::cast).orElse(null);
    }


    public LanyonImportDataEntity insertOrUpdateLanyonImportData(LanyonImportDataEntity lanyonImportData) {
        LanyonImportDataEntity dbLanyonImportData = lanyonImportDataMapper.getLanyonImportData(lanyonImportData.getProjectId(), lanyonImportData.getHotelId(), lanyonImportData.getDataType());
        if(dbLanyonImportData == null){
            lanyonImportDataMapper.insert(lanyonImportData);
        } else {
            lanyonImportData.setLanyonImportDataId(dbLanyonImportData.getLanyonImportDataId());
            lanyonImportDataMapper.updateById(lanyonImportData);
        }
        return  lanyonImportData;
    }

}
