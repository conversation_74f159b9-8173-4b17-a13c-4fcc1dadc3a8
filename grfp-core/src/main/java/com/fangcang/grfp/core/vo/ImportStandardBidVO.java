package com.fangcang.grfp.core.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fangcang.grfp.core.base.ImportVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
@ApiModel(description = "导入报价详情")
public class ImportStandardBidVO extends ImportVO {

    private static final long serialVersionUID = 8559379018743517810L;

    /**
     * 项目 ID
     */
    @ExcelIgnore
    @ApiModelProperty(hidden = true)
    private transient Integer projectId;

    /**
     * 酒店集团编码
     */
    @ExcelProperty(value = {"PROPCODE", "集团内部酒店编码"}, index = 0)
    @ApiModelProperty(value = "集团内部酒店编码")
    private String propCode;

    /**
     * AMADEUS 连锁编码
     */
    @ExcelProperty(value = {"AMADEUS chain code", "AMADEUS 集团编码"}, index = 1)
    @ApiModelProperty(value = "AMADEUS 集团编码")
    private String amadeusChainCode;

    /**
     * AMADEUS 酒店编码
     */
    @ExcelProperty(value = {"AMADEUS HOTELCODE", "AMADEUS 酒店编码"}, index = 2)
    @ApiModelProperty(value = "AMADEUS 酒店编码")
    private String amadeusHotelCode;

    /**
     * 酒店名称 (AMADEUS)
     */
    @ExcelProperty(value = {"PROPNAME", "酒店名称"}, index = 3)
    @ApiModelProperty(value = "酒店名称")
    private String propName;

    /**
     * 酒店电话 (AMADEUS)
     */
    @ExcelProperty(value = {"PRORPHONE", "酒店电话"}, index = 4)
    @ApiModelProperty(value = "酒店电话")
    private String propPhone;

    /**
     * 酒店地址 (AMADEUS)
     */
    @ExcelProperty(value = {"PROPADD", "酒店地址"}, index = 5)
    @ApiModelProperty(value = "酒店地址")
    private String propAddress;

    /**
     * 酒店城市 (AMADEUS)
     */
    @ExcelProperty(value = {"PROPCITY", "城市"}, index = 6)
    @ApiModelProperty(value = "城市")
    private String propCity;

    /**
     * 酒店省/州 (AMADEUS)
     */
    @ExcelProperty(value = {"PROPSTATEPROV", "州/省"}, index = 7)
    @ApiModelProperty(value = "州/省")
    private String propStateProv;

    /**
     * 酒店国家 (AMADEUS)
     */
    @ExcelProperty(value = {"PROPCOUNTRY", "国家"}, index = 8)
    @ApiModelProperty(value = "国家")
    private String propCountry;

    /**
     * 酒店 id
     */
    @ExcelProperty(value = {"FCPROPCODE", "房仓酒店编码"}, index = 9)
    @ApiModelProperty(value = "房仓酒店编码")
    private Long hotelId;

    /**
     * 酒店名称
     */
    @ExcelProperty(value = {"FCPROPNAME", "房仓酒店名称 (根据编码自动匹配用于验证)"}, index = 10)
    @ApiModelProperty(value = "房仓酒店名称 (根据编码自动匹配用于验证)")
    private String hotelName;

    /**
     * 酒店联系人电话
     */
    @ExcelProperty(value = {"FCPRORPHONE", "房仓酒店电话  (根据编码自动匹配用于验证)"}, index = 11)
    @ApiModelProperty(value = "房仓酒店电话  (根据编码自动匹配用于验证)")
    private String telephone;

    /**
     * 酒店地址
     */
    @ExcelProperty(value = {"FCPROPADD", "房仓酒店地址  (根据编码自动匹配用于验证)"}, index = 12)
    @ApiModelProperty(value = "房仓酒店地址  (根据编码自动匹配用于验证)")
    private String address;

    /**
     * 城市名称
     */
    @ExcelProperty(value = {"FCPROPCITY", "房仓酒店城市  (根据编码自动匹配用于验证)"}, index = 13)
    @ApiModelProperty(value = "房仓酒店城市  (根据编码自动匹配用于验证)")
    private String cityName;

    /**
     * 省/州
     */
    @ExcelProperty(value = {"FCPROPSTATEPROV", "房仓酒店州/省  (根据编码自动匹配用于验证)"}, index = 14)
    @ApiModelProperty(value = "房仓酒店州/省  (根据编码自动匹配用于验证)")
    private String provinceName;

    /**
     * 国家
     */
    @ExcelProperty(value = {"FCPROPCOUNTRY", "房仓酒店国家  (根据编码自动匹配用于验证)"}, index = 15)
    @ApiModelProperty(value = "房仓酒店国家  (根据编码自动匹配用于验证)")
    private String countryName;

    /**
     * 销售人姓名
     */
    @ExcelProperty(value = {"Sales name", "酒店销售姓名"}, index = 16)
    @ApiModelProperty(value = "酒店销售姓名")
    private String hotelSalesContactName;

    /**
     * 酒店销售邮箱
     */
    @ExcelProperty(value = {"PROPSALESGENREMAIL", "酒店销售邮箱地址"}, index = 17)
    @ApiModelProperty(value = "酒店销售邮箱地址")
    private String hotelSalesContactEmail;

    /**
     * 酒店销售电话
     */
    @ExcelProperty(value = {"Sales mobile", "销售联系电话"}, index = 18)
    @ApiModelProperty(value = "销售联系电话")
    private String hotelSalesContactMobile;

    /**
     * 币种
     */
    @ExcelProperty(value = {"RATE_CURR", "货币"}, index = 19)
    @ApiModelProperty(value = "货币")
    private String currencyCode;

    /**
     * 房型1定义 （多个用英文逗号隔开）
     */
    @ExcelProperty(value = {"ROOMTYPE1DEFINE", "房型1定义 （多个用英文逗号隔开）"}, index = 20)
    @ApiModelProperty(value = "房型1定义 （多个用英文逗号隔开）")
    private String roomType1Define;

    /**
     * 房仓房型1编码（多个用英文逗号隔开）
     */
    @ExcelProperty(value = {"FCROOMTYPE1CODE", "房仓房型1编码（多个用英文逗号隔开）"}, index = 21)
    @ApiModelProperty(value = "房仓房型1编码（多个用英文逗号隔开）")
    private String level1RoomTypeIds;

    /**
     * 房仓房型1名称（多个用英文逗号隔开）
     */
    @ExcelProperty(value = {"FCROOMTYPE1NAME", "房仓房型1名称（多个用英文逗号隔开）"}, index = 22)
    @ApiModelProperty(value = "房仓房型1名称（多个用英文逗号隔开）")
    private String level1RoomTypeNames;

    /**
     * 房型1数量 (大床数量，双床数量，总房间数量)
     */
    @ExcelProperty(value = {"ROOMTYPE1NUMBER", "房型1数量 (大床数量，双床数量，总房间数量)"}, index = 23)
    @ApiModelProperty(value = "房型1数量 (大床数量，双床数量，总房间数量)")
    private String level1TotalRoomCount;

    /**
     * 房型2定义 （多个用英文逗号隔开）
     */
    @ExcelProperty(value = {"ROOMTYPE2DEFINE", "房型2定义 （多个用英文逗号隔开）"}, index = 24)
    @ApiModelProperty(value = "房型2定义 （多个用英文逗号隔开）")
    private String roomType2Define;

    /**
     * 房仓房型2编码（多个用英文逗号隔开）
     */
    @ExcelProperty(value = {"FCROOMTYPE2CODE", "房仓房型2编码（多个用英文逗号隔开）"}, index = 25)
    @ApiModelProperty(value = "房仓房型2编码（多个用英文逗号隔开）")
    private String level2RoomTypeIds;

    /**
     * 房仓房型2名称（多个用英文逗号隔开）
     */
    @ExcelProperty(value = {"FCROOMTYPE2NAME", "房仓房型2名称（多个用英文逗号隔开）"}, index = 26)
    @ApiModelProperty(value = "房仓房型2名称（多个用英文逗号隔开）")
    private String level2RoomTypeNames;

    /**
     * 房型2数量 (大床数量，双床数量，总房间数量)
     */
    @ExcelProperty(value = {"ROOMTYPE2NUMBER", "房型2数量 (大床数量，双床数量，总房间数量)"}, index = 27)
    @ApiModelProperty(value = "房型2数量 (大床数量，双床数量，总房间数量)")
    private String level2TotalRoomCount;

    /**
     * 房型3定义 （多个用英文逗号隔开）
     */
    @ExcelProperty(value = {"ROOMTYPE3DEFINE", "房型3定义 （多个用英文逗号隔开）"}, index = 28)
    @ApiModelProperty(value = "房型3定义 （多个用英文逗号隔开）")
    private String roomType3Define;

    /**
     * 房仓房型3编码（多个用英文逗号隔开）
     */
    @ExcelProperty(value = {"FCROOMTYPE3CODE", "房仓房型3编码（多个用英文逗号隔开）"}, index = 29)
    @ApiModelProperty(value = "房仓房型3编码（多个用英文逗号隔开）")
    private String level3RoomTypeIds;

    /**
     * 房仓房型3名称（多个用英文逗号隔开）
     */
    @ExcelProperty(value = {"FCROOMTYPE3NAME", "房仓房型3名称（多个用英文逗号隔开）"}, index = 30)
    @ApiModelProperty(value = "房仓房型3名称（多个用英文逗号隔开）")
    private String level3RoomTypeNames;

    /**
     * 房型3数量 (大床数量，双床数量，总房间数量)
     */
    @ExcelProperty(value = {"ROOMTYPE3NUMBER", "房型3数量 (大床数量，双床数量，总房间数量)"}, index = 31)
    @ApiModelProperty(value = "房型3数量 (大床数量，双床数量，总房间数量)")
    private String level3TotalRoomCount;

    /**
     * 房型4定义 （多个用英文逗号隔开）
     */
    @ExcelProperty(value = {"ROOMTYPE4DEFINE", "房型4定义 （多个用英文逗号隔开）"}, index = 32)
    @ApiModelProperty(value = "房型4定义 （多个用英文逗号隔开）")
    private String roomType4Define;

    /**
     * 房仓房型4编码（多个用英文逗号隔开）
     */
    @ExcelProperty(value = {"FCROOMTYPE4CODE", "房仓房型4编码（多个用英文逗号隔开）"}, index = 33)
    @ApiModelProperty(value = "房仓房型4编码（多个用英文逗号隔开）")
    private String level4RoomTypeIds;

    /**
     * 房仓房型4名称（多个用英文逗号隔开）
     */
    @ExcelProperty(value = {"FCROOMTYPE4NAME", "房仓房型4名称（多个用英文逗号隔开）"}, index = 34)
    @ApiModelProperty(value = "房仓房型4名称（多个用英文逗号隔开）")
    private String level4RoomTypeNames;

    /**
     * 房型4数量 (大床数量，双床数量，总房间数量)
     */
    @ExcelProperty(value = {"ROOMTYPE4NUMBER", "房型4数量 (大床数量，双床数量，总房间数量)"}, index = 35)
    @ApiModelProperty(value = "房型4数量 (大床数量，双床数量，总房间数量)")
    private String level4TotalRoomCount;

    /**
     * 房型5定义 （多个用英文逗号隔开）
     */
    @ExcelProperty(value = {"ROOMTYPE5DEFINE", "房型5定义 （多个用英文逗号隔开）"}, index = 36)
    @ApiModelProperty(value = "房型5定义 （多个用英文逗号隔开）")
    private String roomType5Define;

    /**
     * 房仓房型5编码（多个用英文逗号隔开）
     */
    @ExcelProperty(value = {"FCROOMTYPE5CODE", "房仓房型5编码（多个用英文逗号隔开）"}, index = 37)
    @ApiModelProperty(value = "房仓房型5编码（多个用英文逗号隔开）")
    private String level5RoomTypeIds;

    /**
     * 房仓房型5名称（多个用英文逗号隔开）
     */
    @ExcelProperty(value = {"FCROOMTYPE5NAME", "房仓房型5名称（多个用英文逗号隔开）"}, index = 38)
    @ApiModelProperty(value = "房仓房型5名称（多个用英文逗号隔开）")
    private String level5RoomTypeNames;

    /**
     * 房型5数量 (大床数量，双床数量，总房间数量)
     */
    @ExcelProperty(value = {"ROOMTYPE5NUMBER", "房型5数量 (大床数量，双床数量，总房间数量)"}, index = 39)
    @ApiModelProperty(value = "房型5数量 (大床数量，双床数量，总房间数量)")
    private String level5TotalRoomCount;

    /**
     * 协议价开始时间
     */
    @ExcelProperty(value = {"Corp START", "协议价开始时间"}, index = 40)
    @ApiModelProperty(value = "协议价开始时间")
    private String corpStart;

    /**
     * 协议价结束时间
     */
    @ExcelProperty(value = {"Corp END", "协议价结束时间"}, index = 41)
    @ApiModelProperty(value = "协议价结束时间")
    private String corpEnd;

    @ExcelProperty(value = {"LRA_RT1_SGL_EXB", "LRA_房档1_单人_不含早"}, index = 42)
    @ApiModelProperty(value = "LRA_房档1_单人_不含早")
    private BigDecimal lraRt1SglExb;

    @ExcelProperty(value = {"LRA_RT1_SGL_INB", "LRA_房档1_单人_含早"}, index = 43)
    @ApiModelProperty(value = "LRA_房档1_单人_含早")
    private BigDecimal lraRt1SglInb;

    @ExcelProperty(value = {"LRA_RT1_DBL_EXB", "LRA_房档1_双人_不含早"}, index = 44)
    @ApiModelProperty(value = "LRA_房档1_双人_不含早")
    private BigDecimal lraRt1DblExb;

    @ExcelProperty(value = {"LRA_RT1_DBL_INB", "LRA_房档1_双人_含早"}, index = 45)
    @ApiModelProperty(value = "LRA_房档1_双人_含早")
    private BigDecimal lraRt1DblInb;

    @ExcelProperty(value = {"NLRA_RT1_SGL_EXB", "NLRA_房档1_单人_不含早"}, index = 46)
    @ApiModelProperty(value = "NLRA_房档1_单人_不含早")
    private BigDecimal nLraRt1SglExb;

    @ExcelProperty(value = {"NLRA_RT1_SGL_INB", "NLRA_房档1_单人_含早"}, index = 47)
    @ApiModelProperty(value = "NLRA_房档1_单人_含早")
    private BigDecimal nLraRt1SglInb;

    @ExcelProperty(value = {"NLRA_RT1_DBL_EXB", "NLRA_房档1_双人_不含早"}, index = 48)
    @ApiModelProperty(value = "NLRA_房档1_双人_不含早")
    private BigDecimal nLraRt1DblExb;

    @ExcelProperty(value = {"NLRA_RT1_DBL_INB", "NLRA_房档1_双人_含早"}, index = 49)
    @ApiModelProperty(value = "NLRA_房档1_双人_含早")
    private BigDecimal nLraRt1DblInb;

    @ExcelProperty(value = {"LRA_RT2_SGL_EXB", "LRA_房档2_单人_不含早"}, index = 50)
    @ApiModelProperty(value = "LRA_房档2_单人_不含早")
    private BigDecimal lraRt2SglExb;

    @ExcelProperty(value = {"LRA_RT2_SGL_INB", "LRA_房档2_单人_含早"}, index = 51)
    @ApiModelProperty(value = "LRA_房档2_单人_含早")
    private BigDecimal lraRt2SglInb;

    @ExcelProperty(value = {"LRA_RT2_DBL_EXB", "LRA_房档2_双人_不含早"}, index = 52)
    @ApiModelProperty(value = "LRA_房档2_双人_不含早")
    private BigDecimal lraRt2DblExb;

    @ExcelProperty(value = {"LRA_RT2_DBL_INB", "LRA_房档2_双人_含早"}, index = 53)
    @ApiModelProperty(value = "LRA_房档2_双人_含早")
    private BigDecimal lraRt2DblInb;

    @ExcelProperty(value = {"NLRA_RT2_SGL_EXB", "NLRA_房档2_单人_不含早"}, index = 54)
    @ApiModelProperty(value = "NLRA_房档2_单人_不含早")
    private BigDecimal nLraRt2SglExb;

    @ExcelProperty(value = {"NLRA_RT2_SGL_INB", "NLRA_房档2_单人_含早"}, index = 55)
    @ApiModelProperty(value = "NLRA_房档2_单人_含早")
    private BigDecimal nLraRt2SglInb;

    @ExcelProperty(value = {"NLRA_RT2_DBL_EXB", "NLRA_房档2_双人_不含早"}, index = 56)
    @ApiModelProperty(value = "NLRA_房档2_双人_不含早")
    private BigDecimal nLraRt2DblExb;

    @ExcelProperty(value = {"NLRA_RT2_DBL_INB", "NLRA_房档2_双人_含早"}, index = 57)
    @ApiModelProperty(value = "NLRA_房档2_双人_含早")
    private BigDecimal nLraRt2DblInb;
    

    @ExcelProperty(value = {"LRA_RT3_SGL_EXB", "LRA_房档3_单人_不含早"}, index = 58)
    @ApiModelProperty(value = "LRA_房档3_单人_不含早")
    private BigDecimal lraRt3SglExb;

    @ExcelProperty(value = {"LRA_RT3_SGL_INB", "LRA_房档3_单人_含早"}, index = 59)
    @ApiModelProperty(value = "LRA_房档3_单人_含早")
    private BigDecimal lraRt3SglInb;

    @ExcelProperty(value = {"LRA_RT3_DBL_EXB", "LRA_房档3_双人_不含早"}, index = 60)
    @ApiModelProperty(value = "LRA_房档3_双人_不含早")
    private BigDecimal lraRt3DblExb;

    @ExcelProperty(value = {"LRA_RT3_DBL_INB", "LRA_房档3_双人_含早"}, index = 61)
    @ApiModelProperty(value = "LRA_房档3_双人_含早")
    private BigDecimal lraRt3DblInb;

    @ExcelProperty(value = {"NLRA_RT3_SGL_EXB", "NLRA_房档3_单人_不含早"}, index = 62)
    @ApiModelProperty(value = "NLRA_房档3_单人_不含早")
    private BigDecimal nLraRt3SglExb;

    @ExcelProperty(value = {"NLRA_RT3_SGL_INB", "NLRA_房档3_单人_含早"}, index = 63)
    @ApiModelProperty(value = "NLRA_房档3_单人_含早")
    private BigDecimal nLraRt3SglInb;

    @ExcelProperty(value = {"NLRA_RT3_DBL_EXB", "NLRA_房档3_双人_不含早"}, index = 64)
    @ApiModelProperty(value = "NLRA_房档3_双人_不含早")
    private BigDecimal nLraRt3DblExb;

    @ExcelProperty(value = {"NLRA_RT3_DBL_INB", "NLRA_房档3_双人_含早"}, index = 65)
    @ApiModelProperty(value = "NLRA_房档3_双人_含早")
    private BigDecimal nLraRt3DblInb;

    @ExcelProperty(value = {"LRA_RT4_SGL_EXB", "LRA_房档4_单人_不含早"}, index = 66)
    @ApiModelProperty(value = "LRA_房档4_单人_不含早")
    private BigDecimal lraRt4SglExb;

    @ExcelProperty(value = {"LRA_RT4_SGL_INB", "LRA_房档4_单人_含早"}, index = 67)
    @ApiModelProperty(value = "LRA_房档4_单人_含早")
    private BigDecimal lraRt4SglInb;

    @ExcelProperty(value = {"LRA_RT4_DBL_EXB", "LRA_房档4_双人_不含早"}, index = 68)
    @ApiModelProperty(value = "LRA_房档4_双人_不含早")
    private BigDecimal lraRt4DblExb;

    @ExcelProperty(value = {"LRA_RT4_DBL_INB", "LRA_房档4_双人_含早"}, index = 69)
    @ApiModelProperty(value = "LRA_房档4_双人_含早")
    private BigDecimal lraRt4DblInb;

    @ExcelProperty(value = {"NLRA_RT4_SGL_EXB", "NLRA_房档4_单人_不含早"}, index = 70)
    @ApiModelProperty(value = "NLRA_房档4_单人_不含早")
    private BigDecimal nLraRt4SglExb;

    @ExcelProperty(value = {"NLRA_RT4_SGL_INB", "NLRA_房档4_单人_含早"}, index = 71)
    @ApiModelProperty(value = "NLRA_房档4_单人_含早")
    private BigDecimal nLraRt4SglInb;

    @ExcelProperty(value = {"NLRA_RT4_DBL_EXB", "NLRA_房档4_双人_不含早"}, index = 72)
    @ApiModelProperty(value = "NLRA_房档4_双人_不含早")
    private BigDecimal nLraRt4DblExb;

    @ExcelProperty(value = {"NLRA_RT4_DBL_INB", "NLRA_房档4_双人_含早"}, index = 73)
    @ApiModelProperty(value = "NLRA_房档4_双人_含早")
    private BigDecimal nLraRt4DblInb;

    @ExcelProperty(value = {"LRA_RT5_SGL_EXB", "LRA_房档5_单人_不含早"}, index = 74)
    @ApiModelProperty(value = "LRA_房档5_单人_不含早")
    private BigDecimal lraRt5SglExb;

    @ExcelProperty(value = {"LRA_RT5_SGL_INB", "LRA_房档5_单人_含早"}, index = 75)
    @ApiModelProperty(value = "LRA_房档5_单人_含早")
    private BigDecimal lraRt5SglInb;

    @ExcelProperty(value = {"LRA_RT5_DBL_EXB", "LRA_房档5_双人_不含早"}, index = 76)
    @ApiModelProperty(value = "LRA_房档5_双人_不含早")
    private BigDecimal lraRt5DblExb;

    @ExcelProperty(value = {"LRA_RT5_DBL_INB", "LRA_房档5_双人_含早"}, index = 77)
    @ApiModelProperty(value = "LRA_房档5_双人_含早")
    private BigDecimal lraRt5DblInb;

    @ExcelProperty(value = {"NLRA_RT5_SGL_EXB", "NLRA_房档5_单人_不含早"}, index = 78)
    @ApiModelProperty(value = "NLRA_房档5_单人_不含早")
    private BigDecimal nLraRt5SglExb;

    @ExcelProperty(value = {"NLRA_RT5_SGL_INB", "NLRA_房档5_单人_含早"}, index = 79)
    @ApiModelProperty(value = "NLRA_房档5_单人_含早")
    private BigDecimal nLraRt5SglInb;

    @ExcelProperty(value = {"NLRA_RT5_DBL_EXB", "NLRA_房档5_双人_不含早"}, index = 80)
    @ApiModelProperty(value = "NLRA_房档5_双人_不含早")
    private BigDecimal nLraRt5DblExb;

    @ExcelProperty(value = {"NLRA_RT5_DBL_INB", "NLRA_房档5_双人_含早"}, index = 81)
    @ApiModelProperty(value = "NLRA_房档5_双人_含早")
    private BigDecimal nLraRt5DblInb;

    @ExcelProperty(value = {"SEASON1START-END", "淡旺季1开始时间-结束时间 （多段用英文逗号隔开）"}, index = 82)
    @ApiModelProperty(value = "淡旺季1开始时间-结束时间 （多段用英文逗号隔开）")
    private String season1StartEnd;

    @ExcelProperty(value = {"LRA_RT1_SGL_EXB", "LRA_房档1_单人_不含早"}, index = 83)
    @ApiModelProperty(value = "LRA_房档1_单人_不含早")
    private BigDecimal season1LraRt1SglExb;

    @ExcelProperty(value = {"LRA_RT1_SGL_INB", "LRA_房档1_单人_含早"}, index = 84)
    @ApiModelProperty(value = "LRA_房档1_单人_含早")
    private BigDecimal season1LraRt1SglInb;

    @ExcelProperty(value = {"LRA_RT1_DBL_EXB", "LRA_房档1_双人_不含早"}, index = 85)
    @ApiModelProperty(value = "LRA_房档1_双人_不含早")
    private BigDecimal season1LraRt1DblExb;

    @ExcelProperty(value = {"LRA_RT1_DBL_INB", "LRA_房档1_双人_含早"}, index = 86)
    @ApiModelProperty(value = "LRA_房档1_双人_含早")
    private BigDecimal season1LraRt1DblInb;

    @ExcelProperty(value = {"NLRA_RT1_SGL_EXB", "NLRA_房档1_单人_不含早"}, index = 87)
    @ApiModelProperty(value = "NLRA_房档1_单人_不含早")
    private BigDecimal season1NLraRt1SglExb;

    @ExcelProperty(value = {"NLRA_RT1_SGL_INB", "NLRA_房档1_单人_含早"}, index = 88)
    @ApiModelProperty(value = "NLRA_房档1_单人_含早")
    private BigDecimal season1NLraRt1SglInb;

    @ExcelProperty(value = {"NLRA_RT1_DBL_EXB", "NLRA_房档1_双人_不含早"}, index = 89)
    @ApiModelProperty(value = "NLRA_房档1_双人_不含早")
    private BigDecimal season1NLraRt1DblExb;

    @ExcelProperty(value = {"NLRA_RT1_DBL_INB", "NLRA_房档1_双人_含早"}, index = 90)
    @ApiModelProperty(value = "NLRA_房档1_双人_含早")
    private BigDecimal season1NLraRt1DblInb;

    @ExcelProperty(value = {"LRA_RT2_SGL_EXB", "LRA_房档2_单人_不含早"}, index = 91)
    @ApiModelProperty(value = "LRA_房档2_单人_不含早")
    private BigDecimal season1LraRt2SglExb;

    @ExcelProperty(value = {"LRA_RT2_SGL_INB", "LRA_房档2_单人_含早"}, index = 92)
    @ApiModelProperty(value = "LRA_房档2_单人_含早")
    private BigDecimal season1LraRt2SglInb;

    @ExcelProperty(value = {"LRA_RT2_DBL_EXB", "LRA_房档2_双人_不含早"}, index = 93)
    @ApiModelProperty(value = "LRA_房档2_双人_不含早")
    private BigDecimal season1LraRt2DblExb;

    @ExcelProperty(value = {"LRA_RT2_DBL_INB", "LRA_房档2_双人_含早"}, index = 94)
    @ApiModelProperty(value = "LRA_房档2_双人_含早")
    private BigDecimal season1LraRt2DblInb;

    @ExcelProperty(value = {"NLRA_RT2_SGL_EXB", "NLRA_房档2_单人_不含早"}, index = 95)
    @ApiModelProperty(value = "NLRA_房档2_单人_不含早")
    private BigDecimal season1NLraRt2SglExb;

    @ExcelProperty(value = {"NLRA_RT2_SGL_INB", "NLRA_房档2_单人_含早"}, index = 96)
    @ApiModelProperty(value = "NLRA_房档2_单人_含早")
    private BigDecimal season1NLraRt2SglInb;

    @ExcelProperty(value = {"NLRA_RT2_DBL_EXB", "NLRA_房档2_双人_不含早"}, index = 97)
    @ApiModelProperty(value = "NLRA_房档2_双人_不含早")
    private BigDecimal season1NLraRt2DblExb;

    @ExcelProperty(value = {"NLRA_RT2_DBL_INB", "NLRA_房档2_双人_含早"}, index = 98)
    @ApiModelProperty(value = "NLRA_房档2_双人_含早")
    private BigDecimal season1NLraRt2DblInb;

    @ExcelProperty(value = {"LRA_RT3_SGL_EXB", "LRA_房档3_单人_不含早"}, index = 99)
    @ApiModelProperty(value = "LRA_房档3_单人_不含早")
    private BigDecimal season1LraRt3SglExb;

    @ExcelProperty(value = {"LRA_RT3_SGL_INB", "LRA_房档3_单人_含早"}, index = 100)
    @ApiModelProperty(value = "LRA_房档3_单人_含早")
    private BigDecimal season1LraRt3SglInb;

    @ExcelProperty(value = {"LRA_RT3_DBL_EXB", "LRA_房档3_双人_不含早"}, index = 101)
    @ApiModelProperty(value = "LRA_房档3_双人_不含早")
    private BigDecimal season1LraRt3DblExb;

    @ExcelProperty(value = {"LRA_RT3_DBL_INB", "LRA_房档3_双人_含早"}, index = 102)
    @ApiModelProperty(value = "LRA_房档3_双人_含早")
    private BigDecimal season1LraRt3DblInb;

    @ExcelProperty(value = {"NLRA_RT3_SGL_EXB", "NLRA_房档3_单人_不含早"}, index = 103)
    @ApiModelProperty(value = "NLRA_房档3_单人_不含早")
    private BigDecimal season1NLraRt3SglExb;

    @ExcelProperty(value = {"NLRA_RT3_SGL_INB", "NLRA_房档3_单人_含早"}, index = 104)
    @ApiModelProperty(value = "NLRA_房档3_单人_含早")
    private BigDecimal season1NLraRt3SglInb;

    @ExcelProperty(value = {"NLRA_RT3_DBL_EXB", "NLRA_房档3_双人_不含早"}, index = 105)
    @ApiModelProperty(value = "NLRA_房档3_双人_不含早")
    private BigDecimal season1NLraRt3DblExb;

    @ExcelProperty(value = {"NLRA_RT3_DBL_INB", "NLRA_房档3_双人_含早"}, index = 106)
    @ApiModelProperty(value = "NLRA_房档3_双人_含早")
    private BigDecimal season1NLraRt3DblInb;

    @ExcelProperty(value = {"LRA_RT4_SGL_EXB", "LRA_房档4_单人_不含早"}, index = 107)
    @ApiModelProperty(value = "LRA_房档4_单人_不含早")
    private BigDecimal season1LraRt4SglExb;

    @ExcelProperty(value = {"LRA_RT4_SGL_INB", "LRA_房档4_单人_含早"}, index = 108)
    @ApiModelProperty(value = "LRA_房档4_单人_含早")
    private BigDecimal season1LraRt4SglInb;

    @ExcelProperty(value = {"LRA_RT4_DBL_EXB", "LRA_房档4_双人_不含早"}, index = 109)
    @ApiModelProperty(value = "LRA_房档4_双人_不含早")
    private BigDecimal season1LraRt4DblExb;

    @ExcelProperty(value = {"LRA_RT4_DBL_INB", "LRA_房档4_双人_含早"}, index = 110)
    @ApiModelProperty(value = "LRA_房档4_双人_含早")
    private BigDecimal season1LraRt4DblInb;

    @ExcelProperty(value = {"NLRA_RT4_SGL_EXB", "NLRA_房档4_单人_不含早"}, index = 111)
    @ApiModelProperty(value = "NLRA_房档4_单人_不含早")
    private BigDecimal season1NLraRt4SglExb;

    @ExcelProperty(value = {"NLRA_RT4_SGL_INB", "NLRA_房档4_单人_含早"}, index = 112)
    @ApiModelProperty(value = "NLRA_房档4_单人_含早")
    private BigDecimal season1NLraRt4SglInb;

    @ExcelProperty(value = {"NLRA_RT4_DBL_EXB", "NLRA_房档4_双人_不含早"}, index = 113)
    @ApiModelProperty(value = "NLRA_房档4_双人_不含早")
    private BigDecimal season1NLraRt4DblExb;

    @ExcelProperty(value = {"NLRA_RT4_DBL_INB", "NLRA_房档4_双人_含早"}, index = 114)
    @ApiModelProperty(value = "NLRA_房档4_双人_含早")
    private BigDecimal season1NLraRt4DblInb;

    @ExcelProperty(value = {"LRA_RT5_SGL_EXB", "LRA_房档5_单人_不含早"}, index = 115)
    @ApiModelProperty(value = "LRA_房档5_单人_不含早")
    private BigDecimal season1LraRt5SglExb;

    @ExcelProperty(value = {"LRA_RT5_SGL_INB", "LRA_房档5_单人_含早"}, index = 116)
    @ApiModelProperty(value = "LRA_房档5_单人_含早")
    private BigDecimal season1LraRt5SglInb;

    @ExcelProperty(value = {"LRA_RT5_DBL_EXB", "LRA_房档5_双人_不含早"}, index = 117)
    @ApiModelProperty(value = "LRA_房档5_双人_不含早")
    private BigDecimal season1LraRt5DblExb;

    @ExcelProperty(value = {"LRA_RT5_DBL_INB", "LRA_房档5_双人_含早"}, index = 118)
    @ApiModelProperty(value = "LRA_房档5_双人_含早")
    private BigDecimal season1LraRt5DblInb;

    @ExcelProperty(value = {"NLRA_RT5_SGL_EXB", "NLRA_房档5_单人_不含早"}, index = 119)
    @ApiModelProperty(value = "NLRA_房档5_单人_不含早")
    private BigDecimal season1NLraRt5SglExb;

    @ExcelProperty(value = {"NLRA_RT5_SGL_INB", "NLRA_房档5_单人_含早"}, index = 120)
    @ApiModelProperty(value = "NLRA_房档5_单人_含早")
    private BigDecimal season1NLraRt5SglInb;

    @ExcelProperty(value = {"NLRA_RT5_DBL_EXB", "NLRA_房档5_双人_不含早"}, index = 121)
    @ApiModelProperty(value = "NLRA_房档5_双人_不含早")
    private BigDecimal season1NLraRt5DblExb;

    @ExcelProperty(value = {"NLRA_RT5_DBL_INB", "NLRA_房档5_双人_含早"}, index = 122)
    @ApiModelProperty(value = "NLRA_房档5_双人_含早")
    private BigDecimal season1NLraRt5DblInb;
    
    /**
     * 淡旺季2开始时间-结束时间 （多段用英文逗号隔开）
     */
    @ExcelProperty(value = {"SEASON2START-END", "淡旺季2开始时间-结束时间 （多段用英文逗号隔开）"}, index = 123)
    @ApiModelProperty(value = "淡旺季2开始时间-结束时间 （多段用英文逗号隔开）")
    private String season2StartEnd;

    @ExcelProperty(value = {"LRA_RT1_SGL_EXB", "LRA_房档1_单人_不含早"}, index = 124)
    @ApiModelProperty(value = "LRA_房档1_单人_不含早")
    private BigDecimal season2LraRt1SglExb;

    @ExcelProperty(value = {"LRA_RT1_SGL_INB", "LRA_房档1_单人_含早"}, index = 125)
    @ApiModelProperty(value = "LRA_房档1_单人_含早")
    private BigDecimal season2LraRt1SglInb;

    @ExcelProperty(value = {"LRA_RT1_DBL_EXB", "LRA_房档1_双人_不含早"}, index = 126)
    @ApiModelProperty(value = "LRA_房档1_双人_不含早")
    private BigDecimal season2LraRt1DblExb;

    @ExcelProperty(value = {"LRA_RT1_DBL_INB", "LRA_房档1_双人_含早"}, index = 127)
    @ApiModelProperty(value = "LRA_房档1_双人_含早")
    private BigDecimal season2LraRt1DblInb;

    @ExcelProperty(value = {"NLRA_RT1_SGL_EXB", "NLRA_房档1_单人_不含早"}, index = 128)
    @ApiModelProperty(value = "NLRA_房档1_单人_不含早")
    private BigDecimal season2NLraRt1SglExb;

    @ExcelProperty(value = {"NLRA_RT1_SGL_INB", "NLRA_房档1_单人_含早"}, index = 129)
    @ApiModelProperty(value = "NLRA_房档1_单人_含早")
    private BigDecimal season2NLraRt1SglInb;

    @ExcelProperty(value = {"NLRA_RT1_DBL_EXB", "NLRA_房档1_双人_不含早"}, index = 130)
    @ApiModelProperty(value = "NLRA_房档1_双人_不含早")
    private BigDecimal season2NLraRt1DblExb;

    @ExcelProperty(value = {"NLRA_RT1_DBL_INB", "NLRA_房档1_双人_含早"}, index = 131)
    @ApiModelProperty(value = "NLRA_房档1_双人_含早")
    private BigDecimal season2NLraRt1DblInb;

    @ExcelProperty(value = {"LRA_RT2_SGL_EXB", "LRA_房档2_单人_不含早"}, index = 132)
    @ApiModelProperty(value = "LRA_房档2_单人_不含早")
    private BigDecimal season2LraRt2SglExb;

    @ExcelProperty(value = {"LRA_RT2_SGL_INB", "LRA_房档2_单人_含早"}, index = 133)
    @ApiModelProperty(value = "LRA_房档2_单人_含早")
    private BigDecimal season2LraRt2SglInb;

    @ExcelProperty(value = {"LRA_RT2_DBL_EXB", "LRA_房档2_双人_不含早"}, index = 134)
    @ApiModelProperty(value = "LRA_房档2_双人_不含早")
    private BigDecimal season2LraRt2DblExb;

    @ExcelProperty(value = {"LRA_RT2_DBL_INB", "LRA_房档2_双人_含早"}, index = 135)
    @ApiModelProperty(value = "LRA_房档2_双人_含早")
    private BigDecimal season2LraRt2DblInb;

    @ExcelProperty(value = {"NLRA_RT2_SGL_EXB", "NLRA_房档2_单人_不含早"}, index = 136)
    @ApiModelProperty(value = "NLRA_房档2_单人_不含早")
    private BigDecimal season2NLraRt2SglExb;

    @ExcelProperty(value = {"NLRA_RT2_SGL_INB", "NLRA_房档2_单人_含早"}, index = 137)
    @ApiModelProperty(value = "NLRA_房档2_单人_含早")
    private BigDecimal season2NLraRt2SglInb;

    @ExcelProperty(value = {"NLRA_RT2_DBL_EXB", "NLRA_房档2_双人_不含早"}, index = 138)
    @ApiModelProperty(value = "NLRA_房档2_双人_不含早")
    private BigDecimal season2NLraRt2DblExb;

    @ExcelProperty(value = {"NLRA_RT2_DBL_INB", "NLRA_房档2_双人_含早"}, index = 139)
    @ApiModelProperty(value = "NLRA_房档2_双人_含早")
    private BigDecimal season2NLraRt2DblInb;

    @ExcelProperty(value = {"LRA_RT3_SGL_EXB", "LRA_房档3_单人_不含早"}, index = 140)
    @ApiModelProperty(value = "LRA_房档3_单人_不含早")
    private BigDecimal season2LraRt3SglExb;

    @ExcelProperty(value = {"LRA_RT3_SGL_INB", "LRA_房档3_单人_含早"}, index = 141)
    @ApiModelProperty(value = "LRA_房档3_单人_含早")
    private BigDecimal season2LraRt3SglInb;

    @ExcelProperty(value = {"LRA_RT3_DBL_EXB", "LRA_房档3_双人_不含早"}, index = 142)
    @ApiModelProperty(value = "LRA_房档3_双人_不含早")
    private BigDecimal season2LraRt3DblExb;

    @ExcelProperty(value = {"LRA_RT3_DBL_INB", "LRA_房档3_双人_含早"}, index = 143)
    @ApiModelProperty(value = "LRA_房档3_双人_含早")
    private BigDecimal season2LraRt3DblInb;

    @ExcelProperty(value = {"NLRA_RT3_SGL_EXB", "NLRA_房档3_单人_不含早"}, index = 144)
    @ApiModelProperty(value = "NLRA_房档3_单人_不含早")
    private BigDecimal season2NLraRt3SglExb;

    @ExcelProperty(value = {"NLRA_RT3_SGL_INB", "NLRA_房档3_单人_含早"}, index = 145)
    @ApiModelProperty(value = "NLRA_房档3_单人_含早")
    private BigDecimal season2NLraRt3SglInb;

    @ExcelProperty(value = {"NLRA_RT3_DBL_EXB", "NLRA_房档3_双人_不含早"}, index = 146)
    @ApiModelProperty(value = "NLRA_房档3_双人_不含早")
    private BigDecimal season2NLraRt3DblExb;

    @ExcelProperty(value = {"NLRA_RT3_DBL_INB", "NLRA_房档3_双人_含早"}, index = 147)
    @ApiModelProperty(value = "NLRA_房档3_双人_含早")
    private BigDecimal season2NLraRt3DblInb;

    @ExcelProperty(value = {"LRA_RT4_SGL_EXB", "LRA_房档4_单人_不含早"}, index = 148)
    @ApiModelProperty(value = "LRA_房档4_单人_不含早")
    private BigDecimal season2LraRt4SglExb;

    @ExcelProperty(value = {"LRA_RT4_SGL_INB", "LRA_房档4_单人_含早"}, index = 149)
    @ApiModelProperty(value = "LRA_房档4_单人_含早")
    private BigDecimal season2LraRt4SglInb;

    @ExcelProperty(value = {"LRA_RT4_DBL_EXB", "LRA_房档4_双人_不含早"}, index = 150)
    @ApiModelProperty(value = "LRA_房档4_双人_不含早")
    private BigDecimal season2LraRt4DblExb;

    @ExcelProperty(value = {"LRA_RT4_DBL_INB", "LRA_房档4_双人_含早"}, index = 151)
    @ApiModelProperty(value = "LRA_房档4_双人_含早")
    private BigDecimal season2LraRt4DblInb;

    @ExcelProperty(value = {"NLRA_RT4_SGL_EXB", "NLRA_房档4_单人_不含早"}, index = 152)
    @ApiModelProperty(value = "NLRA_房档4_单人_不含早")
    private BigDecimal season2NLraRt4SglExb;

    @ExcelProperty(value = {"NLRA_RT4_SGL_INB", "NLRA_房档4_单人_含早"}, index = 153)
    @ApiModelProperty(value = "NLRA_房档4_单人_含早")
    private BigDecimal season2NLraRt4SglInb;

    @ExcelProperty(value = {"NLRA_RT4_DBL_EXB", "NLRA_房档4_双人_不含早"}, index = 154)
    @ApiModelProperty(value = "NLRA_房档4_双人_不含早")
    private BigDecimal season2NLraRt4DblExb;

    @ExcelProperty(value = {"NLRA_RT4_DBL_INB", "NLRA_房档4_双人_含早"}, index = 155)
    @ApiModelProperty(value = "NLRA_房档4_双人_含早")
    private BigDecimal season2NLraRt4DblInb;

    @ExcelProperty(value = {"LRA_RT5_SGL_EXB", "LRA_房档5_单人_不含早"}, index = 156)
    @ApiModelProperty(value = "LRA_房档5_单人_不含早")
    private BigDecimal season2LraRt5SglExb;

    @ExcelProperty(value = {"LRA_RT5_SGL_INB", "LRA_房档5_单人_含早"}, index = 157)
    @ApiModelProperty(value = "LRA_房档5_单人_含早")
    private BigDecimal season2LraRt5SglInb;

    @ExcelProperty(value = {"LRA_RT5_DBL_EXB", "LRA_房档5_双人_不含早"}, index = 158)
    @ApiModelProperty(value = "LRA_房档5_双人_不含早")
    private BigDecimal season2LraRt5DblExb;

    @ExcelProperty(value = {"LRA_RT5_DBL_INB", "LRA_房档5_双人_含早"}, index = 159)
    @ApiModelProperty(value = "LRA_房档5_双人_含早")
    private BigDecimal season2LraRt5DblInb;

    @ExcelProperty(value = {"NLRA_RT5_SGL_EXB", "NLRA_房档5_单人_不含早"}, index = 160)
    @ApiModelProperty(value = "NLRA_房档5_单人_不含早")
    private BigDecimal season2NLraRt5SglExb;

    @ExcelProperty(value = {"NLRA_RT5_SGL_INB", "NLRA_房档5_单人_含早"}, index = 161)
    @ApiModelProperty(value = "NLRA_房档5_单人_含早")
    private BigDecimal season2NLraRt5SglInb;

    @ExcelProperty(value = {"NLRA_RT5_DBL_EXB", "NLRA_房档5_双人_不含早"}, index = 162)
    @ApiModelProperty(value = "NLRA_房档5_双人_不含早")
    private BigDecimal season2NLraRt5DblExb;

    @ExcelProperty(value = {"NLRA_RT5_DBL_INB", "NLRA_房档5_双人_含早"}, index = 163)
    @ApiModelProperty(value = "NLRA_房档5_双人_含早")
    private BigDecimal season2NLraRt5DblInb;

    @ExcelProperty(value = {"CANC_POL", "取消政策 (当天以前免费取消用小时数表示 48H)"}, index = 75)
    @ApiModelProperty(value = "取消政策 (当天以前免费取消用小时数表示 48H)")
    private String cancelPolicy;

    @ExcelProperty(value = {"EARLYCK_FEE", "提前入住费用"}, index = 76)
    @ApiModelProperty(value = "提前入住费用")
    private BigDecimal earlyCheckFee;

    @ExcelProperty(value = {"EARLYCK_UOM", "提前入住费用收取方式（百分比或固定费用）N 不收 F 固定值 P 百分比"}, index = 77)
    @ApiModelProperty(value = "提前入住费用收取方式（百分比或固定费用）N 不收 F 固定值 P 百分比")
    private String earlyCheckFeeUom;

    @ExcelProperty(value = {"EARLYCK_INCLUDE", "报价是否包含提前入住税费"}, index = 78)
    @ApiModelProperty(value = "报价是否包含提前入住税费")
    private String earlyCheckInclude;

    @ExcelProperty(value = {"LODGTX_FEE", "入住税费用"}, index = 79)
    @ApiModelProperty(value = "入住税费用")
    private BigDecimal lodgtxFee;

    @ExcelProperty(value = {"LODGTX_UOM", "入住税收费方式"}, index = 80)
    @ApiModelProperty(value = "入住税收费方式")
    private String lodgtxUom;

    @ExcelProperty(value = {"LODGTX_INCLUDE", "是否含入住税"}, index = 81)
    @ApiModelProperty(value = "是否含入住税")
    private String lodgtxInclude;

    @ExcelProperty(value = {"STATETX_FEE", "州税费用"}, index = 82)
    @ApiModelProperty(value = "州税费用")
    private BigDecimal statetxFee;

    /**
     * 州税收费方式
     */
    @ExcelProperty(value = {"STATETX_UOM", "州税收费方式"}, index = 83)
    @ApiModelProperty(value = "州税收费方式")
    private String statetxUom;

    /**
     * 是否包含州税
     */
    @ExcelProperty(value = {"STATETX_INCLUDE", "是否含州税"}, index = 84)
    @ApiModelProperty(value = "是否含州税")
    private String statetxInclude;

    /**
     * 市税费用
     */
    @ExcelProperty(value = {"CITYTX_FEE", "城市税费用"}, index = 85)
    @ApiModelProperty(value = "城市税费用")
    private BigDecimal citytxFee;

    /**
     * 市税收费方式
     */
    @ExcelProperty(value = {"CITYTX_UOM", "城市税收费方式"}, index = 86)
    @ApiModelProperty(value = "城市税收费方式")
    private String citytxUom;

    /**
     * 是否包含市税
     */
    @ExcelProperty(value = {"CITYTX_INCLUDE", "是否含城市税"}, index = 87)
    @ApiModelProperty(value = "是否含城市税")
    private String citytxInclude;

    /**
     * 客房增值税费用
     */
    @ExcelProperty(value = {"VATGSTRM_FEE", "客房增值税费用"}, index = 88)
    @ApiModelProperty(value = "客房增值税费用")
    private BigDecimal vatgstrmFee;

    /**
     * 客房增值税收费方式
     */
    @ExcelProperty(value = {"VATGSTRM_UOM", "客房增值税收费方式"}, index = 89)
    @ApiModelProperty(value = "客房增值税收费方式")
    private String vatgstrmUom;

    /**
     * 是否含客房增值税
     */
    @ExcelProperty(value = {"VATGSTRM_INCLUDE", "是否含客房增值税"}, index = 90)
    @ApiModelProperty(value = "是否含客房增值税")
    private String vatgstrmInclude;

    /**
     * 餐饮增值税费用
     */
    @ExcelProperty(value = {"VATGSTFB_FEE", "餐饮增值税费用"}, index = 91)
    @ApiModelProperty(value = "餐饮增值税费用")
    private BigDecimal vatgstfbFee;

    /**
     * 餐饮增值税收费方式
     */
    @ExcelProperty(value = {"VATGSTFB_UOM", "餐饮增值税收费方式"}, index = 92)
    @ApiModelProperty(value = "餐饮增值税收费方式")
    private String vatgstfbUom;

    /**
     * 是否含餐饮增值税
     */
    @ExcelProperty(value = {"VATGSTFB_INCLUDE", "是否含餐饮增值税"}, index = 93)
    @ApiModelProperty(value = "是否含餐饮增值税")
    private String vatgstfbInclude;

    /**
     * 服务费
     */
    @ExcelProperty(value = {"SERVICE_FEE", "服务费"}, index = 94)
    @ApiModelProperty(value = "服务费")
    private BigDecimal serviceFee;

    /**
     * 服务费收费方式
     */
    @ExcelProperty(value = {"SERVICE_UOM", "服务费收费方式"}, index = 95)
    @ApiModelProperty(value = "服务费收费方式")
    private String serviceUom;

    /**
     * 是否含服务费
     */
    @ExcelProperty(value = {"SERVICE_INCLUDE", "是否含服务费"}, index = 96)
    @ApiModelProperty(value = "是否含服务费")
    private String serviceInclude;

    /**
     * 占用费
     */
    @ExcelProperty(value = {"OCC_FEE", "占用费"}, index = 97)
    @ApiModelProperty(value = "占用费")
    private BigDecimal occFee;

    /**
     * 占用费收费方式
     */
    @ExcelProperty(value = {"OCC_UOM", "占用费收费方式"}, index = 98)
    @ApiModelProperty(value = "占用费收费方式")
    private String occUom;

    /**
     * 是否含占用费
     */
    @ExcelProperty(value = {"OCC_INCLUDE", "是否含占用费"}, index = 99)
    @ApiModelProperty(value = "是否含占用费")
    private String occInclude;

    /**
     * 其他税费1
     */
    @ExcelProperty(value = {"OTHERTX_FEE 1", "其他税费1"}, index = 100)
    @ApiModelProperty(value = "其他税费1")
    private BigDecimal otherTxFee1;

    /**
     * 其他税费1收费方式
     */
    @ExcelProperty(value = {"OTHERTX_FEE 1_UOM", "其他税费1收费方式"}, index = 101)
    @ApiModelProperty(value = "其他税费1收费方式")
    private String otherTxFee1Uom;

    /**
     * 其他税费1描述
     */
    @ExcelProperty(value = {"OTHERTX_FEE 1_DESC", "是否其他税费1描述"}, index = 102)
    @ApiModelProperty(value = "是否其他税费1描述")
    private String otherTxFee1Desc;

    /**
     * 其他税费1是否包含
     */
    @ExcelProperty(value = {"OTHERTX_FEE 1_INCL", "是否含其他税费1"}, index = 103)
    @ApiModelProperty(value = "是否含其他税费1")
    private String otherTxFee1Incl;

    /**
     * 其他税费2
     */
    @ExcelProperty(value = {"OTHERTX_FEE 2", "其他税费2"}, index = 104)
    @ApiModelProperty(value = "其他税费2")
    private BigDecimal otherTxFee2;

    /**
     * 其他税费2收费方式
     */
    @ExcelProperty(value = {"OTHERTX_FEE 2_UOM", "其他税费2收费方式"}, index = 105)
    @ApiModelProperty(value = "其他税费2收费方式")
    private String otherTxFee2Uom;

    /**
     * 其他税费2描述
     */
    @ExcelProperty(value = {"OTHERTX_FEE 2_DESC", "是否其他税费2描述"}, index = 106)
    @ApiModelProperty(value = "是否其他税费2描述")
    private String otherTxFee2Desc;

    /**
     * 其他税费2是否包含
     */
    @ExcelProperty(value = {"OTHERTX_FEE 2_INCL", "是否含其他税费2"}, index = 107)
    @ApiModelProperty(value = "是否含其他税费2")
    private String otherTxFee2Incl;

    /**
     * 其他税费3
     */
    @ExcelProperty(value = {"OTHERTX_FEE 3", "其他税费3"}, index = 108)
    @ApiModelProperty(value = "其他税费3")
    private BigDecimal otherTxFee3;

    /**
     * 其他税费3收费方式
     */
    @ExcelProperty(value = {"OTHERTX_FEE 3_UOM", "其他税费3收费方式"}, index = 109)
    @ApiModelProperty(value = "其他税费3收费方式")
    private String otherTxFee3Uom;

    /**
     * 其他税费3描述
     */
    @ExcelProperty(value = {"OTHERTX_FEE 3_DESC", "是否其他税费3描述"}, index = 110)
    @ApiModelProperty(value = "是否其他税费3描述")
    private String otherTxFee3Desc;

    /**
     * 其他税费3是否包含
     */
    @ExcelProperty(value = {"OTHERTX_FEE 3_INCL", "是否含其他税费3"}, index = 111)
    @ApiModelProperty(value = "是否含其他税费3")
    private String otherTxFee3Incl;

    /**
     * 是否包含停车费
     */
    @ExcelProperty(value = {"PARK_INCLUDE", "免费停车"}, index = 112)
    @ApiModelProperty(value = "免费停车")
    private String parkInclude;

    /**
     * 早餐费用
     */
    @ExcelProperty(value = {"BREAK_FEE", "早餐费用"}, index = 114)
    @ApiModelProperty(value = "早餐费用")
    private BigDecimal breakFee;

    /**
     * 早餐类型
     */
    @ExcelProperty(value = {"BREAK_TYPE", "早餐类型"}, index = 115)
    @ApiModelProperty(value = "早餐类型")
    private String breakType;

    /**
     * 健身中心
     */
    @ExcelProperty(value = {"FITON_CENT", "健身房"}, index = 116)
    @ApiModelProperty(value = "健身房")
    private String fitonCent;

    /**
     * 是否包含无线网络
     */
    @ExcelProperty(value = {"WIRELESS_INCLUDE", "含无线网络"}, index = 117)
    @ApiModelProperty(value = "含无线网络")
    private String wirelessInclude;

    /**
     * 费率替代
     */
    @ExcelProperty(value = {"RATE_SUBS", "协议价格是否适用于分子公司员工"}, index = 118)
    @ApiModelProperty(value = "协议价格是否适用于分子公司员工")
    private String rateSubs;

    /**
     * 费率个人
     */
    @ExcelProperty(value = {"RATE_PERSO", "协议价格是否适用于员工因私出行"}, index = 119)
    @ApiModelProperty(value = "协议价格是否适用于员工因私出行")
    private String ratePerso;

    // 自定义问题
    @ExcelProperty(value = {"USERDEFINED1", "客户定制内容1"}, index = 120)
    @ApiModelProperty(value = "客户定制内容1")
    private String supportCustomBidStrategy1;

    @ExcelProperty(value = {"USERDEFINED2", "客户定制内容2"}, index = 121)
    @ApiModelProperty(value = "客户定制内容2")
    private String supportCustomBidStrategy2;

    @ExcelProperty(value = {"USERDEFINED3", "客户定制内容3"}, index = 122)
    @ApiModelProperty(value = "客户定制内容3")
    private String supportCustomBidStrategy3;

    @ExcelProperty(value = {"USERDEFINED4", "客户定制内容4"}, index = 123)
    @ApiModelProperty(value = "客户定制内容4")
    private String supportCustomBidStrategy4;

    @ExcelProperty(value = {"USERDEFINED5", "客户定制内容5"}, index = 124)
    @ApiModelProperty(value = "客户定制内容5")
    private String supportCustomBidStrategy5;

    @ExcelProperty(value = {"USERDEFINED6", "客户定制内容6"}, index = 125)
    @ApiModelProperty(value = "客户定制内容6")
    private String supportCustomBidStrategy6;

    @ExcelProperty(value = {"USERDEFINED7", "客户定制内容7"}, index = 126)
    @ApiModelProperty(value = "客户定制内容7")
    private String supportCustomBidStrategy7;

    @ExcelProperty(value = {"USERDEFINED8", "客户定制内容8"}, index = 127)
    @ApiModelProperty(value = "客户定制内容8")
    private String supportCustomBidStrategy8;

    @ExcelProperty(value = {"USERDEFINED9", "客户定制内容9"}, index = 128)
    @ApiModelProperty(value = "客户定制内容9")
    private String supportCustomBidStrategy9;

    @ExcelProperty(value = {"USERDEFINED10", "客户定制内容10"}, index = 129)
    @ApiModelProperty(value = "客户定制内容10")
    private String supportCustomBidStrategy10;

    @ExcelProperty(value = {"USERDEFINED11", "客户定制内容11"}, index = 130)
    @ApiModelProperty(value = "客户定制内容11")
    private String supportCustomBidStrategy11;

    @ExcelProperty(value = {"USERDEFINED12", "客户定制内容12"}, index = 131)
    @ApiModelProperty(value = "客户定制内容12")
    private String supportCustomBidStrategy12;

    @ExcelProperty(value = {"USERDEFINED13", "客户定制内容13"}, index = 132)
    @ApiModelProperty(value = "客户定制内容13")
    private String supportCustomBidStrategy13;

    @ExcelProperty(value = {"USERDEFINED14", "客户定制内容14"}, index = 133)
    @ApiModelProperty(value = "客户定制内容14")
    private String supportCustomBidStrategy14;

    @ExcelProperty(value = {"USERDEFINED15", "客户定制内容15"}, index = 134)
    @ApiModelProperty(value = "客户定制内容15")
    private String supportCustomBidStrategy15;

    @ExcelProperty(value = {"USERDEFINED16", "客户定制内容16"}, index = 135)
    @ApiModelProperty(value = "客户定制内容16")
    private String supportCustomBidStrategy16;

    @ExcelProperty(value = {"USERDEFINED17", "客户定制内容17"}, index = 136)
    @ApiModelProperty(value = "客户定制内容17")
    private String supportCustomBidStrategy17;

    @ExcelProperty(value = {"USERDEFINED18", "客户定制内容18"}, index = 137)
    @ApiModelProperty(value = "客户定制内容18")
    private String supportCustomBidStrategy18;

    @ExcelProperty(value = {"USERDEFINED19", "客户定制内容19"}, index = 138)
    @ApiModelProperty(value = "客户定制内容19")
    private String supportCustomBidStrategy19;

    @ExcelProperty(value = {"USERDEFINED20", "客户定制内容20"}, index = 139)
    @ApiModelProperty(value = "客户定制内容20")
    private String supportCustomBidStrategy20;

    @ExcelProperty(value = {"USERDEFINED21", "客户定制内容21"}, index = 140)
    @ApiModelProperty(value = "客户定制内容21")
    private String supportCustomBidStrategy21;

    @ExcelProperty(value = {"USERDEFINED22", "客户定制内容22"}, index = 141)
    @ApiModelProperty(value = "客户定制内容22")
    private String supportCustomBidStrategy22;

    @ExcelProperty(value = {"USERDEFINED23", "客户定制内容23"}, index = 142)
    @ApiModelProperty(value = "客户定制内容23")
    private String supportCustomBidStrategy23;

    @ExcelProperty(value = {"USERDEFINED24", "客户定制内容24"}, index = 143)
    @ApiModelProperty(value = "客户定制内容24")
    private String supportCustomBidStrategy24;

    @ExcelProperty(value = {"USERDEFINED25", "客户定制内容25"}, index = 144)
    @ApiModelProperty(value = "客户定制内容25")
    private String supportCustomBidStrategy25;

    @ExcelProperty(value = {"USERDEFINED26", "客户定制内容26"}, index = 145)
    @ApiModelProperty(value = "客户定制内容26")
    private String supportCustomBidStrategy26;

    @ExcelProperty(value = {"USERDEFINED27", "客户定制内容27"}, index = 146)
    @ApiModelProperty(value = "客户定制内容27")
    private String supportCustomBidStrategy27;

    @ExcelProperty(value = {"USERDEFINED28", "客户定制内容28"}, index = 147)
    @ApiModelProperty(value = "客户定制内容28")
    private String supportCustomBidStrategy28;

    @ExcelProperty(value = {"USERDEFINED29", "客户定制内容29"}, index = 148)
    @ApiModelProperty(value = "客户定制内容29")
    private String supportCustomBidStrategy29;

    @ExcelProperty(value = {"USERDEFINED30", "客户定制内容30"}, index = 149)
    @ApiModelProperty(value = "客户定制内容30")
    private String supportCustomBidStrategy30;

    @ExcelProperty(value = {"USERDEFINED31", "客户定制内容31"}, index = 150)
    @ApiModelProperty(value = "客户定制内容31")
    private String supportCustomBidStrategy31;

    @ExcelProperty(value = {"USERDEFINED32", "客户定制内容32"}, index = 151)
    @ApiModelProperty(value = "客户定制内容32")
    private String supportCustomBidStrategy32;

    @ExcelProperty(value = {"USERDEFINED33", "客户定制内容33"}, index = 152)
    @ApiModelProperty(value = "客户定制内容33")
    private String supportCustomBidStrategy33;

    @ExcelProperty(value = {"USERDEFINED34", "客户定制内容34"}, index = 153)
    @ApiModelProperty(value = "客户定制内容34")
    private String supportCustomBidStrategy34;

    @ExcelProperty(value = {"USERDEFINED35", "客户定制内容35"}, index = 154)
    @ApiModelProperty(value = "客户定制内容35")
    private String supportCustomBidStrategy35;

    @ExcelProperty(value = {"USERDEFINED36", "客户定制内容36"}, index = 155)
    @ApiModelProperty(value = "客户定制内容36")
    private String supportCustomBidStrategy36;

    @ExcelProperty(value = {"USERDEFINED37", "客户定制内容37"}, index = 156)
    @ApiModelProperty(value = "客户定制内容37")
    private String supportCustomBidStrategy37;

    @ExcelProperty(value = {"USERDEFINED38", "客户定制内容38"}, index = 157)
    @ApiModelProperty(value = "客户定制内容38")
    private String supportCustomBidStrategy38;

    @ExcelProperty(value = {"USERDEFINED39", "客户定制内容39"}, index = 158)
    @ApiModelProperty(value = "客户定制内容39")
    private String supportCustomBidStrategy39;

    @ExcelProperty(value = {"USERDEFINED40", "客户定制内容40"}, index = 159)
    @ApiModelProperty(value = "客户定制内容40")
    private String supportCustomBidStrategy40;

    @ExcelProperty(value = {"USERDEFINED41", "客户定制内容41"}, index = 160)
    @ApiModelProperty(value = "客户定制内容41")
    private String supportCustomBidStrategy41;

    @ExcelProperty(value = {"USERDEFINED42", "客户定制内容42"}, index = 161)
    @ApiModelProperty(value = "客户定制内容42")
    private String supportCustomBidStrategy42;

    @ExcelProperty(value = {"USERDEFINED43", "客户定制内容43"}, index = 162)
    @ApiModelProperty(value = "客户定制内容43")
    private String supportCustomBidStrategy43;

    @ExcelProperty(value = {"USERDEFINED44", "客户定制内容44"}, index = 163)
    @ApiModelProperty(value = "客户定制内容44")
    private String supportCustomBidStrategy44;

    @ExcelProperty(value = {"USERDEFINED45", "客户定制内容45"}, index = 164)
    @ApiModelProperty(value = "客户定制内容45")
    private String supportCustomBidStrategy45;

    @ExcelProperty(value = {"USERDEFINED46", "客户定制内容46"}, index = 165)
    @ApiModelProperty(value = "客户定制内容46")
    private String supportCustomBidStrategy46;

    @ExcelProperty(value = {"USERDEFINED47", "客户定制内容47"}, index = 166)
    @ApiModelProperty(value = "客户定制内容47")
    private String supportCustomBidStrategy47;

    @ExcelProperty(value = {"USERDEFINED48", "客户定制内容48"}, index = 167)
    @ApiModelProperty(value = "客户定制内容48")
    private String supportCustomBidStrategy48;

    @ExcelProperty(value = {"USERDEFINED49", "客户定制内容49"}, index = 168)
    @ApiModelProperty(value = "客户定制内容49")
    private String supportCustomBidStrategy49;

    @ExcelProperty(value = {"USERDEFINED50", "客户定制内容50"}, index = 169)
    @ApiModelProperty(value = "客户定制内容50")
    private String supportCustomBidStrategy50;

    @ExcelProperty(value = {"USERDEFINED51", "客户定制内容51"}, index = 170)
    @ApiModelProperty(value = "客户定制内容51")
    private String supportCustomBidStrategy51;

    @ExcelProperty(value = {"USERDEFINED52", "客户定制内容52"}, index = 171)
    @ApiModelProperty(value = "客户定制内容52")
    private String supportCustomBidStrategy52;

    @ExcelProperty(value = {"USERDEFINED53", "客户定制内容53"}, index = 172)
    @ApiModelProperty(value = "客户定制内容53")
    private String supportCustomBidStrategy53;

    @ExcelProperty(value = {"USERDEFINED54", "客户定制内容54"}, index = 173)
    @ApiModelProperty(value = "客户定制内容54")
    private String supportCustomBidStrategy54;

    @ExcelProperty(value = {"USERDEFINED55", "客户定制内容55"}, index = 174)
    @ApiModelProperty(value = "客户定制内容55")
    private String supportCustomBidStrategy55;

    @ExcelProperty(value = {"USERDEFINED56", "客户定制内容56"}, index = 175)
    @ApiModelProperty(value = "客户定制内容56")
    private String supportCustomBidStrategy56;

    @ExcelProperty(value = {"USERDEFINED57", "客户定制内容57"}, index = 176)
    @ApiModelProperty(value = "客户定制内容57")
    private String supportCustomBidStrategy57;

    @ExcelProperty(value = {"USERDEFINED58", "客户定制内容58"}, index = 177)
    @ApiModelProperty(value = "客户定制内容58")
    private String supportCustomBidStrategy58;

    @ExcelProperty(value = {"USERDEFINED59", "客户定制内容59"}, index = 178)
    @ApiModelProperty(value = "客户定制内容59")
    private String supportCustomBidStrategy59;

    @ExcelProperty(value = {"USERDEFINED60", "客户定制内容60"}, index = 179)
    @ApiModelProperty(value = "客户定制内容60")
    private String supportCustomBidStrategy60;

    @ExcelProperty(value = {"USERDEFINED61", "客户定制内容61"}, index = 180)
    @ApiModelProperty(value = "客户定制内容61")
    private String supportCustomBidStrategy61;

    @ExcelProperty(value = {"USERDEFINED62", "客户定制内容62"}, index = 181)
    @ApiModelProperty(value = "客户定制内容62")
    private String supportCustomBidStrategy62;

    @ExcelProperty(value = {"USERDEFINED63", "客户定制内容63"}, index = 182)
    @ApiModelProperty(value = "客户定制内容63")
    private String supportCustomBidStrategy63;

    @ExcelProperty(value = {"USERDEFINED64", "客户定制内容64"}, index = 183)
    @ApiModelProperty(value = "客户定制内容64")
    private String supportCustomBidStrategy64;

    @ExcelProperty(value = {"USERDEFINED65", "客户定制内容65"}, index = 184)
    @ApiModelProperty(value = "客户定制内容65")
    private String supportCustomBidStrategy65;

    @ExcelProperty(value = {"USERDEFINED66", "客户定制内容66"}, index = 185)
    @ApiModelProperty(value = "客户定制内容66")
    private String supportCustomBidStrategy66;

    @ExcelProperty(value = {"USERDEFINED67", "客户定制内容67"}, index = 186)
    @ApiModelProperty(value = "客户定制内容67")
    private String supportCustomBidStrategy67;

    @ExcelProperty(value = {"USERDEFINED68", "客户定制内容68"}, index = 187)
    @ApiModelProperty(value = "客户定制内容68")
    private String supportCustomBidStrategy68;

    @ExcelProperty(value = {"USERDEFINED69", "客户定制内容69"}, index = 188)
    @ApiModelProperty(value = "客户定制内容69")
    private String supportCustomBidStrategy69;

    @ExcelProperty(value = {"USERDEFINED70", "客户定制内容70"}, index = 189)
    @ApiModelProperty(value = "客户定制内容70")
    private String supportCustomBidStrategy70;

    @ExcelProperty(value = {"USERDEFINED71", "客户定制内容71"}, index = 190)
    @ApiModelProperty(value = "客户定制内容71")
    private String supportCustomBidStrategy71;

    @ExcelProperty(value = {"USERDEFINED72", "客户定制内容72"}, index = 191)
    @ApiModelProperty(value = "客户定制内容72")
    private String supportCustomBidStrategy72;

    @ExcelProperty(value = {"USERDEFINED73", "客户定制内容73"}, index = 192)
    @ApiModelProperty(value = "客户定制内容73")
    private String supportCustomBidStrategy73;

    @ExcelProperty(value = {"USERDEFINED74", "客户定制内容74"}, index = 193)
    @ApiModelProperty(value = "客户定制内容74")
    private String supportCustomBidStrategy74;

    @ExcelProperty(value = {"USERDEFINED75", "客户定制内容75"}, index = 194)
    @ApiModelProperty(value = "客户定制内容75")
    private String supportCustomBidStrategy75;

    @ExcelProperty(value = {"USERDEFINED76", "客户定制内容76"}, index = 195)
    @ApiModelProperty(value = "客户定制内容76")
    private String supportCustomBidStrategy76;

    @ExcelProperty(value = {"USERDEFINED77", "客户定制内容77"}, index = 196)
    @ApiModelProperty(value = "客户定制内容77")
    private String supportCustomBidStrategy77;

    @ExcelProperty(value = {"USERDEFINED78", "客户定制内容78"}, index = 197)
    @ApiModelProperty(value = "客户定制内容78")
    private String supportCustomBidStrategy78;

    @ExcelProperty(value = {"USERDEFINED79", "客户定制内容79"}, index = 198)
    @ApiModelProperty(value = "客户定制内容79")
    private String supportCustomBidStrategy79;

    @ExcelProperty(value = {"USERDEFINED80", "客户定制内容80"}, index = 199)
    @ApiModelProperty(value = "客户定制内容80")
    private String supportCustomBidStrategy80;

    @ExcelProperty(value = {"USERDEFINED81", "客户定制内容81"}, index = 200)
    @ApiModelProperty(value = "客户定制内容81")
    private String supportCustomBidStrategy81;

    @ExcelProperty(value = {"USERDEFINED82", "客户定制内容82"}, index = 201)
    @ApiModelProperty(value = "客户定制内容82")
    private String supportCustomBidStrategy82;

    @ExcelProperty(value = {"USERDEFINED83", "客户定制内容83"}, index = 202)
    @ApiModelProperty(value = "客户定制内容83")
    private String supportCustomBidStrategy83;

    @ExcelProperty(value = {"USERDEFINED84", "客户定制内容84"}, index = 203)
    @ApiModelProperty(value = "客户定制内容84")
    private String supportCustomBidStrategy84;

    @ExcelProperty(value = {"USERDEFINED85", "客户定制内容85"}, index = 204)
    @ApiModelProperty(value = "客户定制内容85")
    private String supportCustomBidStrategy85;

    @ExcelProperty(value = {"USERDEFINED86", "客户定制内容86"}, index = 205)
    @ApiModelProperty(value = "客户定制内容86")
    private String supportCustomBidStrategy86;

    @ExcelProperty(value = {"USERDEFINED87", "客户定制内容87"}, index = 206)
    @ApiModelProperty(value = "客户定制内容87")
    private String supportCustomBidStrategy87;

    @ExcelProperty(value = {"USERDEFINED88", "客户定制内容88"}, index = 207)
    @ApiModelProperty(value = "客户定制内容88")
    private String supportCustomBidStrategy88;

    @ExcelProperty(value = {"USERDEFINED89", "客户定制内容89"}, index = 208)
    @ApiModelProperty(value = "客户定制内容89")
    private String supportCustomBidStrategy89;

    @ExcelProperty(value = {"USERDEFINED90", "客户定制内容90"}, index = 209)
    @ApiModelProperty(value = "客户定制内容90")
    private String supportCustomBidStrategy90;

    @ExcelProperty(value = {"USERDEFINED91", "客户定制内容91"}, index = 210)
    @ApiModelProperty(value = "客户定制内容91")
    private String supportCustomBidStrategy91;

    @ExcelProperty(value = {"USERDEFINED92", "客户定制内容92"}, index = 211)
    @ApiModelProperty(value = "客户定制内容92")
    private String supportCustomBidStrategy92;

    @ExcelProperty(value = {"USERDEFINED93", "客户定制内容93"}, index = 212)
    @ApiModelProperty(value = "客户定制内容93")
    private String supportCustomBidStrategy93;

    @ExcelProperty(value = {"USERDEFINED94", "客户定制内容94"}, index = 213)
    @ApiModelProperty(value = "客户定制内容94")
    private String supportCustomBidStrategy94;

    @ExcelProperty(value = {"USERDEFINED95", "客户定制内容95"}, index = 214)
    @ApiModelProperty(value = "客户定制内容95")
    private String supportCustomBidStrategy95;

    @ExcelProperty(value = {"USERDEFINED96", "客户定制内容96"}, index = 215)
    @ApiModelProperty(value = "客户定制内容96")
    private String supportCustomBidStrategy96;

    @ExcelProperty(value = {"USERDEFINED97", "客户定制内容97"}, index = 216)
    @ApiModelProperty(value = "客户定制内容97")
    private String supportCustomBidStrategy97;

    @ExcelProperty(value = {"USERDEFINED98", "客户定制内容98"}, index = 217)
    @ApiModelProperty(value = "客户定制内容98")
    private String supportCustomBidStrategy98;

    @ExcelProperty(value = {"USERDEFINED99", "客户定制内容99"}, index = 218)
    @ApiModelProperty(value = "客户定制内容99")
    private String supportCustomBidStrategy99;

    @ExcelProperty(value = {"USERDEFINED100", "客户定制内容100"}, index = 219)
    @ApiModelProperty(value = "客户定制内容100")
    private String supportCustomBidStrategy100;

    /**
     * 价格不适用时段1_开始
     */
    @ExcelProperty(value = {"BD1_START", "价格不适用时段1_开始"}, index = 220)
    @ApiModelProperty(value = "价格不适用时段1_开始")
    private String bd1Start;

    /**
     * 价格不适用时段1_结束
     */
    @ExcelProperty(value = {"BD1_END", "价格不适用时段1_结束"}, index = 221)
    @ApiModelProperty(value = "价格不适用时段1_结束")
    private String bd1End;

    /**
     * 价格不适用时段1_名称
     */
    @ExcelProperty(value = {"BD1_NAME", "价格不适用时段1_名称"}, index = 222)
    @ApiModelProperty(value = "价格不适用时段1_名称")
    private String bd1Name;

    /**
     * BD2开始时间
     */
    @ExcelProperty(value = {"BD2_START", "价格不适用时段2_开始"}, index = 223)
    @ApiModelProperty(value = "价格不适用时段2_开始")
    private String bd2Start;

    /**
     * BD2结束时间
     */
    @ExcelProperty(value = {"BD2_END", "价格不适用时段2_结束"}, index = 224)
    @ApiModelProperty(value = "价格不适用时段2_结束")
    private String bd2End;

    /**
     * BD2名称
     */
    @ExcelProperty(value = {"BD2_NAME", "价格不适用时段2_名称"}, index = 225)
    @ApiModelProperty(value = "价格不适用时段2_名称")
    private String bd2Name;

    /**
     * BD3开始时间
     */
    @ExcelProperty(value = {"BD3_START", "价格不适用时段3_开始"}, index = 226)
    @ApiModelProperty(value = "价格不适用时段3_开始")
    private String bd3Start;

    /**
     * BD3结束时间
     */
    @ExcelProperty(value = {"BD3_END", "价格不适用时段3_结束"}, index = 227)
    @ApiModelProperty(value = "价格不适用时段3_结束")
    private String bd3End;

    /**
     * BD3名称
     */
    @ExcelProperty(value = {"BD3_NAME", "价格不适用时段3_名称"}, index = 228)
    @ApiModelProperty(value = "价格不适用时段3_名称")
    private String bd3Name;

    /**
     * BD4开始时间
     */
    @ExcelProperty(value = {"BD4_START", "价格不适用时段4_开始"}, index = 229)
    @ApiModelProperty(value = "价格不适用时段4_开始")
    private String bd4Start;

    /**
     * BD4结束时间
     */
    @ExcelProperty(value = {"BD4_END", "价格不适用时段4_结束"}, index = 230)
    @ApiModelProperty(value = "价格不适用时段4_结束")
    private String bd4End;

    /**
     * BD4名称
     */
    @ExcelProperty(value = {"BD4_NAME", "价格不适用时段4_名称"}, index = 231)
    @ApiModelProperty(value = "价格不适用时段4_名称")
    private String bd4Name;

    /**
     * BD5开始时间
     */
    @ExcelProperty(value = {"BD5_START", "价格不适用时段5_开始"}, index = 232)
    @ApiModelProperty(value = "价格不适用时段5_开始")
    private String bd5Start;

    /**
     * BD5结束时间
     */
    @ExcelProperty(value = {"BD5_END", "价格不适用时段5_结束"}, index = 233)
    @ApiModelProperty(value = "价格不适用时段5_结束")
    private String bd5End;

    /**
     * BD5名称
     */
    @ExcelProperty(value = {"BD5_NAME", "价格不适用时段5_名称"}, index = 234)
    @ApiModelProperty(value = "价格不适用时段5_名称")
    private String bd5Name;

    /**
     * BD6开始时间
     */
    @ExcelProperty(value = {"BD6_START", "价格不适用时段6_开始"}, index = 235)
    @ApiModelProperty(value = "价格不适用时段6_开始")
    private String bd6Start;

    /**
     * BD6结束时间
     */
    @ExcelProperty(value = {"BD6_END", "价格不适用时段6_结束"}, index = 236)
    @ApiModelProperty(value = "价格不适用时段6_结束")
    private String bd6End;

    /**
     * BD6名称
     */
    @ExcelProperty(value = {"BD6_NAME", "价格不适用时段6_名称"}, index = 237)
    @ApiModelProperty(value = "价格不适用时段6_名称")
    private String bd6Name;

    /**
     * BD7开始时间
     */
    @ExcelProperty(value = {"BD7_START", "价格不适用时段7_开始"}, index = 238)
    @ApiModelProperty(value = "价格不适用时段7_开始")
    private String bd7Start;

    /**
     * BD7结束时间
     */
    @ExcelProperty(value = {"BD7_END", "价格不适用时段7_结束"}, index = 239)
    @ApiModelProperty(value = "价格不适用时段7_结束")
    private String bd7End;

    /**
     * BD7名称
     */
    @ExcelProperty(value = {"BD7_NAME", "价格不适用时段7_名称"}, index = 240)
    @ApiModelProperty(value = "价格不适用时段7_名称")
    private String bd7Name;

    /**
     * BD8开始时间
     */
    @ExcelProperty(value = {"BD8_START", "价格不适用时段8_开始"}, index = 241)
    @ApiModelProperty(value = "价格不适用时段8_开始")
    private String bd8Start;

    /**
     * BD8结束时间
     */
    @ExcelProperty(value = {"BD8_END", "价格不适用时段8_结束"}, index = 242)
    @ApiModelProperty(value = "价格不适用时段8_结束")
    private String bd8End;

    /**
     * BD8名称
     */
    @ExcelProperty(value = {"BD8_NAME", "价格不适用时段8_名称"}, index = 243)
    @ApiModelProperty(value = "价格不适用时段8_名称")
    private String bd8Name;

    /**
     * BD9开始时间
     */
    @ExcelProperty(value = {"BD9_START", "价格不适用时段9_开始"}, index = 244)
    @ApiModelProperty(value = "价格不适用时段9_开始")
    private String bd9Start;

    /**
     * BD9结束时间
     */
    @ExcelProperty(value = {"BD9_END", "价格不适用时段9_结束"}, index = 245)
    @ApiModelProperty(value = "价格不适用时段9_结束")
    private String bd9End;

    /**
     * BD9名称
     */
    @ExcelProperty(value = {"BD9_NAME", "价格不适用时段9_名称"}, index = 246)
    @ApiModelProperty(value = "价格不适用时段9_名称")
    private String bd9Name;

    /**
     * BD10开始时间
     */
    @ExcelProperty(value = {"BD10_START", "价格不适用时段10_开始"}, index = 247)
    @ApiModelProperty(value = "价格不适用时段10_开始")
    private String bd10Start;

    /**
     * BD10结束时间
     */
    @ExcelProperty(value = {"BD10_END", "价格不适用时段10_结束"}, index = 248)
    @ApiModelProperty(value = "价格不适用时段10_结束")
    private String bd10End;

    /**
     * BD10名称
     */
    @ExcelProperty(value = {"BD10_NAME", "价格不适用时段10_名称"}, index = 249)
    @ApiModelProperty(value = "价格不适用时段10_名称")
    private String bd10Name;

    /**
     * 干洗服务
     */
    @ExcelProperty(value = {"LAUN_DRY", "干洗服务"}, index = 250)
    @ApiModelProperty(value = "干洗服务")
    private String laundry;

    /**
     * 洗衣房
     */
    @ExcelProperty(value = {"LAUNDRY_SITE", "洗衣房"}, index = 251)
    @ApiModelProperty(value = "洗衣房")
    private String laundrySite;

    /**
     * Mini 吧
     */
    @ExcelProperty(value = {"MINI_FRIG", "Mini吧"}, index = 252)
    @ApiModelProperty(value = "Mini吧")
    private String miniFrig;

}
